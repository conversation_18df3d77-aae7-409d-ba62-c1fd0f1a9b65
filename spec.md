# 实现 Deployment 工作负载的复制创建功能

## 功能需求概述

在 CCE 控制台的 Deployment 类型工作负载列表页面中，为【更多】操作下拉菜单添加【复制创建】功能选项。

## 具体实现要求

### 1. UI 界面修改

-   **位置**：在 Deployment 列表页面的【更多】操作下拉菜单中，将【复制创建】选项放置在"标签注解"选项的下方
-   **显示条件**：仅在 deployment 类型的工作负载列表中显示此选项，statefulset 和 daemonset 类型列表中不显示
-   **交互行为**：用户点击后直接跳转到创建工作负载页面，无需额外确认步骤

### 2. 数据复制逻辑

实现选择性数据复制，具体规则如下：

#### 2.1 不复制的部分（保持创建页面默认状态）

-   **基本信息**：工作负载名称、命名空间、副本数等基础配置
-   **高级设置**：调度策略、节点亲和性、污点容忍等高级配置

#### 2.2 需要完整复制的部分（容器配置）

复制源工作负载的所有容器配置，包括：

-   容器名称
-   容器镜像地址和版本标签（Tag）
-   镜像拉取策略
-   容器资源配额（CPU、内存限制和请求）
-   加速卡资源申请和类型配置（GPU/NPU）
-   容器端口映射
-   环境变量配置
-   容器启动命令和参数
-   特权容器设置
-   初始化容器配置
-   健康检查配置（存活探针、就绪探针）
-   容器生命周期钩子
-   数据卷挂载配置
-   镜像仓库访问凭证

### 3. 技术实现指导

#### 3.1 涉及的关键文件

-   **列表页面**：`pages/workload/deployment2/index.js` （当前三种工作负载类型共用）
-   **创建页面**：`pages/workload/create/index.js`
-   **容器配置组件**：`components/cluster/workload/container-config.js` 或 `components/cluster/workload/container-hybrid-config.js`
-   **数据结构参考**：参考 `components/cluster/workload/data-flavor.js` 中的 `getContainerData` 和 `defaultCData` 来确定需要复制的具体数据字段

#### 3.2 实现步骤建议

1. 在列表页面添加复制创建的菜单项和点击处理逻辑
2. 实现数据提取函数，从选中的 deployment 中提取容器配置数据
3. 实现页面跳转逻辑，将提取的数据传递给创建页面
4. 在创建页面中处理接收到的复制数据，预填充相应的表单字段
5. 确保多容器场景下所有容器配置都能正确复制

#### 3.3 特殊注意事项

-   **多容器支持**：如果源 deployment 包含多个容器（包括初始化容器），需要确保所有容器的配置都能完整复制，不能有遗漏
-   **类型区分**：确保复制功能仅在 deployment 类型列表中可用
-   **数据完整性**：复制的容器配置必须包含所有相关参数，确保复制后的工作负载能够正常创建和运行
-   **API 兼容性**：需要考虑不同集群版本和区域的 API 兼容性

## 验收标准

1. 在 deployment 列表页面能看到复制创建选项，其他类型列表页面不显示
2. 点击复制创建后能正确跳转到创建页面
3. 创建页面中容器配置部分已预填充源工作负载的所有容器配置
4. 基本信息和高级设置保持默认状态
5. 支持多容器场景的完整复制
6. 复制后能成功创建新的工作负载

## 技术设计详细说明

### 架构概览

```mermaid
graph TD
    A[Deployment 列表页面] --> B[更多操作下拉菜单]
    B --> C[复制创建选项]
    C --> D[获取源 Deployment 详情]
    D --> E[提取容器配置数据]
    E --> F[跳转到创建页面]
    F --> G[预填充容器配置]
    G --> H[用户完成创建]
```

### 文件修改清单

#### 主要修改文件

-   `pages/workload/deployment2/index.js` - 添加复制创建功能
-   `pages/workload/create/index.js` - 支持预填充数据
-   `components/cluster/workload/container-config.js` - 支持复制模式

#### 涉及的 API 接口

-   `getAppDeploymentInfo` - 获取 Deployment 详情
-   `getAppYaml` - 获取 Deployment YAML 配置

### 数据流设计

#### 复制数据结构

```javascript
// 需要复制的容器配置数据结构
const containerConfigData = {
    containersData: [
        {
            name: string, // 容器名称
            image: string, // 容器镜像地址
            imageVersion: string, // 镜像版本Tag
            imagePullPolicy: string, // 镜像拉取策略
            cpuRequest: string, // CPU请求
            cpuLimit: string, // CPU限制
            memRequest: string, // 内存请求
            memLimit: string, // 内存限制
            GPUList: array, // GPU配置
            cardType: string, // 加速卡类型
            npuData: object, // NPU配置
            ports: array, // 容器端口
            env: array, // 环境变量
            containerStartUps: array, // 容器启动项
            securityContext: object, // 特权容器设置
            lifecycle: object, // 生命周期配置
            volumeData: array, // 数据卷挂载
            livenessProbe: object, // 存活探针
            readinessProbe: object, // 就绪探针
            // ... 其他容器配置
        },
    ],
    certificates: array, // 仓库访问凭证
    initContainers: array, // 初始化容器
};
```

#### URL 参数设计

```
/cce/application/workload/create?clusterUuid={uuid}&namespace={ns}&type=deployment&clusterName={name}&copyFrom={sourceName}&copyNamespace={sourceNs}
```

## 实现细节

### UI 层实现

#### 更多操作菜单修改

在 `pages/workload/deployment2/index.js` 中的 `moreOpt` 数组中添加复制创建选项：

```javascript
// 在 initData() 方法中修改 moreOpt 配置
moreOpt: [
    {
        text: '重启',
        value: 'reloadWorkload',
    },
    {
        text: '编辑YAML',
        value: 'editYaml',
    },
    {
        text: '标签注解',
        value: 'labelAnnotation',
    },
    {
        text: '复制创建', // 新增选项，放置在标签注解下方
        value: 'copyAndCreate', // 新增选项
    },
    {
        text: '日志',
        value: 'log',
    },
    // ... 其他选项
];
```

#### 显示逻辑控制

修改 `showAPM` 过滤器或新增过滤器来控制复制创建选项的显示：

```javascript
// 在 filters 中添加或修改显示逻辑
showCopyCreate(row, action) {
    const moduleName = this.data.get('moduleName');
    if (action.value === 'copyAndCreate') {
        // 只在 deployment 类型下显示，statefulset 和 daemonset 不显示
        return moduleName === 'deployment' ? 'block' : 'none';
    }
    return 'block';
}
```

### 3.2 数据获取与处理

#### 3.2.1 获取源 Deployment 数据

```javascript
// 新增方法：获取 Deployment 详情用于复制
async getDeploymentForCopy(row) {
    const {clusterUuid, namespaceName, name} = row;

    try {
        // 获取 Deployment 基本信息
        const result = await this.$http.getAppDeploymentInfo({
            clusterUuid,
            namespaceName,
            deploymentName: name,
        });

        // 获取 YAML 配置
        const yamlRes = await this.$http.getAppYaml('deployment', {
            clusterUuid,
            namespaceName,
            name,
        });

        return {
            detail: result.result,
            yamlContent: yamlRes.result
        };
    } catch (error) {
        Notification.error('获取工作负载信息失败');
        throw error;
    }
}
```

#### 3.2.2 提取容器配置数据

```javascript
// 新增方法：从 YAML 中提取容器配置
extractContainerConfig(yamlContent) {
    const containers = _.get(yamlContent, 'spec.template.spec.containers', []);
    const initContainers = _.get(yamlContent, 'spec.template.spec.initContainers', []);
    const volumes = _.get(yamlContent, 'spec.template.spec.volumes', []);
    const imagePullSecrets = _.get(yamlContent, 'spec.template.spec.imagePullSecrets', []);

    // 使用现有的 getContainerData 方法转换数据格式
    const containerData = getContainerData(containers, false, 0, null, volumes);
    const initContainerData = getContainerData(initContainers, true, containers.length, null, volumes);

    return {
        containers: containerData,
        initContainers: initContainerData,
        volumes: volumes,
        imagePullSecrets: imagePullSecrets
    };
}
```

### 3.3 导航与数据传递

#### 3.3.1 复制创建处理方法

```javascript
// 新增方法：处理复制创建操作
async copyAndCreate(row) {
    try {
        // 获取源 Deployment 数据
        const sourceData = await this.getDeploymentForCopy(row);

        // 提取容器配置
        const containerConfig = this.extractContainerConfig(sourceData.yamlContent);

        // 将配置数据存储到 sessionStorage 或通过 URL 参数传递
        const copyData = {
            containerConfig,
            sourceInfo: {
                name: row.name,
                namespace: row.namespaceName
            }
        };

        // 存储复制数据
        sessionStorage.setItem('workload-copy-data', JSON.stringify(copyData));

        // 跳转到创建页面
        const {clusterUuid, namespace, clusterName} = this.data.get();
        redirect(
            `#/cce/application/workload/create?clusterUuid=${clusterUuid}` +
            `&namespace=${namespace}&type=deployment&clusterName=${clusterName}` +
            `&copyFrom=${row.name}&copyNamespace=${row.namespaceName}`
        );
    } catch (error) {
        // 错误已在 getDeploymentForCopy 中处理
    }
}
```

### 3.4 创建页面适配

#### 3.4.1 检测复制模式

在 `pages/workload/create/index.js` 的 `attached` 方法中检测复制参数：

```javascript
attached() {
    const {clusterUuid, namespace, type, clusterName, copyFrom, copyNamespace} = this.data.get('route.query');

    // 现有逻辑
    this.data.set('clusterUuid', clusterUuid);
    this.data.set('namespace', namespace !== 'all' ? namespace : 'default');
    this.data.set('workloadType', type);
    this.data.set('clusterName', clusterName);

    // 新增：检测复制模式
    if (copyFrom && copyNamespace) {
        this.data.set('copyMode', true);
        this.data.set('copySource', {
            name: copyFrom,
            namespace: copyNamespace
        });
        this.loadCopyData();
    }

    const backTo = `/cce/application/${type}/list?clusterUuid=${clusterUuid}&namespaceName=${namespace}`;
    this.data.set('backTo', backTo);
    this.getK8sVersion();
}
```

#### 3.4.2 加载复制数据

```javascript
// 新增方法：加载复制数据
loadCopyData() {
    try {
        const copyDataStr = sessionStorage.getItem('workload-copy-data');
        if (copyDataStr) {
            const copyData = JSON.parse(copyDataStr);
            this.data.set('copyData', copyData);

            // 清理 sessionStorage
            sessionStorage.removeItem('workload-copy-data');
        }
    } catch (error) {
        console.error('加载复制数据失败:', error);
    }
}
```

### 3.5 容器配置组件适配

#### 3.5.1 支持复制模式初始化

在 `components/cluster/workload/container-config.js` 中添加复制模式支持：

```javascript
// 修改 createMode 方法，支持复制数据初始化
async createMode() {
    const detail = this.data.get('detail');
    if (_.isEmpty(detail)) {
        return;
    }

    this.getSecret();
    await this.getCapability();

    // 检查是否有复制数据
    const copyData = this.data.get('copyData');
    if (copyData && copyData.containerConfig) {
        await this.initFromCopyData(copyData.containerConfig);
    } else {
        // 原有逻辑：添加默认容器
        const containerList = this.data.get('containerList');
        if (!containerList.length) {
            cIndex = 1;
            this.addContainer();
        }
    }

    this.getGpuUnit();
}
```

#### 3.5.2 从复制数据初始化容器配置

```javascript
// 新增方法：从复制数据初始化容器配置
async initFromCopyData(containerConfig) {
    const {containers, initContainers, imagePullSecrets} = containerConfig;

    // 设置容器列表
    if (containers && containers.length > 0) {
        this.data.set('containerList', containers);
        this.data.set('containerSelected', 0);
        cIndex = containers.length;
    }

    // 设置初始化容器
    if (initContainers && initContainers.length > 0) {
        // 将初始化容器添加到对应的容器配置中
        containers.forEach((container, index) => {
            if (initContainers[index]) {
                container.initContainers = initContainers;
            }
        });
    }

    // 设置仓库凭证
    if (imagePullSecrets && imagePullSecrets.length > 0) {
        this.data.set('certificates', imagePullSecrets);
    }
}
```

## 4. 数据复制规则

### 4.1 需要复制的配置项

#### 4.1.1 容器基本配置

-   ✅ 容器名称 (`name`)
-   ✅ 容器镜像地址 (`image`)
-   ✅ 镜像版本 Tag (`imageVersion`)
-   ✅ 镜像拉取策略 (`imagePullPolicy`)

#### 4.1.2 资源配置

-   ✅ CPU 请求/限制 (`cpuRequest`, `cpuLimit`)
-   ✅ 内存请求/限制 (`memRequest`, `memLimit`)
-   ✅ GPU 配置 (`GPUList`, `GPUValue`, `GPUTypeValue`, `GPUNum`)
-   ✅ 加速卡配置 (`cardType`, `npuData`)

#### 4.1.3 网络配置

-   ✅ 容器端口 (`ports`)

#### 4.1.4 环境配置

-   ✅ 环境变量 (`env`)
-   ✅ 容器启动项 (`containerStartUps`)
-   ✅ 启动命令 (`command`)
-   ✅ 启动参数 (`args`)

#### 4.1.5 安全配置

-   ✅ 特权容器设置 (`securityContext`)

#### 4.1.6 高级配置

-   ✅ 初始化容器 (`initContainers`)
-   ✅ 健康检查 (`livenessProbe`, `readinessProbe`)
-   ✅ 生命周期配置 (`lifecycle`)
-   ✅ 数据卷挂载 (`volumeData`, `volumeMounts`)

#### 4.1.7 镜像配置

-   ✅ 仓库访问凭证 (`imagePullSecrets`)

### 4.2 不复制的配置项

#### 4.2.1 基本信息

-   ❌ 工作负载名称
-   ❌ 工作负载描述
-   ❌ 副本数量
-   ❌ 命名空间（使用当前选择的命名空间）

#### 4.2.2 高级设置

-   ❌ 标签和注解
-   ❌ 调度策略
-   ❌ 升级策略
-   ❌ Pod 安全策略
-   ❌ 自动伸缩配置

## 5. 错误处理

### 5.1 异常场景处理

#### 5.1.1 数据获取失败

```javascript
// 在 getDeploymentForCopy 方法中处理
catch (error) {
    if (error.status === 404) {
        Notification.error('工作负载不存在或已被删除');
    } else if (error.status === 403) {
        Notification.error('没有权限访问该工作负载');
    } else {
        Notification.error('获取工作负载信息失败，请稍后重试');
    }
    throw error;
}
```

#### 5.1.2 数据格式异常

```javascript
// 在 extractContainerConfig 方法中添加数据验证
extractContainerConfig(yamlContent) {
    if (!yamlContent || typeof yamlContent !== 'object') {
        throw new Error('无效的工作负载配置数据');
    }

    const containers = _.get(yamlContent, 'spec.template.spec.containers', []);
    if (!Array.isArray(containers) || containers.length === 0) {
        throw new Error('工作负载中没有找到有效的容器配置');
    }

    // ... 其他处理逻辑
}
```

#### 5.1.3 存储空间不足

```javascript
// 在存储复制数据时处理 sessionStorage 异常
try {
    sessionStorage.setItem('workload-copy-data', JSON.stringify(copyData));
} catch (error) {
    if (error.name === 'QuotaExceededError') {
        Notification.error('浏览器存储空间不足，请清理缓存后重试');
    } else {
        Notification.error('数据存储失败，请重试');
    }
    throw error;
}
```

### 5.2 用户体验优化

#### 5.2.1 加载状态提示

```javascript
async copyAndCreate(row) {
    // 显示加载状态
    this.data.set('copyLoading', true);

    try {
        // ... 复制逻辑
    } catch (error) {
        // ... 错误处理
    } finally {
        this.data.set('copyLoading', false);
    }
}
```

#### 5.2.2 操作确认

```javascript
async copyAndCreate(row) {
    await Dialog.confirm({
        content: `确定要基于工作负载 "${row.name}" 创建新的部署吗？`,
        title: '复制创建确认'
    });

    // ... 后续逻辑
}
```

## 6. 测试计划

### 6.1 功能测试

#### 6.1.1 基本功能测试

-   [ ] 验证复制创建选项仅在 Deployment 列表中显示
-   [ ] 验证 StatefulSet 和 DaemonSet 列表中不显示复制创建选项
-   [ ] 验证点击复制创建能正确跳转到创建页面
-   [ ] 验证容器配置数据能正确预填充

#### 6.1.2 数据完整性测试

-   [ ] 验证所有容器配置项都能正确复制
-   [ ] 验证多容器场景下的数据复制
-   [ ] 验证初始化容器的复制
-   [ ] 验证数据卷配置的复制
-   [ ] 验证环境变量的复制
-   [ ] 验证资源配置的复制

#### 6.1.3 边界条件测试

-   [ ] 测试复制不存在的工作负载
-   [ ] 测试复制权限不足的工作负载
-   [ ] 测试复制配置异常的工作负载
-   [ ] 测试网络异常情况下的复制操作

### 6.2 兼容性测试

#### 6.2.1 浏览器兼容性

-   [ ] Chrome 最新版本
-   [ ] Firefox 最新版本
-   [ ] Safari 最新版本
-   [ ] Edge 最新版本

#### 6.2.2 集群版本兼容性

-   [ ] Kubernetes 1.18+
-   [ ] 不同 CCE 集群版本

### 6.3 性能测试

#### 6.3.1 数据加载性能

-   [ ] 测试大型工作负载配置的复制性能
-   [ ] 测试多容器工作负载的复制性能
-   [ ] 测试复杂数据卷配置的复制性能

#### 6.3.2 内存使用测试

-   [ ] 测试复制数据的内存占用
-   [ ] 测试 sessionStorage 的使用情况

## 7. 部署计划

### 7.1 发布策略

-   采用灰度发布策略
-   先在测试环境验证功能完整性
-   逐步推广到生产环境

### 7.2 回滚计划

-   如发现严重问题，可通过配置开关快速禁用该功能
-   保留原有创建流程不受影响

### 7.3 监控指标

-   复制创建功能的使用频率
-   复制操作的成功率
-   用户反馈和问题报告

## 9. 关键代码实现示例

### 9.1 列表页面核心代码

#### 9.1.1 moreOpt 配置修改

```javascript
// pages/workload/deployment2/index.js - initData() 方法中
moreOpt: [
    {
        text: '重启',
        value: 'reloadWorkload',
    },
    {
        text: '编辑YAML',
        value: 'editYaml',
    },
    {
        text: '标签注解',
        value: 'labelAnnotation',
    },
    {
        text: '复制创建',
        value: 'copyAndCreate',
    },
    {
        text: '日志',
        value: 'log',
    },
    {
        text: 'APM控制台',
        value: 'apm',
    },
    {
        text: '删除',
        value: 'deleteWorkload',
    },
],
```

#### 9.1.2 显示控制逻辑

```javascript
// pages/workload/deployment2/index.js - filters 中修改 showAPM 方法
showAPM(row, action) {
    const moduleName = this.data.get('moduleName');
    const region = window.$context.getCurrentRegionId();

    if (action.value === 'apm') {
        if (
            row?.labels?.cceOnepilotEnable === 'on' &&
            ['deployment', 'statefulset'].includes(moduleName) &&
            region === 'bj'
        ) {
            return 'block';
        }
        return 'none';
    } else if (action.value === 'copyAndCreate') {
        // 仅在 deployment 类型中显示复制创建选项
        return moduleName === 'deployment' ? 'block' : 'none';
    } else {
        return 'block';
    }
},
```

#### 9.1.3 复制创建处理方法

```javascript
// pages/workload/deployment2/index.js - 新增方法
async copyAndCreate(row) {
    try {
        // 显示加载状态
        this.data.set('copyLoading', true);

        // 获取源 Deployment 数据
        const sourceData = await this.getDeploymentForCopy(row);

        // 提取容器配置数据
        const containerConfig = this.extractContainerConfig(sourceData.yamlContent);

        // 存储复制数据到 sessionStorage
        const copyData = {
            containerConfig,
            sourceInfo: {
                name: row.name,
                namespace: row.namespaceName,
                clusterUuid: row.clusterUuid
            }
        };

        sessionStorage.setItem('workload-copy-data', JSON.stringify(copyData));

        // 跳转到创建页面
        const {clusterUuid, namespace, clusterName} = this.data.get();
        redirect(
            `#/cce/application/workload/create?clusterUuid=${clusterUuid}` +
            `&namespace=${namespace}&type=deployment&clusterName=${clusterName}` +
            `&copyFrom=${row.name}&copyNamespace=${row.namespaceName}`
        );
    } catch (error) {
        console.error('复制创建失败:', error);
    } finally {
        this.data.set('copyLoading', false);
    }
},

async getDeploymentForCopy(row) {
    const {clusterUuid, namespaceName, name} = row;

    try {
        // 获取 Deployment 基本信息
        const result = await this.$http.getAppDeploymentInfo({
            clusterUuid,
            namespaceName,
            deploymentName: name,
        });

        // 获取 YAML 配置
        const yamlRes = await this.$http.getAppYaml('deployment', {
            clusterUuid,
            namespaceName,
            name,
        });

        return {
            detail: result,
            yamlContent: yamlRes.result
        };
    } catch (error) {
        if (error.status === 404) {
            Notification.error('工作负载不存在或已被删除');
        } else if (error.status === 403) {
            Notification.error('没有权限访问该工作负载');
        } else {
            Notification.error('获取工作负载信息失败，请稍后重试');
        }
        throw error;
    }
},

extractContainerConfig(yamlContent) {
    if (!yamlContent || typeof yamlContent !== 'object') {
        throw new Error('无效的工作负载配置数据');
    }

    const containers = _.get(yamlContent, 'spec.template.spec.containers', []);
    const initContainers = _.get(yamlContent, 'spec.template.spec.initContainers', []);
    const volumes = _.get(yamlContent, 'spec.template.spec.volumes', []);
    const imagePullSecrets = _.get(yamlContent, 'spec.template.spec.imagePullSecrets', []);

    if (!Array.isArray(containers) || containers.length === 0) {
        throw new Error('工作负载中没有找到有效的容器配置');
    }

    return {
        containers,
        initContainers,
        volumes,
        imagePullSecrets
    };
}
```

### 9.2 创建页面核心代码

#### 9.2.1 路由参数处理

```javascript
// pages/workload/create/index.js - attached() 方法修改
attached() {
    const {clusterUuid, namespace, type, clusterName, copyFrom, copyNamespace} = this.data.get('route.query');

    this.data.set('clusterUuid', clusterUuid);
    this.data.set('namespace', namespace !== 'all' ? namespace : 'default');
    this.data.set('workloadType', type);
    this.data.set('clusterName', clusterName);

    // 检测复制模式
    if (copyFrom && copyNamespace) {
        this.data.set('copyMode', true);
        this.data.set('copySource', {
            name: copyFrom,
            namespace: copyNamespace
        });
        this.loadCopyData();
    }

    const backTo = `/cce/application/${type}/list?clusterUuid=${clusterUuid}&namespaceName=${namespace}`;
    this.data.set('backTo', backTo);
    this.getK8sVersion();
},

// 新增方法：加载复制数据
loadCopyData() {
    try {
        const copyDataStr = sessionStorage.getItem('workload-copy-data');
        if (copyDataStr) {
            const copyData = JSON.parse(copyDataStr);
            this.data.set('copyData', copyData);

            // 清理 sessionStorage
            sessionStorage.removeItem('workload-copy-data');

            // 显示复制来源提示
            const sourceInfo = copyData.sourceInfo;
            if (sourceInfo) {
                Notification.info(`正在基于工作负载 "${sourceInfo.name}" 创建新部署`);
            }
        }
    } catch (error) {
        console.error('加载复制数据失败:', error);
        Notification.error('加载复制数据失败，将使用默认配置');
    }
}
```

#### 9.2.2 模板修改

```javascript
// pages/workload/create/index.js - 模板中容器配置组件修改
<div style="display: {{steps.current === 1 ? 'block' : 'none'}}">
    <container-hybrid-config
        class="mt20"
        detail="{{clusterInfo}}"
        clusterUuid="{{clusterUuid}}"
        s-ref="{{steps.datasource[1].ref}}"
        copyData="{{copyData}}"
        copyMode="{{copyMode}}"
    />
</div>
```

### 9.3 容器配置组件核心代码

#### 9.3.1 复制模式初始化

```javascript
// components/cluster/workload/container-config.js 或 container-hybrid-config.js
// 修改 createMode 方法
async createMode() {
    const detail = this.data.get('detail');
    if (_.isEmpty(detail)) {
        return;
    }

    this.getSecret();
    await this.getCapability();

    // 检查是否有复制数据
    const copyData = this.data.get('copyData');
    const copyMode = this.data.get('copyMode');

    if (copyMode && copyData && copyData.containerConfig) {
        await this.initFromCopyData(copyData.containerConfig);
    } else {
        // 原有逻辑：添加默认容器
        const containerList = this.data.get('containerList');
        if (!containerList.length) {
            cIndex = 1;
            this.addContainer();
        }
    }

    this.getGpuUnit();
},

// 新增方法：从复制数据初始化容器配置
async initFromCopyData(containerConfig) {
    const {containers, initContainers, volumes, imagePullSecrets} = containerConfig;
    const npuDatasource = this.data.get('npuDatasource');

    try {
        // 使用现有的 getContainerData 方法转换数据格式
        const containerList = [
            ...getContainerData(containers, false, 0, npuDatasource, volumes),
            ...getContainerData(initContainers, true, containers.length, npuDatasource, volumes),
        ];

        // 设置容器列表
        this.data.set('containerList', containerList);
        this.data.set(
            'containerTabList',
            _.map(containerList, item => ({name: item.name, value: item.name}))
        );

        // 设置仓库凭证
        if (imagePullSecrets && imagePullSecrets.length > 0) {
            this.data.set('certificates', imagePullSecrets);
        }

        // 设置当前选中的容器
        if (containerList.length > 0) {
            this.data.set('containerSelected', 0);
            cIndex = containerList.length + 1;
        }

        console.log('容器配置复制成功，共复制', containerList.length, '个容器');
    } catch (error) {
        console.error('初始化复制数据失败:', error);
        Notification.error('容器配置复制失败，将使用默认配置');

        // 降级到默认模式
        cIndex = 1;
        this.addContainer();
    }
}
```

## 10. 数据结构详细说明

### 10.1 复制数据结构定义

```typescript
interface CopyData {
    containerConfig: {
        containers: ContainerConfig[];
        initContainers: ContainerConfig[];
        volumes: VolumeConfig[];
        imagePullSecrets: ImagePullSecret[];
    };
    sourceInfo: {
        name: string;
        namespace: string;
        clusterUuid: string;
    };
}

interface ContainerConfig {
    name: string;
    image: string;
    imageVersion: string;
    imagePullPolicy: string;
    cpuRequest: string;
    cpuLimit: string;
    memRequest: string;
    memLimit: string;
    ports: PortConfig[];
    env: EnvConfig[];
    volumeMounts: VolumeMountConfig[];
    securityContext: SecurityContextConfig;
    lifecycle: LifecycleConfig;
    livenessProbe: ProbeConfig;
    readinessProbe: ProbeConfig;
    // GPU/NPU 相关配置
    GPUList: any[];
    cardType: string;
    npuData: NPUConfig;
}
```

### 10.2 关键字段映射表

| YAML 字段路径                                               | 组件数据字段            | 数据类型 | 说明           |
| ----------------------------------------------------------- | ----------------------- | -------- | -------------- |
| `spec.template.spec.containers[].name`                      | `name`                  | string   | 容器名称       |
| `spec.template.spec.containers[].image`                     | `image`, `imageVersion` | string   | 镜像地址和版本 |
| `spec.template.spec.containers[].imagePullPolicy`           | `imagePullPolicy`       | string   | 镜像拉取策略   |
| `spec.template.spec.containers[].resources.requests.cpu`    | `cpuRequest`            | string   | CPU 请求       |
| `spec.template.spec.containers[].resources.limits.cpu`      | `cpuLimit`              | string   | CPU 限制       |
| `spec.template.spec.containers[].resources.requests.memory` | `memRequest`            | string   | 内存请求       |
| `spec.template.spec.containers[].resources.limits.memory`   | `memLimit`              | string   | 内存限制       |
| `spec.template.spec.containers[].ports`                     | `ports`                 | array    | 容器端口配置   |
| `spec.template.spec.containers[].env`                       | `env`                   | array    | 环境变量       |
| `spec.template.spec.containers[].volumeMounts`              | `volumeMounts`          | array    | 数据卷挂载     |
| `spec.template.spec.containers[].securityContext`           | `securityContext`       | object   | 安全上下文     |
| `spec.template.spec.containers[].lifecycle`                 | `lifecycle`             | object   | 生命周期配置   |
| `spec.template.spec.containers[].livenessProbe`             | `livenessProbe`         | object   | 存活探针       |
| `spec.template.spec.containers[].readinessProbe`            | `readinessProbe`        | object   | 就绪探针       |
| `spec.template.spec.initContainers`                         | `initContainers`        | array    | 初始化容器     |
| `spec.template.spec.volumes`                                | `volumes`               | array    | 数据卷定义     |
| `spec.template.spec.imagePullSecrets`                       | `certificates`          | array    | 镜像拉取凭证   |

## 11. 风险评估与缓解措施

### 11.1 技术风险

#### 11.1.1 数据丢失风险

-   **风险**：sessionStorage 数据可能因浏览器刷新或异常而丢失
-   **缓解措施**：添加数据持久化备份机制，提供重新获取数据的能力

#### 11.1.2 兼容性风险

-   **风险**：不同版本的 Kubernetes 集群可能有配置差异
-   **缓解措施**：添加版本检测和配置适配逻辑

#### 11.1.3 性能风险

-   **风险**：大型工作负载配置可能导致页面加载缓慢
-   **缓解措施**：添加加载状态提示，优化数据传输方式

### 11.2 用户体验风险

#### 11.2.1 操作混淆风险

-   **风险**：用户可能不清楚哪些配置被复制，哪些需要重新设置
-   **缓解措施**：添加复制来源提示，提供配置对比功能

#### 11.2.2 数据安全风险

-   **风险**：敏感信息可能在复制过程中泄露
-   **缓解措施**：过滤敏感字段，使用安全的数据传输方式

### 11.3 业务风险

#### 11.3.1 功能冲突风险

-   **风险**：新功能可能与现有功能产生冲突
-   **缓解措施**：充分的回归测试，渐进式发布

#### 11.3.2 维护成本风险

-   **风险**：新功能增加了系统复杂度和维护成本
-   **缓解措施**：完善的文档和测试用例，模块化设计

## 12. 总结

本规格说明详细描述了 Deployment 工作负载复制创建功能的完整实现方案，包括：

1. **功能范围明确**：仅支持 Deployment 类型，复制容器配置，不复制基本信息和高级设置
2. **技术方案完整**：涵盖前端 UI 修改、数据获取处理、页面跳转、组件适配等各个环节
3. **实现细节具体**：提供了关键代码示例和数据结构定义
4. **质量保证充分**：包含详细的测试计划和风险评估
5. **可扩展性良好**：为后续功能扩展预留了空间

该功能的实现将显著提升用户在创建相似工作负载时的效率，减少重复配置工作，提升 CCE 控制台的用户体验。
