apiVersion: kubeflow.org/v1
kind: DeepSpeedJob
metadata:
  annotations:
    infra.shiyak.com/job-name: test_clone4
    infra.shiyak.com/job-owner: zuti.he
    infra.shiyak.com/job-type: deepspeed 
  labels:
    infra.shiyak.com/job-id: job-nfc9y8fjev
    infra.shiyak.com/owner: zuti.he
    infra.shiyak.com/project-id: infra
  name: job-nfc9y8fjev
spec:
  replicaSpecs:
    launcher:
      replicas: 1
      restartPolicy: Never
      template:
        metadata:
          labels:
            infra.shiyak.com/job-id: job-nfc9y8fjev
            infra.shiyak.com/owner: zuti.he
            infra.shiyak.com/project-id: infra
        spec:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: nvidia.com/gpu.product
                    operator: In
                    values:
                    - NVIDIA-H20Z
          containers:
          - command:
            - /bin/bash
            - -c
            - |-
              echo Bootstrap launcher ...
              if [ ! -x /usr/bin/pdsh ]; then
                  echo /usr/bin/pdsh not found, installing ...
                  DEBIAN_FRONTEND=noninteractive apt-get -y -q update > /dev/null
                  DEBIAN_FRONTEND=noninteractive apt-get -y install openssh-client pdsh > /dev/null
                  chown root /usr/lib/
              fi
              echo Execute user command ...
              sleep inf
            env:
            - name: NVIDIA_VISIBLE_DEVICES
              value: void
            image: registry.baidubce.com/ai-native-dev/aixl:latest
            imagePullPolicy: Always
            name: deepspeed
            resources:
              limits:
                cpu: "2"
                memory: 4Gi
    worker:
      replicas: 337
      restartPolicy: Never
      template:
        metadata:
          annotations:
            k8s.v1.cni.cncf.io/networks: network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca
          labels:
            infra.shiyak.com/job-id: job-nfc9y8fjev
            infra.shiyak.com/owner: zuti.he
            infra.shiyak.com/project-id: infra
        spec:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: nvidia.com/gpu.product
                    operator: In
                    values:
                    - NVIDIA-H20Z
          containers:
          - command:
            - /bin/bash
            - -c
            - |
              echo Bootstrap worker ...
              if [ ! -x /usr/sbin/sshd ]; then
                  echo /usr/sbin/sshd not found, installing ...
                  DEBIAN_FRONTEND=noninteractive apt-get -y update > /dev/null
                  DEBIAN_FRONTEND=noninteractive apt-get -y install openssh-server > /dev/null
                  mkdir -p /run/sshd
              fi
              echo Launch sshd ...
              exec -a /usr/sbin/sshd /usr/sbin/sshd -D -e
            image: registry.baidubce.com/ai-native-dev/aixl:latest
            imagePullPolicy: Always
            name: deepspeed
            resources:
              limits:
                nvidia.com/gpu: "8"
                rdma/hca: "8"
              requests:
                nvidia.com/gpu: "8"
                rdma/hca: "8"
            securityContext:
              capabilities:
                add:
                - IPC_LOCK
                - SYS_NICE
            volumeMounts:
            - mountPath: /dev/shm
              name: dshm
          volumes:
          - emptyDir:
              medium: Memory
              sizeLimit: 1Ti
            name: dshm
  runPolicy:
    cleanPodPolicy: None
    schedulingPolicy: {}
    suspend: false
  slotsPerWorker: 8