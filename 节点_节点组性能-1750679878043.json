{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 481, "iteration": *************, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 44, "panels": [], "title": "概览", "type": "row"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [], "max": 1, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 0.99}, {"color": "green", "value": 1}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 0, "y": 1}, "id": 30, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(cce_instance_phase{clusterID=~\"$clusterID\",accountID=~\"$accountID\",phase!~\"create_failed|deleta_failed|upgrade_failed|unknown\",region=~\"$region\"}) by ()/sum(cce_instance_phase{clusterID=~\"$clusterID\",accountID=~\"$accountID\",region=~\"$region\"}) by ()", "format": "time_series", "instant": false, "interval": "", "legendFormat": "{{region}}", "queryType": "randomWalk", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "实例健康度", "transformations": [], "type": "stat"}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "byRegexp", "options": ".*Latency.*(AVG).*"}, "properties": [{"id": "unit", "value": "s"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.5}, {"color": "red", "value": 1}]}}, {"id": "decimals"}]}, {"matcher": {"id": "byRegexp", "options": ".*Rate.*"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 0.999}]}}, {"id": "decimals", "value": 5}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "control_plane"}, "properties": [{"id": "links", "value": [{"targetBlank": true, "title": "查看${__data.fields.control_plane}日志", "url": "https://cce-grafana.baidu-int.com/explore?orgId=1&left=%5B%22now-1h%22,%22now%22,%22cce-loki-cluster%22,%7B%22expr%22:%22%7Barea%3D%5C%22cce-meta%5C%22,%20region%3D%5C%22bj%5C%22,%20service%3D%5C%22${__data.fields.control_plane}%5C%22%7D%22,%22queryType%22:%22randomWalk%22,%22datasource%22:%22cce-loki-cluster%22%7D%5D"}]}]}, {"matcher": {"id": "byRegexp", "options": ".*QPS.*"}, "properties": [{"id": "unit", "value": "cps"}, {"id": "decimals"}]}, {"matcher": {"id": "byRegexp", "options": ".*Latency.*(MAX).*"}, "properties": [{"id": "unit", "value": "s"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 1}, {"color": "red", "value": 5}]}}]}, {"matcher": {"id": "byRegexp", "options": ".*Latency.*(P..)|(AVG).*"}, "properties": [{"id": "unit", "value": "s"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 1}, {"color": "red", "value": 2.5}]}}, {"id": "mappings", "value": [{"from": "", "id": 1, "text": "≥2.56 s", "to": "", "type": 1, "value": "2.56"}]}]}, {"matcher": {"id": "byRegexp", "options": ".*(Requests|Instances|Tasks)"}, "properties": [{"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "TotalNon2xxRequests"}, "properties": [{"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "TotalInstances"}, "properties": [{"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "yellow", "value": null}, {"color": "green", "value": 1}]}}]}, {"matcher": {"id": "byRegexp", "options": ".*异常.*"}, "properties": [{"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}]}]}, "gridPos": {"h": 8, "w": 20, "x": 4, "y": 1}, "id": 17, "maxDataPoints": 1448, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "value_and_name"}, "pluginVersion": "7.5.17", "repeat": null, "repeatDirection": "v", "targets": [{"exemplar": true, "expr": "sum(cce_instance_phase{clusterID=~\"$clusterID\",accountID=~\"$accountID\",region=~\"$region\"}) by ()", "format": "table", "hide": false, "instant": false, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "TotalInstances"}, {"exemplar": true, "expr": "sum(cce_instance_phase{clusterID=~\"$clusterID\",accountID=~\"$accountID\",phase=~\"create_failed|deleta_failed|upgrade_failed|unknown\",region=~\"$region\"}) by ()", "format": "table", "hide": false, "instant": false, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "FailedInstances"}, {"exemplar": true, "expr": "sum(cce_task_phase{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) by ()", "format": "table", "hide": false, "instant": false, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "TotalTasks"}, {"exemplar": true, "expr": "sum(cce_task_phase{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\", phase=~\"Aborted\"}) by ()", "format": "table", "hide": false, "instant": false, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "FailedTasks"}, {"exemplar": true, "expr": "count(sum(cce_instance_phase{clusterID=~\"$clusterID\",accountID=~\"$accountID\",phase=~\"create_failed|deleta_failed|upgrade_failed|unknown\",region=~\"$region\"}) by (clusterID) > 0)", "format": "table", "hide": false, "instant": false, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "count(sum(cce_instance_phase{clusterID=~\"$clusterID\",accountID=~\"$accountID\",phase=~\"create_failed|deleta_failed|upgrade_failed|unknown\",region=~\"$region\"}) by (accountID) > 0)", "format": "table", "hide": false, "instant": false, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "实例/变更任务状态", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Total5xxRequests": false, "Value #CreateInstance-QPS-MAX[1d]": true, "Value #TaskLatency-AVG": true, "Value #TaskLatency-MAX": true, "Value #Total5xxRequests": true, "Value #TotalRequests": false, "Value #TotalRequests - Total5xxRequests": true}, "indexByName": {"Time": 0, "Value #A": 5, "Value #B": 6, "Value #FailedInstances": 3, "Value #FailedTasks": 4, "Value #TotalInstances": 1, "Value #TotalTasks": 2}, "renameByName": {"Total5xxRequests": "FailedInstances", "Value #A": "有异常实例的集群数", "Value #B": "有异常实例的用户数", "Value #CreateInstance-QPS-MAX[1d]": "", "Value #FailedInstances": "异常实例数", "Value #FailedTasks": "异常变更任务数", "Value #Latency-AVG": "TaskLatency-AVG", "Value #Latency-MAX": "TaskLatency-MAX", "Value #Latency-P100": "Latency-P100(2.56s)", "Value #Latency-P99": "Latency-P99(2.56s)", "Value #QPS-AVG": "QPS-AVG", "Value #QPS-MAX": "QPS-MAX", "Value #SuccessRate": "SuccessRate", "Value #TaskLatency-AVG": "TaskLatency-AVG", "Value #TaskLatency-MAX": "任务最大延迟-MAX", "Value #Total5xxRequests": "FailedInstances", "Value #TotalInstances": "总实例数", "Value #TotalNon2xxRequests": "TotalNon2xxRequests", "Value #TotalRequests": "TotalInstances", "Value #TotalRequests - Total5xxRequests / Value #TotalRequests": "NormalRate(Non-5xx)", "Value #TotalTasks": "总变更任务数", "accountID": "", "clusterID": "", "region": ""}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": true, "field": "FailedInstances"}]}}], "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "hiddenSeries": false, "id": 18, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(cce_instance_phase{clusterID=~\"$clusterID\",accountID=~\"$accountID\",phase!~\"create_failed|deleta_failed|upgrade_failed|unknown\",region=~\"$region\"}) by (region)/sum(cce_instance_phase{clusterID=~\"$clusterID\",accountID=~\"$accountID\",region=~\"$region\"}) by (region)", "format": "time_series", "instant": false, "interval": "", "legendFormat": "{{region}} {{clusterID}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [{"$$hashKey": "object:184", "colorMode": "warning", "fill": false, "line": true, "op": "lt", "value": 1, "yaxis": "left"}, {"$$hashKey": "object:3101", "colorMode": "critical", "fill": true, "line": true, "op": "lt", "value": 0.99, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "实例健康度", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:152", "decimals": 2, "format": "percentunit", "label": null, "logBase": 1, "max": "1", "min": null, "show": true}, {"$$hashKey": "object:153", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "hiddenSeries": false, "id": 13, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "delta(count(cce_instance_create_time{clusterID=~\"$clusterID\",accountID=~\"$accountID\",region=~\"$region\"})[$__rate_interval])", "format": "time_series", "instant": false, "interval": "", "legendFormat": "新增/删除实例数", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "rate(sum(cce_instance_phase{clusterID=~\"$clusterID\",accountID=~\"$accountID\",phase=~\"create_failed|deleta_failed|upgrade_failed|unknown\",region=~\"$region\"})[$__rate_interval])", "hide": false, "instant": false, "interval": "", "legendFormat": "新增异常实例数量", "refId": "B"}, {"exemplar": true, "expr": "rate(sum(cce_instance_phase{clusterID=~\"$clusterID\",accountID=~\"$accountID\",phase!~\"create_failed|deleta_failed|upgrade_failed|unknown\",region=~\"$region\"})[$__rate_interval])", "hide": false, "instant": false, "interval": "", "legendFormat": "新增正常实例数量", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "实例增量", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:152", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:153", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 17}, "hiddenSeries": false, "id": 101, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "delta(count(cce_task_create_time{clusterID=~\"$clusterID\",accountID=~\"$accountID\",region=~\"$region\"})[$__rate_interval])", "format": "time_series", "instant": false, "interval": "", "legendFormat": "新增/回收任务数", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "rate(sum(cce_task_phase{clusterID=~\"$clusterID\",accountID=~\"$accountID\",phase=~\"Aborted\",region=~\"$region\"})[$__rate_interval])", "hide": false, "instant": false, "interval": "", "legendFormat": "新增异常任务数", "refId": "B"}, {"exemplar": true, "expr": "rate(sum(cce_task_phase{clusterID=~\"$clusterID\",accountID=~\"$accountID\",phase!~\"Aborted\",region=~\"$region\"})[$__rate_interval])", "hide": false, "instant": false, "interval": "", "legendFormat": "新增正常任务数", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "任务增量", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:152", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:153", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 17}, "hiddenSeries": false, "id": 69, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "实例耗时（所有未删除的实例）", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "clocks"}]}, {"matcher": {"id": "byRegexp", "options": ".*耗时.*"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 25}, "hiddenSeries": false, "id": 52, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_task_finishTime{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\"} - cce_task_create_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "任务耗时 MAX", "refId": "总耗时"}, {"exemplar": true, "expr": "1000*max(cce_task_create_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\"}) by (name)", "format": "table", "hide": true, "instant": true, "interval": "", "legendFormat": "", "refId": "开始时间"}, {"exemplar": true, "expr": "1000*max(cce_task_finishTime{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\"}) by (name)", "format": "table", "hide": true, "instant": true, "interval": "", "legendFormat": "", "refId": "结束时间"}, {"exemplar": true, "expr": "quantile(0.99, cce_task_finishTime{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\"} - cce_task_create_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "任务耗时 P99", "refId": "A"}, {"exemplar": true, "expr": "quantile(0.9, cce_task_finishTime{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\"} - cce_task_create_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "任务耗时 P90", "refId": "B"}, {"exemplar": true, "expr": "avg( cce_task_finishTime{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\"} - cce_task_create_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "任务耗时 AVG", "refId": "C"}, {"exemplar": true, "expr": "min( cce_task_finishTime{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\"} - cce_task_create_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "任务耗时 MIN", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "变更任务耗时（所有未删除的任务）", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:60", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:61", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 33}, "id": 88, "panels": [], "title": "异常实例/任务详情", "type": "row"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "displayMode": "auto", "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "phase"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "accountID"}, "properties": [{"id": "custom.width", "value": 285}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "clusterID"}, "properties": [{"id": "custom.width", "value": 113}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "name"}, "properties": [{"id": "custom.width", "value": 216}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "region"}, "properties": [{"id": "custom.width", "value": 61}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 34}, "id": 89, "options": {"showHeader": true, "sortBy": []}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(cce_instance_phase{clusterID=~\"$clusterID\",accountID=~\"$accountID\",phase=~\"create_failed|deleta_failed|upgrade_failed|unknown\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\"}) by (accountID, region, clusterID, instanceGroupID, name, phase) > 0", "format": "table", "instant": false, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "异常实例", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true}, "indexByName": {"Time": 0, "Value": 6, "accountID": 1, "clusterID": 3, "name": 4, "phase": 5, "region": 2}, "renameByName": {}}}, {"id": "groupBy", "options": {"fields": {"Time": {"aggregations": [], "operation": "groupby"}, "accountID": {"aggregations": [], "operation": "groupby"}, "clusterID": {"aggregations": [], "operation": "groupby"}, "name": {"aggregations": [], "operation": "groupby"}, "phase": {"aggregations": [], "operation": "groupby"}, "region": {"aggregations": [], "operation": "groupby"}}}}], "type": "table"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "displayMode": "auto", "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "phase"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "accountID"}, "properties": [{"id": "custom.width", "value": 272}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "region"}, "properties": [{"id": "custom.width", "value": 66}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "clusterID"}, "properties": [{"id": "custom.width", "value": 113}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "name"}, "properties": [{"id": "custom.width", "value": 240}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 34}, "id": 90, "options": {"showHeader": true, "sortBy": []}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(cce_task_phase{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\", phase=~\"Aborted\", name=~\"$taskName\"}) by (accountID, region, clusterID, name, phase) > 0", "format": "table", "instant": false, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "异常变更任务", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true}, "indexByName": {"Time": 0, "Value": 6, "accountID": 1, "clusterID": 3, "name": 4, "phase": 5, "region": 2}, "renameByName": {}}}, {"id": "groupBy", "options": {"fields": {"accountID": {"aggregations": [], "operation": "groupby"}, "clusterID": {"aggregations": [], "operation": "groupby"}, "name": {"aggregations": [], "operation": "groupby"}, "phase": {"aggregations": [], "operation": "groupby"}, "region": {"aggregations": [], "operation": "groupby"}}}}], "type": "table"}, {"datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 42}, "id": 61, "title": "任务各阶段耗时趋势", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*耗时.*"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 43}, "hiddenSeries": false, "id": 36, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeat": "processName", "scopedVars": {"processName": {"selected": false, "text": "CreateMachines", "value": "CreateMachines"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_task_process_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\",processName=~\"$processName\"} - cce_task_process_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\",processName=~\"$processName\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_task_process_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\",processName=~\"$processName\"} - cce_task_process_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\",processName=~\"$processName\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "A"}, {"exemplar": true, "expr": "quantile(0.9, cce_task_process_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\",processName=~\"$processName\"} - cce_task_process_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\",processName=~\"$processName\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "B"}, {"exemplar": true, "expr": "avg(cce_task_process_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\",processName=~\"$processName\"} - cce_task_process_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\",processName=~\"$processName\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "C"}, {"exemplar": true, "expr": "min(cce_task_process_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\",processName=~\"$processName\"} - cce_task_process_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\",processName=~\"$processName\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "变更任务 $processName 阶段耗时（所有未删除的任务）", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:316", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:317", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*耗时.*"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 43}, "hiddenSeries": false, "id": 102, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeatIteration": *************, "repeatPanelId": 36, "scopedVars": {"processName": {"selected": false, "text": "MoveMachinesIntoInstanceGroup", "value": "MoveMachinesIntoInstanceGroup"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_task_process_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\",processName=~\"$processName\"} - cce_task_process_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\",processName=~\"$processName\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_task_process_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\",processName=~\"$processName\"} - cce_task_process_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\",processName=~\"$processName\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "A"}, {"exemplar": true, "expr": "quantile(0.9, cce_task_process_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\",processName=~\"$processName\"} - cce_task_process_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\",processName=~\"$processName\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "B"}, {"exemplar": true, "expr": "avg(cce_task_process_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\",processName=~\"$processName\"} - cce_task_process_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\",processName=~\"$processName\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "C"}, {"exemplar": true, "expr": "min(cce_task_process_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\",processName=~\"$processName\"} - cce_task_process_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\",processName=~\"$processName\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "变更任务 $processName 阶段耗时（所有未删除的任务）", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:316", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:317", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 51}, "id": 51, "panels": [], "title": "节点组各阶段耗时趋势", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 0, "y": 52}, "hiddenSeries": false, "id": 49, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeat": "mainStep", "scopedVars": {"mainStep": {"selected": false, "text": "CreateMachine", "value": "CreateMachine"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "实例 $mainStep 阶段耗时统计（所有未删除的实例）", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 6, "y": 52}, "hiddenSeries": false, "id": 103, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeatIteration": *************, "repeatPanelId": 49, "scopedVars": {"mainStep": {"selected": false, "text": "CreateMachineAndReinstallOS", "value": "CreateMachineAndReinstallOS"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "实例 $mainStep 阶段耗时统计（所有未删除的实例）", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 12, "y": 52}, "hiddenSeries": false, "id": 104, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeatIteration": *************, "repeatPanelId": 49, "scopedVars": {"mainStep": {"selected": false, "text": "EnsureSecurityGroups", "value": "EnsureSecurityGroups"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "实例 $mainStep 阶段耗时统计（所有未删除的实例）", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 18, "y": 52}, "hiddenSeries": false, "id": 105, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeatIteration": *************, "repeatPanelId": 49, "scopedVars": {"mainStep": {"selected": false, "text": "EnsureSecurityGroupsV2", "value": "EnsureSecurityGroupsV2"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "实例 $mainStep 阶段耗时统计（所有未删除的实例）", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 0, "y": 61}, "hiddenSeries": false, "id": 106, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeatIteration": *************, "repeatPanelId": 49, "scopedVars": {"mainStep": {"selected": false, "text": "WaitAPIServerWhiteList", "value": "WaitAPIServerWhiteList"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "实例 $mainStep 阶段耗时统计（所有未删除的实例）", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 6, "y": 61}, "hiddenSeries": false, "id": 107, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeatIteration": *************, "repeatPanelId": 49, "scopedVars": {"mainStep": {"selected": false, "text": "Deploy", "value": "Deploy"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "实例 $mainStep 阶段耗时统计（所有未删除的实例）", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 12, "y": 61}, "hiddenSeries": false, "id": 108, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeatIteration": *************, "repeatPanelId": 49, "scopedVars": {"mainStep": {"selected": false, "text": "SyncNodeInfo", "value": "SyncNodeInfo"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "实例 $mainStep 阶段耗时统计（所有未删除的实例）", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 18, "y": 61}, "hiddenSeries": false, "id": 109, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeatIteration": *************, "repeatPanelId": 49, "scopedVars": {"mainStep": {"selected": false, "text": "WaitForNodeReady", "value": "WaitForNodeReady"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "实例 $mainStep 阶段耗时统计（所有未删除的实例）", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 0, "y": 70}, "hiddenSeries": false, "id": 110, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeatIteration": *************, "repeatPanelId": 49, "scopedVars": {"mainStep": {"selected": false, "text": "EnsurePostUserScript", "value": "EnsurePostUserScript"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "实例 $mainStep 阶段耗时统计（所有未删除的实例）", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 79}, "id": 22, "panels": [], "title": "集群变更任务耗时详情", "type": "row"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "clocks"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 30}]}}]}, {"matcher": {"id": "byRegexp", "options": ".*耗时"}, "properties": [{"id": "unit", "value": "clocks"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 10}, {"color": "red", "value": 30}]}}, {"id": "custom.filterable", "value": false}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "name"}, "properties": [{"id": "custom.width", "value": null}]}]}, "gridPos": {"h": 5, "w": 24, "x": 0, "y": 80}, "id": 48, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "开始时间"}]}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "max(cce_task_finishTime{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\"} - cce_task_create_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\"}) by (name)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "总耗时"}, {"exemplar": true, "expr": "1000*max(cce_task_create_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\"}) by (name)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "开始时间"}, {"exemplar": true, "expr": "1000*max(cce_task_finishTime{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\"}) by (name)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "结束时间"}], "timeFrom": null, "timeShift": null, "title": "变更任务耗时（所有未删除的任务）", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {"Time": 0, "Value #P99 总耗时": 3, "Value #平均总耗时": 5, "Value #最大总耗时": 4, "name": 1, "processName": 2}, "renameByName": {"Value": "耗时", "Value #A": "最大耗时", "Value #B": "最大重试次数", "Value #P99 总耗时": "P99 总耗时", "Value #P99 重试次数": "P99 重试次数", "Value #平均总耗时": "平均总耗时", "Value #开始时间": "开始时间", "Value #总耗时": "耗时", "Value #最大单次耗时": "最大单次耗时", "Value #最大总耗时": "最大总耗时", "Value #最大重试次数": "最大重试次数", "Value #结束时间": "结束时间", "name": "变更任务ID", "processName": "变更阶段"}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": true, "field": "最大总耗时"}]}}], "type": "table"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 30}]}}]}, {"matcher": {"id": "byRegexp", "options": ".*耗时"}, "properties": [{"id": "unit", "value": "clocks"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 10}, {"color": "red", "value": 30}]}}, {"id": "custom.filterable", "value": false}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "name"}, "properties": [{"id": "custom.width", "value": null}]}]}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 85}, "id": 53, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "开始时间"}]}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "max(cce_task_process_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\"} - cce_task_process_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\"}) by (name, processName)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最大总耗时"}, {"exemplar": true, "expr": "1000*max(cce_task_process_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\"}) by (name, processName)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "开始时间"}, {"exemplar": true, "expr": "1000*max(cce_task_process_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\".*$instanceGroupID.*\",name=~\"$taskName\"}) by (name, processName)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "结束时间"}], "timeFrom": null, "timeShift": null, "title": "变更任务各阶段耗时（所有未删除的任务）", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {"Time": 0, "Value #P99 总耗时": 3, "Value #平均总耗时": 5, "Value #最大总耗时": 4, "name": 1, "processName": 2}, "renameByName": {"Value": "耗时", "Value #A": "最大耗时", "Value #B": "最大重试次数", "Value #P99 总耗时": "P99 总耗时", "Value #P99 重试次数": "P99 重试次数", "Value #平均总耗时": "平均总耗时", "Value #开始时间": "开始时间", "Value #最大单次耗时": "最大单次耗时", "Value #最大总耗时": "耗时", "Value #最大重试次数": "最大重试次数", "Value #结束时间": "结束时间", "name": "变更任务ID", "processName": "变更阶段"}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": true, "field": "最大总耗时"}]}}], "type": "table"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 92}, "id": 38, "panels": [], "title": "实例部署各阶段耗时统计", "type": "row"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 30}]}}, {"id": "custom.width", "value": 76}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 10}, {"color": "red", "value": 30}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "instanceGroupID"}, "properties": [{"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "step"}, "properties": [{"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}, {"id": "custom.width", "value": 150}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "端到端耗时"}, "properties": [{"id": "custom.width", "value": 99}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "P99 耗时"}, "properties": [{"id": "custom.width", "value": 83}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "最小耗时"}, "properties": [{"id": "custom.width", "value": 76}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "平均耗时"}, "properties": [{"id": "custom.width", "value": 80}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "最大重试次数"}, "properties": [{"id": "custom.width", "value": 109}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 2}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "最大启动延迟"}, "properties": [{"id": "custom.width", "value": 99}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "最大结束延迟"}, "properties": [{"id": "custom.width", "value": 99}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "节点组ID"}, "properties": [{"id": "custom.width", "value": 123}, {"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "最早结束时间"}, "properties": [{"id": "custom.width", "value": 172}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "实例部署阶段"}, "properties": [{"id": "custom.width", "value": 207}, {"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "统计实例数"}, "properties": [{"id": "custom.width", "value": 94}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "节点组实例数"}, "properties": [{"id": "custom.width", "value": 96}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 93}, "id": 54, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "端到端耗时"}]}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "max(cce_instance_step_cost_seconds{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "最大单次耗时"}, {"exemplar": true, "expr": "max(cce_instance_step_retry_count{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最大重试次数"}, {"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}>0) by (instanceGroupID, step) - min(cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}>0) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "端到端耗时（第一个实例创建至最后一个实例完成）"}, {"exemplar": true, "expr": "min(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最小总耗时"}, {"exemplar": true, "expr": "1000*min(cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最早开始时间"}, {"exemplar": true, "expr": "1000*max(cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最晚开始时间"}, {"exemplar": true, "expr": "1000*min(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最早结束时间"}, {"exemplar": true, "expr": "1000*max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最晚结束时间"}, {"exemplar": true, "expr": "max(cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}>0) by (instanceGroupID, step) - min(cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}>0) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "最大启动延迟"}, {"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}>0) by (instanceGroupID, step) - min(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}>0) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "最大结束延迟"}, {"exemplar": true, "expr": "count(cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "统计实例数"}], "timeFrom": null, "timeShift": null, "title": "实例各阶段耗时统计&重试次数（所有未删除的实例）", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Value #最大单次耗时": true}, "indexByName": {"Time": 0, "Value #P99 总耗时": 5, "Value #平均总耗时": 8, "Value #最大单次耗时": 9, "Value #最大启动延迟": 13, "Value #最大总耗时": 7, "Value #最大结束延迟": 16, "Value #最大重试次数": 10, "Value #最小总耗时": 6, "Value #最早开始时间": 11, "Value #最早结束时间": 14, "Value #最晚开始时间": 12, "Value #最晚结束时间": 15, "Value #端到端耗时（第一个实例创建至最后一个实例完成）": 4, "Value #统计实例数": 3, "instanceGroupID": 1, "step": 2}, "renameByName": {"Value #A": "最大耗时", "Value #B": "最大重试次数", "Value #P99 总耗时": "P99 耗时", "Value #P99 重试次数": "P99 重试次数", "Value #平均总耗时": "平均耗时", "Value #最大单次耗时": "最大单次耗时", "Value #最大启动延迟": "最大启动延迟", "Value #最大总耗时": "最大耗时", "Value #最大等待开始耗时": "最大等待开始耗时", "Value #最大等待结束耗时": "最大结束延迟", "Value #最大等待耗时": "最大等待耗时", "Value #最大结束延迟": "最大结束延迟", "Value #最大重试次数": "最大重试次数", "Value #最小总耗时": "最小耗时", "Value #最早开始时间": "最早开始时间", "Value #最早结束时间": "最早结束时间", "Value #最晚开始时间": "最晚开始时间", "Value #最晚结束时间": "最晚结束时间", "Value #端到端耗时（第一个实例创建至最后一个实例完成）": "端到端耗时", "Value #统计实例数": "节点组实例数", "instanceGroupID": "节点组ID", "step": "实例部署阶段"}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": true, "field": "最大总耗时"}]}}], "type": "table"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 30}]}}, {"id": "custom.width", "value": 76}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 10}, {"color": "red", "value": 30}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "instanceGroupID"}, "properties": [{"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "step"}, "properties": [{"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}, {"id": "custom.width", "value": 150}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "端到端耗时"}, "properties": [{"id": "custom.width", "value": 99}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "P99 耗时"}, "properties": [{"id": "custom.width", "value": 83}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "最小耗时"}, "properties": [{"id": "custom.width", "value": 76}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "平均耗时"}, "properties": [{"id": "custom.width", "value": 80}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "最大重试次数"}, "properties": [{"id": "custom.width", "value": 109}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 2}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "最大启动延迟"}, "properties": [{"id": "custom.width", "value": 99}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "最大结束延迟"}, "properties": [{"id": "custom.width", "value": 99}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "节点组ID"}, "properties": [{"id": "custom.width", "value": 123}, {"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "最早结束时间"}, "properties": [{"id": "custom.width", "value": 172}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "实例部署阶段"}, "properties": [{"id": "custom.width", "value": 207}, {"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "统计实例数"}, "properties": [{"id": "custom.width", "value": 94}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "节点组实例数"}, "properties": [{"id": "custom.width", "value": 103}]}]}, "gridPos": {"h": 13, "w": 24, "x": 0, "y": 103}, "id": 47, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "最大启动延迟"}]}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "max(cce_instance_step_cost_seconds{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "最大单次耗时"}, {"exemplar": true, "expr": "max(cce_instance_step_retry_count{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最大重试次数"}, {"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"}>0) by (instanceGroupID, step) - min(cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"}>0) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "端到端耗时（第一个实例创建至最后一个实例完成）"}, {"exemplar": true, "expr": "min(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最小总耗时"}, {"exemplar": true, "expr": "1000*min(cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最早开始时间"}, {"exemplar": true, "expr": "1000*max(cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最晚开始时间"}, {"exemplar": true, "expr": "1000*min(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最早结束时间"}, {"exemplar": true, "expr": "1000*max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最晚结束时间"}, {"exemplar": true, "expr": "max(cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"}>0) by (instanceGroupID, step) - min(cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"}>0) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "最大启动延迟"}, {"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"}>0) by (instanceGroupID, step) - min(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"}>0) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "最大结束延迟"}, {"exemplar": true, "expr": "count(cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step!~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "统计实例数"}], "timeFrom": null, "timeShift": null, "title": "实例部署阶段耗时统计&重试次数（所有未删除的实例）", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Value #最大单次耗时": true}, "indexByName": {"Time": 0, "Value #P99 总耗时": 5, "Value #平均总耗时": 8, "Value #最大单次耗时": 9, "Value #最大启动延迟": 13, "Value #最大总耗时": 7, "Value #最大结束延迟": 16, "Value #最大重试次数": 10, "Value #最小总耗时": 6, "Value #最早开始时间": 11, "Value #最早结束时间": 14, "Value #最晚开始时间": 12, "Value #最晚结束时间": 15, "Value #端到端耗时（第一个实例创建至最后一个实例完成）": 4, "Value #统计实例数": 3, "instanceGroupID": 1, "step": 2}, "renameByName": {"Value #A": "最大耗时", "Value #B": "最大重试次数", "Value #P99 总耗时": "P99 耗时", "Value #P99 重试次数": "P99 重试次数", "Value #平均总耗时": "平均耗时", "Value #最大单次耗时": "最大单次耗时", "Value #最大启动延迟": "最大启动延迟", "Value #最大总耗时": "最大耗时", "Value #最大等待开始耗时": "最大等待开始耗时", "Value #最大等待结束耗时": "最大结束延迟", "Value #最大等待耗时": "最大等待耗时", "Value #最大结束延迟": "最大结束延迟", "Value #最大重试次数": "最大重试次数", "Value #最小总耗时": "最小耗时", "Value #最早开始时间": "最早开始时间", "Value #最早结束时间": "最早结束时间", "Value #最晚开始时间": "最晚开始时间", "Value #最晚结束时间": "最晚结束时间", "Value #端到端耗时（第一个实例创建至最后一个实例完成）": "端到端耗时", "Value #统计实例数": "节点组实例数", "instanceGroupID": "节点组ID", "step": "实例部署阶段"}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": true, "field": "最大总耗时"}]}}], "type": "table"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "displayMode": "color-background", "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 20}, {"color": "red", "value": 30}]}, "unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 30}]}}]}, {"matcher": {"id": "byRegexp", "options": ".*耗时"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-background"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 60}, {"color": "red", "value": 120}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "实例部署阶段"}, "properties": [{"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "CCE实例ID"}, "properties": [{"id": "custom.filterable", "value": true}, {"id": "custom.displayMode", "value": "auto"}, {"id": "custom.width", "value": 184}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "节点组ID"}, "properties": [{"id": "custom.filterable", "value": true}, {"id": "custom.displayMode"}, {"id": "custom.width", "value": 120}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}, {"matcher": {"id": "byRegexp", "options": ".*重试次数"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 2}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "端到端耗时"}, "properties": [{"id": "custom.width", "value": 98}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "CreateMachine"}, "properties": [{"id": "custom.width", "value": 121}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "EnsureSecurityGroups"}, "properties": [{"id": "custom.width", "value": 164}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "EnsureSecurityGroupsV2"}, "properties": [{"id": "custom.width", "value": 173}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "WaitAPIServerWhiteList"}, "properties": [{"id": "custom.width", "value": 172}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Deploy"}, "properties": [{"id": "custom.width", "value": 84}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "SyncNodeInfo"}, "properties": [{"id": "custom.width", "value": 105}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "EnsurePostUserScript"}, "properties": [{"id": "custom.width", "value": 156}]}]}, "gridPos": {"h": 15, "w": 24, "x": 0, "y": 116}, "id": 46, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "WaitForNodeReady"}]}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\",step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\",step=~\"$mainStep\"}) by (instanceGroupID, name, step)", "format": "time_series", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最大总耗时"}], "timeFrom": null, "timeShift": null, "title": "实例部署各阶段耗时（所有未删除的实例）", "transformations": [{"id": "labelsToFields", "options": {"valueLabel": "step"}}, {"id": "calculateField", "options": {"mode": "reduceRow", "reduce": {"include": ["Deploy", "SyncNodeInfo", "CreateMachine", "EnsurePostUserScript", "EnsureSecurityGroups", "EnsureSecurityGroupsV2", "WaitAPIServerWhiteList", "WaitForNodeReady"], "reducer": "sum"}}}, {"id": "organize", "options": {"excludeByName": {"Clean": true, "DeliverPackage": true, "EnsureDiskMounted": true, "EnsureK8SServiceRunning": true, "EnsureSystemInited": true, "SyncNodeInfo": false, "Time": true, "Value #最大单次耗时": true, "WaitAPIServerWhiteList": false}, "indexByName": {"CreateMachine": 4, "Deploy": 8, "EnsurePostUserScript": 10, "EnsureSecurityGroups": 5, "EnsureSecurityGroupsV2": 6, "SyncNodeInfo": 9, "Time": 0, "Total": 3, "WaitAPIServerWhiteList": 7, "instanceGroupID": 1, "name": 2}, "renameByName": {"Total": "端到端耗时", "Value #A": "最大耗时", "Value #B": "最大重试次数", "Value #P99 总耗时": "P99 耗时", "Value #P99 重试次数": "P99 重试次数", "Value #平均总耗时": "平均耗时", "Value #开始时间": "开始时间", "Value #最大单次耗时": "最大单次耗时", "Value #最大总耗时": "最大耗时", "Value #最大重试次数": "重试次数", "Value #最小总耗时": "最小耗时", "Value #端到端耗时（第一个实例创建至最后一个实例完成）": "端到端耗时", "Value #结束时间": "结束时间", "instanceGroupID": "节点组ID", "name": "CCE实例ID", "step": "实例部署阶段"}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": true, "field": "端到端耗时"}]}}], "type": "table"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 131}, "id": 42, "panels": [], "title": "节点组实例详情", "type": "row"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 30}]}}]}, {"matcher": {"id": "byRegexp", "options": ".*耗时"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-background"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 10}, {"color": "red", "value": 20}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "节点组ID"}, "properties": [{"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "step"}, "properties": [{"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "name"}, "properties": [{"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}, {"matcher": {"id": "byRegexp", "options": ".*重试次数"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 2}]}}]}]}, "gridPos": {"h": 5, "w": 24, "x": 0, "y": 132}, "id": 40, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "统计覆盖的实例数"}]}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "count(max(cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\"}) by (instanceGroupID, name)) by (instanceGroupID)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "统计实例数"}], "timeFrom": null, "timeShift": null, "title": "统计覆盖的实例数", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Value #最大单次耗时": true}, "indexByName": {"Time": 0, "Value #P99 总耗时": 5, "Value #平均总耗时": 8, "Value #最大单次耗时": 9, "Value #最大总耗时": 7, "Value #最大重试次数": 10, "Value #最小总耗时": 6, "Value #端到端耗时（第一个实例创建至最后一个实例完成）": 4, "instanceGroupID": 1, "name": 2, "step": 3}, "renameByName": {"Value": "统计覆盖的实例数", "Value #A": "最大耗时", "Value #B": "最大重试次数", "Value #P99 总耗时": "P99 耗时", "Value #P99 重试次数": "P99 重试次数", "Value #平均总耗时": "平均耗时", "Value #开始时间": "开始时间", "Value #最大单次耗时": "最大单次耗时", "Value #最大总耗时": "最大耗时", "Value #最大重试次数": "重试次数", "Value #最小总耗时": "最小耗时", "Value #端到端耗时（第一个实例创建至最后一个实例完成）": "端到端耗时", "Value #结束时间": "结束时间", "instanceGroupID": "节点组ID", "name": "CCE实例ID", "step": "实例部署阶段"}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": true, "field": "最大总耗时"}]}}], "type": "table"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 30}]}}]}, {"matcher": {"id": "byRegexp", "options": ".*耗时"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-background"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 10}, {"color": "red", "value": 20}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "实例部署阶段"}, "properties": [{"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "CCE实例ID"}, "properties": [{"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "节点组ID"}, "properties": [{"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}, {"matcher": {"id": "byRegexp", "options": ".*重试次数"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 2}]}}]}]}, "gridPos": {"h": 18, "w": 24, "x": 0, "y": 137}, "id": 76, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "开始时间"}]}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "max(cce_instance_step_retry_count{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\"}) by (instanceGroupID, name, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最大重试次数"}, {"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\"}) by (instanceGroupID, name, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最大总耗时"}, {"exemplar": true, "expr": "1000*max(cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\"}) by (instanceGroupID, name, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "开始时间"}, {"exemplar": true, "expr": "1000*max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\"}) by (instanceGroupID, name, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "结束时间"}], "timeFrom": null, "timeShift": null, "title": "实例部署各阶段耗时&重试次数（所有未删除的实例）", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Value #最大单次耗时": true}, "indexByName": {"Time": 0, "Value #P99 总耗时": 5, "Value #平均总耗时": 8, "Value #最大单次耗时": 9, "Value #最大总耗时": 7, "Value #最大重试次数": 10, "Value #最小总耗时": 6, "Value #端到端耗时（第一个实例创建至最后一个实例完成）": 4, "instanceGroupID": 1, "name": 2, "step": 3}, "renameByName": {"Value #A": "最大耗时", "Value #B": "最大重试次数", "Value #P99 总耗时": "P99 耗时", "Value #P99 重试次数": "P99 重试次数", "Value #平均总耗时": "平均耗时", "Value #开始时间": "开始时间", "Value #最大单次耗时": "最大单次耗时", "Value #最大总耗时": "最大耗时", "Value #最大重试次数": "重试次数", "Value #最小总耗时": "最小耗时", "Value #端到端耗时（第一个实例创建至最后一个实例完成）": "端到端耗时", "Value #结束时间": "结束时间", "instanceGroupID": "节点组ID", "name": "CCE实例ID", "step": "实例部署阶段"}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": true, "field": "最大总耗时"}]}}], "type": "table"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 30}]}}, {"id": "custom.width", "value": 76}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 10}, {"color": "red", "value": 30}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "instanceGroupID"}, "properties": [{"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "step"}, "properties": [{"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}, {"id": "custom.width", "value": 150}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "端到端耗时"}, "properties": [{"id": "custom.width", "value": 99}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "P99 耗时"}, "properties": [{"id": "custom.width", "value": 83}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "最小耗时"}, "properties": [{"id": "custom.width", "value": 76}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "平均耗时"}, "properties": [{"id": "custom.width", "value": 80}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "最大重试次数"}, "properties": [{"id": "custom.width", "value": 109}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 2}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "最大启动延迟"}, "properties": [{"id": "custom.width", "value": 99}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "最大结束延迟"}, "properties": [{"id": "custom.width", "value": 99}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "节点组ID"}, "properties": [{"id": "custom.width", "value": 123}, {"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "最早结束时间"}, "properties": [{"id": "custom.width", "value": 172}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "实例部署阶段"}, "properties": [{"id": "custom.width", "value": 207}, {"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "统计实例数"}, "properties": [{"id": "custom.width", "value": 94}]}]}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 155}, "id": 45, "options": {"showHeader": true, "sortBy": [{"desc": false, "displayName": "最早开始时间"}]}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "max(cce_instance_step_cost_seconds{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "最大单次耗时"}, {"exemplar": true, "expr": "max(cce_instance_step_retry_count{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最大重试次数"}, {"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}>0) by (instanceGroupID, step) - min(cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}>0) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "端到端耗时（第一个实例创建至最后一个实例完成）"}, {"exemplar": true, "expr": "min(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最小总耗时"}, {"exemplar": true, "expr": "1000*min(cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最早开始时间"}, {"exemplar": true, "expr": "1000*max(cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最晚开始时间"}, {"exemplar": true, "expr": "1000*min(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最早结束时间"}, {"exemplar": true, "expr": "1000*max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最晚结束时间"}, {"exemplar": true, "expr": "max(cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}>0) by (instanceGroupID, step) - min(cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}>0) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "最大启动延迟"}, {"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}>0) by (instanceGroupID, step) - min(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}>0) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "最大结束延迟"}, {"exemplar": true, "expr": "count(cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",instanceGroupID=~\"$instanceGroupID\",name=~\"$instanceName\", step=~\"$mainStep\"}) by (instanceGroupID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "统计实例数"}], "timeFrom": null, "timeShift": null, "title": "实例部署各主阶段耗时统计&重试次数（所有未删除的实例）", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Value #最大单次耗时": true}, "indexByName": {"Time": 0, "Value #P99 总耗时": 5, "Value #平均总耗时": 8, "Value #最大单次耗时": 9, "Value #最大启动延迟": 13, "Value #最大总耗时": 7, "Value #最大结束延迟": 16, "Value #最大重试次数": 10, "Value #最小总耗时": 6, "Value #最早开始时间": 11, "Value #最早结束时间": 14, "Value #最晚开始时间": 12, "Value #最晚结束时间": 15, "Value #端到端耗时（第一个实例创建至最后一个实例完成）": 4, "Value #统计实例数": 3, "instanceGroupID": 1, "step": 2}, "renameByName": {"Value #A": "最大耗时", "Value #B": "最大重试次数", "Value #P99 总耗时": "P99 耗时", "Value #P99 重试次数": "P99 重试次数", "Value #平均总耗时": "平均耗时", "Value #最大单次耗时": "最大单次耗时", "Value #最大启动延迟": "最大启动延迟", "Value #最大总耗时": "最大耗时", "Value #最大等待开始耗时": "最大等待开始耗时", "Value #最大等待结束耗时": "最大结束延迟", "Value #最大等待耗时": "最大等待耗时", "Value #最大结束延迟": "最大结束延迟", "Value #最大重试次数": "最大重试次数", "Value #最小总耗时": "最小耗时", "Value #最早开始时间": "最早开始时间", "Value #最早结束时间": "最早结束时间", "Value #最晚开始时间": "最晚开始时间", "Value #最晚结束时间": "最晚结束时间", "Value #端到端耗时（第一个实例创建至最后一个实例完成）": "端到端耗时", "Value #统计实例数": "统计实例数", "instanceGroupID": "节点组ID", "step": "实例部署阶段"}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": true, "field": "最大总耗时"}]}}], "type": "table"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 164}, "id": 20, "panels": [{"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 30}]}}]}, {"matcher": {"id": "byRegexp", "options": ".*耗时"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 10}, {"color": "red", "value": 30}]}}]}]}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 164}, "id": 10, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "P99 总耗时"}]}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "max(cce_instance_step_cost_seconds{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"} * on(accountID, clusterID, region, name, step) group_left() (timestamp(cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"}) - cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"} <= bool 86400)) by (step)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "最大单次耗时"}, {"exemplar": true, "expr": "max(cce_instance_step_retry_count{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"} * on(accountID, clusterID, region, name, step) group_left() (timestamp(cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"}) - cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"} <= bool 86400)) by (step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最大重试次数"}, {"exemplar": true, "expr": "max((cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) * on(accountID, clusterID, region, name, step) group_left() (timestamp(cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"}) - cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"} <= bool 86400)) by (step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg((cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) * on(accountID, clusterID, region, name, step) group_left() (timestamp(cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"}) - cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"} <= bool 86400)) by (step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, (cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) * on(accountID, clusterID, region, name, step) group_left() (timestamp(cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"}) - cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"} <= bool 86400)) by (step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_instance_step_retry_count{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"} * on(accountID, clusterID, region, name, step) group_left() (timestamp(cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"}) - cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"} <= bool 86400)) by (step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "P99 重试次数"}], "timeFrom": null, "timeShift": null, "title": "Instance 各阶段耗时&重试次数（仅统计 24h 窗口内进入各阶段的 Instance）", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {"Time": 0, "Value #P99 总耗时": 2, "Value #P99 重试次数": 6, "Value #平均总耗时": 4, "Value #最大单次耗时": 5, "Value #最大总耗时": 3, "Value #最大重试次数": 7, "step": 1}, "renameByName": {"Value #A": "最大耗时", "Value #B": "最大重试次数", "Value #P99 总耗时": "P99 总耗时", "Value #P99 重试次数": "P99 重试次数", "Value #平均总耗时": "平均总耗时", "Value #最大单次耗时": "最大单次耗时", "Value #最大总耗时": "最大总耗时", "Value #最大重试次数": "最大重试次数"}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": true, "field": "P99 总耗时"}]}}], "type": "table"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*耗时"}, "properties": [{"id": "unit", "value": "s"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 164}, "hiddenSeries": false, "id": 34, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_instance_step_cost_seconds{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"} * on(accountID, clusterID, region, name, step) group_left() (timestamp(cce_instance_step_start_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"}) - cce_instance_step_start_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"} <= bool 300)) by (step)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "最大单次耗时"}, {"exemplar": true, "expr": "max(cce_instance_step_retry_count{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"} * on(accountID, clusterID, region, name, step) group_left() (timestamp(cce_instance_step_start_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"}) - cce_instance_step_start_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"} <= bool 300)) by (step)", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "最大重试次数"}, {"exemplar": true, "expr": "max((cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) * on(accountID, clusterID, region, name, step) group_left() (timestamp(cce_instance_step_start_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"}) - cce_instance_step_start_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"} <= bool 300)) by (step)", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg((cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) * on(accountID, clusterID, region, name, step) group_left() (timestamp(cce_instance_step_start_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"}) - cce_instance_step_start_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"} <= bool 300)) by (step)", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, (cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) * on(accountID, clusterID, region, name, step) group_left() (timestamp(cce_instance_step_start_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"}) - cce_instance_step_start_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"} <= bool 300)) by (step)", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_instance_step_retry_count{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"} * on(accountID, clusterID, region, name, step) group_left() (timestamp(cce_instance_step_start_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"}) - cce_instance_step_start_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"} <= bool 300)) by (step)", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "P99 重试次数"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Instance 各阶段耗时&重试次数（仅统计 5 分钟窗口内创建的 Instance）", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:185", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:186", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 173}, "hiddenSeries": false, "id": 23, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "quantile(0.99, (cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) * on(accountID, clusterID, region, name, step) group_left() (timestamp(cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"}) - cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"} <= bool 60)) by (step)", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "{{step}}", "refId": "实例创建耗时"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Instance 各阶段耗时 P99（仅统计 1 分钟窗口内进入各阶段的 Instance）", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:410", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:411", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*耗时"}, "properties": [{"id": "unit", "value": "s"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 173}, "hiddenSeries": false, "id": 35, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max((cce_task_finishTime{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\"$taskName\"} - cce_task_create_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\"$taskName\"}) * on(accountID, clusterID, region, name) group_left() (timestamp(cce_task_create_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\",name=~\"$taskName\"}) - cce_task_create_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\",name=~\"$taskName\"} <= bool 300))", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "e2e max", "refId": "B"}, {"exemplar": true, "expr": "max((cce_task_process_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\"$taskName\"} - cce_task_process_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\"$taskName\"}) * on(accountID, clusterID, region, name, processName) group_left() (timestamp(cce_task_process_start_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\",name=~\"$taskName\"}) - cce_task_process_start_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\",name=~\"$taskName\"} <= bool 300)) by (processName)", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "{{processName}} max", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Task 各阶段耗时（仅统计 5 分钟窗口内创建的 Task）", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:185", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:186", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 0}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "总节点数"}, "properties": [{"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}]}]}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 182}, "id": 15, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "text": {}, "textMode": "value_and_name"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(cce_instance_phase{clusterID=~\"$clusterID\",accountID=~\"$accountID\",region=~\"$region\"} * on(accountID, clusterID, region, name, step) group_left() (timestamp(cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"}) - cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"} <= bool 86400)) by (accountID)", "hide": false, "instant": true, "interval": "", "legendFormat": "总节点数", "refId": "D"}, {"exemplar": true, "expr": "count(sum(cce_instance_phase{clusterID=~\"$clusterID\",accountID=~\"$accountID\",phase=~\"create_failed|deleta_failed|upgrade_failed|unknown\",region=~\"$region\"} * on(accountID, clusterID, region, name, step) group_left() (timestamp(cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"}) - cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"} <= bool 86400)) by (clusterID) > 0)", "hide": false, "instant": true, "interval": "", "legendFormat": "有异常节点的集群数", "refId": "C"}, {"exemplar": true, "expr": "sum(cce_instance_phase{clusterID=~\"$clusterID\",accountID=~\"$accountID\",phase=~\"create_failed|deleta_failed|upgrade_failed|unknown\",region=~\"$region\"} * on(accountID, clusterID, region, name, step) group_left() (timestamp(cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"}) - cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"} <= bool 86400))", "hide": false, "instant": true, "interval": "", "legendFormat": "异常节点总数", "refId": "B"}, {"exemplar": true, "expr": "max(sum(cce_instance_phase{clusterID=~\"$clusterID\",accountID=~\"$accountID\",phase=~\"create_failed|deleta_failed|upgrade_failed|unknown\",region=~\"$region\"} * on(accountID, clusterID, region, name, step) group_left() (timestamp(cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"}) - cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"} <= bool 86400)) by (clusterID))", "hide": false, "instant": true, "interval": "", "legendFormat": "集群异常节点最大值", "refId": "A"}, {"exemplar": true, "expr": "count(sum(cce_instance_phase{clusterID=~\"$clusterID\",accountID=~\"$accountID\",phase=~\"create_failed|deleta_failed|upgrade_failed|unknown\",region=~\"$region\"} * on(accountID, clusterID, region, name, step) group_left() (timestamp(cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"}) - cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"} <= bool 86400)) by (accountID) > 0)", "hide": false, "instant": true, "interval": "", "legendFormat": "有异常节点的用户数", "refId": "E"}], "timeFrom": null, "timeShift": null, "title": "Instance 异常分布（仅统计 24h 窗口内进入各阶段的 Instance）", "transformations": [], "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*耗时"}, "properties": [{"id": "unit", "value": "s"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 191}, "hiddenSeries": false, "id": 24, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max((cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) * on(accountID, clusterID, region, name, step) group_left() (timestamp(cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"}) - cce_instance_step_finish_time{accountID=~\"$accountID\", region=\"$region\", clusterID=\"$clusterID\"} <= bool 60)) by (step)", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "{{step}}", "refId": "实例创建耗时"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Instance 各阶段耗时 MAX（仅统计 1 分钟窗口内进入各阶段的 Instance）", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:410", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:411", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "增量概览", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 165}, "id": 29, "panels": [{"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 30}]}}]}, {"matcher": {"id": "byRegexp", "options": ".*耗时"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 10}, {"color": "red", "value": 30}]}}]}]}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 165}, "id": 32, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "最大总耗时"}]}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "max(cce_instance_step_retry_count{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) by (step)", "format": "table", "hide": true, "instant": true, "interval": "", "legendFormat": "", "refId": "最大重试次数"}, {"exemplar": true, "expr": "max(cce_task_process_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\"$taskName\"} - cce_task_process_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\"$taskName\"}) by (processName)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_task_process_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\"$taskName\"} - cce_task_process_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\"$taskName\"}) by (processName)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_task_process_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\"$taskName\"} - cce_task_process_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",name=~\"$taskName\"}) by (processName)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "P99 总耗时"}], "timeFrom": null, "timeShift": null, "title": "Task 各阶段耗时（含存量 task）", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {"Time": 0, "Value #P99 总耗时": 2, "Value #平均总耗时": 4, "Value #最大总耗时": 3, "processName": 1}, "renameByName": {"Value #A": "最大耗时", "Value #B": "最大重试次数", "Value #P99 总耗时": "P99 总耗时", "Value #P99 重试次数": "P99 重试次数", "Value #平均总耗时": "平均总耗时", "Value #最大单次耗时": "最大单次耗时", "Value #最大总耗时": "最大总耗时", "Value #最大重试次数": "最大重试次数"}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": true, "field": "最大总耗时"}]}}], "type": "table"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*耗时"}, "properties": [{"id": "unit", "value": "s"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 165}, "hiddenSeries": false, "id": 26, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max((cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"})) by (step)", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "{{step}}", "refId": "实例创建耗时"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Instance 各阶段耗时 MAX（含存量 Instance）", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:410", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:411", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*耗时"}, "properties": [{"id": "unit", "value": "s"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 174}, "hiddenSeries": false, "id": 25, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "quantile(0.99, (cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"})) by (step)", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "{{step}}", "refId": "实例创建耗时"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Instance 各阶段耗时 P99（含存量 Instance）", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:410", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:411", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 30}]}}]}, {"matcher": {"id": "byRegexp", "options": ".*耗时"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 10}, {"color": "red", "value": 30}]}}]}]}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 183}, "id": 27, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "最大总耗时"}]}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "max(cce_instance_step_cost_seconds{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) by (step)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "最大单次耗时"}, {"exemplar": true, "expr": "max(cce_instance_step_retry_count{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) by (step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最大重试次数"}, {"exemplar": true, "expr": "max(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) by (step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) by (step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "平均总耗时"}, {"exemplar": true, "expr": "max(cce_instance_step_cost_seconds{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) by (step)", "format": "table", "hide": true, "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "quantile(0.99, cce_instance_step_finish_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"} - cce_instance_step_start_time{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) by (step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_instance_step_retry_count{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) by (step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "P99 重试次数"}], "timeFrom": null, "timeShift": null, "title": "Instance 各阶段耗时&重试次数（含存量 Instance）", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {"Time": 0, "Value #P99 总耗时": 2, "Value #P99 重试次数": 6, "Value #平均总耗时": 4, "Value #最大单次耗时": 5, "Value #最大总耗时": 3, "Value #最大重试次数": 7, "step": 1}, "renameByName": {"Value #A": "最大耗时", "Value #B": "最大重试次数", "Value #P99 总耗时": "P99 总耗时", "Value #P99 重试次数": "P99 重试次数", "Value #平均总耗时": "平均总耗时", "Value #最大单次耗时": "最大单次耗时", "Value #最大总耗时": "最大总耗时", "Value #最大重试次数": "最大重试次数"}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": true, "field": "最大总耗时"}]}}], "type": "table"}], "title": "存量概览", "type": "row"}], "refresh": "", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "cce-vm-cluster", "value": "cce-vm-cluster"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "current": {"selected": false, "text": "bjtest2", "value": "bjtest2"}, "datasource": "${datasource}", "definition": "label_values(cce_instance_create_time,region)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": null, "multi": false, "name": "region", "options": [], "query": {"query": "label_values(cce_instance_create_time,region)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".+", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "${datasource}", "definition": "label_values(cce_instance_create_time,accountID)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": null, "multi": true, "name": "accountID", "options": [], "query": {"query": "label_values(cce_instance_create_time,accountID)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".+", "current": {"selected": true, "text": ["cce-cqa5ku5o"], "value": ["cce-cqa5ku5o"]}, "datasource": "${datasource}", "definition": "label_values(cce_instance_create_time{accountID=~\"$accountID\",region=\"$region\"},clusterID)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": null, "multi": true, "name": "clusterID", "options": [], "query": {"query": "label_values(cce_instance_create_time{accountID=~\"$accountID\",region=\"$region\"},clusterID)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".+", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "${datasource}", "definition": "label_values(cce_instance_create_time{accountID=~\"$accountID\",region=\"$region\", clusterID=~\"$clusterID\"},instanceGroupID)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "节点组ID", "multi": true, "name": "instanceGroupID", "options": [], "query": {"query": "label_values(cce_instance_create_time{accountID=~\"$accountID\",region=\"$region\", clusterID=~\"$clusterID\"},instanceGroupID)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".+", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "${datasource}", "definition": "label_values(cce_task_create_time{accountID=~\"$accountID\",region=\"$region\", clusterID=~\"$clusterID\", name=~\".*$instanceGroupID.*\"},name)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "变更任务ID", "multi": true, "name": "taskName", "options": [], "query": {"query": "label_values(cce_task_create_time{accountID=~\"$accountID\",region=\"$region\", clusterID=~\"$clusterID\", name=~\".*$instanceGroupID.*\"},name)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".+", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "${datasource}", "definition": "label_values(cce_instance_create_time{accountID=~\"$accountID\",region=\"$region\", clusterID=~\"$clusterID\",instanceGroupID=~\"$instanceGroupID\"},name)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "CCE实例ID", "multi": true, "name": "instanceName", "options": [], "query": {"query": "label_values(cce_instance_create_time{accountID=~\"$accountID\",region=\"$region\", clusterID=~\"$clusterID\",instanceGroupID=~\"$instanceGroupID\"},name)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": "", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "description": null, "error": null, "hide": 0, "includeAll": true, "label": "实例主阶段", "multi": true, "name": "mainStep", "options": [{"selected": true, "text": "All", "value": "$__all"}, {"selected": false, "text": "CreateMachine", "value": "CreateMachine"}, {"selected": false, "text": "CreateMachineAndReinstallOS", "value": "CreateMachineAndReinstallOS"}, {"selected": false, "text": "EnsureSecurityGroups", "value": "EnsureSecurityGroups"}, {"selected": false, "text": "EnsureSecurityGroupsV2", "value": "EnsureSecurityGroupsV2"}, {"selected": false, "text": "WaitAPIServerWhiteList", "value": "WaitAPIServerWhiteList"}, {"selected": false, "text": "Deploy", "value": "Deploy"}, {"selected": false, "text": "SyncNodeInfo", "value": "SyncNodeInfo"}, {"selected": false, "text": "WaitForNodeReady", "value": "WaitForNodeReady"}, {"selected": false, "text": "EnsurePostUserScript", "value": "EnsurePostUserScript"}], "query": "CreateMachine, CreateMachineAndReinstallOS, EnsureSecurityGroups, EnsureSecurityGroupsV2, WaitAPIServerWhiteList, Deploy, SyncNodeInfo, WaitForNodeReady, EnsurePostUserScript", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"allValue": "", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "${datasource}", "definition": "label_values(cce_task_process_start_time{accountID=~\"$accountID\",region=\"$region\", clusterID=~\"$clusterID\",name=~\"$taskName\"},processName)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "任务阶段", "multi": true, "name": "processName", "options": [], "query": {"query": "label_values(cce_task_process_start_time{accountID=~\"$accountID\",region=\"$region\", clusterID=~\"$clusterID\",name=~\"$taskName\"},processName)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "节点/节点组性能", "uid": "rytjvHpNk", "version": 1}