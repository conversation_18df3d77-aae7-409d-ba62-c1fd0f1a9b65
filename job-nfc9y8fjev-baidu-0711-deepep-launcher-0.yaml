apiVersion: v1
kind: Pod
metadata:
  annotations:
    scheduling.k8s.io/group-name: job-nfc9y8fjev-baidu-0711-deepep
    volcano.sh/task-spec: launcher
  creationTimestamp: "2025-07-15T06:38:46Z"
  labels:
    infra.shiyak.com/job-id: job-nfc9y8fjev
    infra.shiyak.com/owner: zuti.he
    infra.shiyak.com/project-id: infra
    training.kubeflow.org/job-name: job-nfc9y8fjev-baidu-0711-deepep
    training.kubeflow.org/job-role: master
    training.kubeflow.org/operator-name: deepspeed-controller
    training.kubeflow.org/replica-index: "0"
    training.kubeflow.org/replica-type: launcher
  name: job-nfc9y8fjev-baidu-0711-deepep-launcher-0
  namespace: project-infra
  ownerReferences:
  - apiVersion: kubeflow.org/v1
    blockOwnerDeletion: true
    controller: true
    kind: DeepSpeedJob
    name: job-nfc9y8fjev-baidu-0711-deepep
    uid: d3d6311d-948c-484f-b525-0e12592cd886
  resourceVersion: "10723461"
  uid: c4e25834-**************-21d1756f3122
spec:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: nvidia.com/gpu.product
            operator: In
            values:
            - NVIDIA-H20Z
  containers:
  - command:
    - /bin/bash
    - -c
    - |-
      echo Bootstrap launcher ...
      if [ ! -x /usr/bin/pdsh ]; then
          echo /usr/bin/pdsh not found, installing ...
          DEBIAN_FRONTEND=noninteractive apt-get -y -q update > /dev/null
          DEBIAN_FRONTEND=noninteractive apt-get -y install openssh-client pdsh > /dev/null
          chown root /usr/lib/
      fi
      echo Execute user command ...
      sleep inf
    env:
    - name: MLP_CLUSTER
      value: hb
    - name: MLP_PROJECT
      value: infra
    - name: MLP_USER
      value: zuti.he
    - name: NCCL_DEBUG
      value: WARN
    - name: NCCL_SOCKET_IFNAME
      value: eth0
    - name: NCCL_IB_QPS_PER_CONNECTION
      value: "4"
    - name: NVIDIA_GDRCOPY
      value: enabled
    - name: NCCL_IB_HCA
      value: =mlx5_1,mlx5_2,mlx5_3,mlx5_4,mlx5_5,mlx5_6,mlx5_7,mlx5_8
    - name: NCCL_IB_TC
      value: "162"
    - name: NVSHMEM_IB_TRAFFIC_CLASS
      value: "130"
    - name: NVSHMEM_HCA_LIST
      value: mlx5_1:1,mlx5_2:1,mlx5_3:1,mlx5_4:1,mlx5_5:1,mlx5_6:1,mlx5_7:1,mlx5_8:1
    - name: UCX_TLS
      value: tcp
    - name: UCX_NET_DEVICES
      value: eth0
    - name: TORCH_CPP_LOG_LEVEL
      value: INFO
    - name: TORCH_DISTRIBUTED_DEBUG
      value: INFO
    - name: RAY_COLOR_PREFIX
      value: "0"
    - name: NVIDIA_VISIBLE_DEVICES
      value: void
    image: registry.baidubce.com/ai-native-dev/aixl:latest
    imagePullPolicy: Always
    name: deepspeed
    resources:
      limits:
        cpu: "2"
        memory: 4Gi
      requests:
        cpu: "2"
        memory: 4Gi
    terminationMessagePath: /dev/termination-log
    terminationMessagePolicy: File
    volumeMounts:
    - mountPath: /mnt/project
      name: host-path-project
    - mountPath: /mnt/personal
      name: host-path-personal
    - mountPath: /mnt/share
      name: host-path-share
    - mountPath: /job
      name: deepspeed-hostfile
    - mountPath: /etc/edl
      name: training-job-hostfile
    - mountPath: /root/.ssh
      name: ssh-config
      subPath: .ssh
    - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
      name: kube-api-access-hmrw7
      readOnly: true
  dnsPolicy: ClusterFirst
  enableServiceLinks: false
  initContainers:
  - command:
    - /bin/sh
    - -c
    - |
      cp /init-hostfile/hostfile /etc/edl/hostfile
      chmod 600 /etc/edl/hostfile
      cp /init-hostfile/discover_hosts.sh /etc/edl/discover_hosts.sh
      chmod +x /etc/edl/discover_hosts.sh
      cp /init-hostfile/deepspeed-hostfile /job/hostfile
      chmod 600 /job/hostfile

      for i in $(seq 120); do
          hosts=$(awk -F':' '{print $1}' /etc/edl/hostfile | xargs | sed 's/ /,/g')

          pdsh -S -R ssh -w "$hosts" "echo ok" &>/dev/null
          err=$?

          if [ $err -eq 0 ]; then
              echo "All worker nodes are reachable via SSH."
              break
          else
              echo "One or more worker nodes are not reachable via SSH. Retrying..."
              sleep 10
          fi
      done
      exit $err
    image: ghcr.io/shiyak-infra/deepspeed-init:v0.0.1
    imagePullPolicy: IfNotPresent
    name: init-hostfile
    resources: {}
    terminationMessagePath: /dev/termination-log
    terminationMessagePolicy: File
    volumeMounts:
    - mountPath: /job
      name: deepspeed-hostfile
    - mountPath: /etc/edl
      name: training-job-hostfile
    - mountPath: /init-hostfile
      name: training-job-config
    - mountPath: /root/.ssh
      name: ssh-config
      subPath: .ssh
    - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
      name: kube-api-access-hmrw7
      readOnly: true
  nodeName: **********
  preemptionPolicy: PreemptLowerPriority
  priority: 0
  restartPolicy: Never
  schedulerName: volcano
  securityContext: {}
  serviceAccount: default
  serviceAccountName: default
  terminationGracePeriodSeconds: 30
  tolerations:
  - effect: NoExecute
    key: node.kubernetes.io/not-ready
    operator: Exists
    tolerationSeconds: 300
  - effect: NoExecute
    key: node.kubernetes.io/unreachable
    operator: Exists
    tolerationSeconds: 300
  volumes:
  - hostPath:
      path: /mnt/pfs/project/project-infra
      type: Directory
    name: host-path-project
  - hostPath:
      path: /mnt/pfs/personal/zuti.he
      type: Directory
    name: host-path-personal
  - hostPath:
      path: /mnt/pfs/share
      type: Directory
    name: host-path-share
  - emptyDir: {}
    name: deepspeed-hostfile
  - emptyDir: {}
    name: training-job-hostfile
  - configMap:
      defaultMode: 420
      name: job-nfc9y8fjev-baidu-0711-deepep-hostfile
    name: training-job-config
  - name: ssh-config
    secret:
      defaultMode: 256
      items:
      - key: id_rsa
        path: .ssh/id_rsa
      - key: id_rsa.pub
        path: .ssh/authorized_keys
      - key: config
        path: .ssh/config
      secretName: job-nfc9y8fjev-baidu-0711-deepep-ssh-config
  - name: kube-api-access-hmrw7
    projected:
      defaultMode: 420
      sources:
      - serviceAccountToken:
          expirationSeconds: 3607
          path: token
      - configMap:
          items:
          - key: ca.crt
            path: ca.crt
          name: kube-root-ca.crt
      - downwardAPI:
          items:
          - fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
            path: namespace
status:
  conditions:
  - lastProbeTime: null
    lastTransitionTime: "2025-07-15T06:38:52Z"
    status: "True"
    type: PodReadyToStartContainers
  - lastProbeTime: null
    lastTransitionTime: "2025-07-15T06:39:53Z"
    status: "True"
    type: Initialized
  - lastProbeTime: null
    lastTransitionTime: "2025-07-15T06:39:54Z"
    status: "True"
    type: Ready
  - lastProbeTime: null
    lastTransitionTime: "2025-07-15T06:39:54Z"
    status: "True"
    type: ContainersReady
  - lastProbeTime: null
    lastTransitionTime: "2025-07-15T06:38:48Z"
    status: "True"
    type: PodScheduled
  containerStatuses:
  - containerID: containerd://0e08a9bf73401ef1b3c5900434295b3ff37416c35248f370f126849bf82a944d
    image: registry.baidubce.com/ai-native-dev/aixl:latest
    imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
    lastState: {}
    name: deepspeed
    ready: true
    restartCount: 0
    started: true
    state:
      running:
        startedAt: "2025-07-15T06:39:54Z"
  hostIP: **********
  hostIPs:
  - ip: **********
  initContainerStatuses:
  - containerID: containerd://971d979159f23904adc60b2ec008fcf0b8c6b72a6af1b02cc875edbbc08a0e39
    image: ghcr.io/shiyak-infra/deepspeed-init:v0.0.1
    imageID: ghcr.io/shiyak-infra/deepspeed-init@sha256:e17faad87f610e82216dde59b9422c9412b362cf37ed6e775c13d9e145074de7
    lastState: {}
    name: init-hostfile
    ready: true
    restartCount: 0
    started: false
    state:
      terminated:
        containerID: containerd://971d979159f23904adc60b2ec008fcf0b8c6b72a6af1b02cc875edbbc08a0e39
        exitCode: 0
        finishedAt: "2025-07-15T06:39:53Z"
        reason: Completed
        startedAt: "2025-07-15T06:38:50Z"
  phase: Running
  podIP: *************
  podIPs:
  - ip: *************
  qosClass: Burstable
  startTime: "2025-07-15T06:38:48Z"
