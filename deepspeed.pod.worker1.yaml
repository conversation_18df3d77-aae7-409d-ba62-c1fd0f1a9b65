apiVersion: v1
kind: Pod
metadata:
  annotations:
    k8s.v1.cni.cncf.io/networks: network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca
    scheduling.k8s.io/group-name: job-nfc9y8fjev
    volcano.sh/task-spec: worker
  labels:
    infra.shiyak.com/job-id: job-nfc9y8fjev
    infra.shiyak.com/owner: zuti.he
    infra.shiyak.com/project-id: infra
    training.kubeflow.org/job-name: job-nfc9y8fjev
    training.kubeflow.org/operator-name: deepspeed-controller
    training.kubeflow.org/replica-index: "1"
    training.kubeflow.org/replica-type: worker
  name: job-nfc9y8fjev-worker-1
  ownerReferences:
  - apiVersion: kubeflow.org/v1
    blockOwnerDeletion: true
    controller: true
    kind: DeepSpeedJob
    name: job-nfc9y8fjev-baidu-0711-deepep
    uid: f186f94a-43f6-42d7-b1ed-22cc1b51a9de
spec:
  containers:
  - name: nginx
    image: registry.baidubce.com/cce/nginx-alpine-go:latest
    command: ["sleep", "9999999"]