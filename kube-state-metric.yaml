        - groupVersionKind:
            group: cce.baidubce.com
            version: "v1"
            kind: Cluster
          metricNamePrefix: cce_cluster
          labelsFromPath:
            name: [metadata, name]
            accountID: [spec, accountID]
            clusterID: [spec, clusterID]
            clusterName: [spec, clusterName]
            k8sVersion: [spec, k8sVersion]
            masterType: [spec, masterConfig, masterType]
            vpcID: [spec, vpcID]
            networkMode: [spec, containerNetworkConfig, mode]
            clusterType: [spec, clusterType]
          metrics:
            - name: "create_timestamp"
              help: "cce cluster create timestamp"
              each:
                type: Gauge
                gauge:
                  path: [metadata]
                  valueFrom: [creationTimestamp]
            - name: "phase"
              help: "cce cluster phase"
              each:
                type: StateSet
                stateSet:
                  labelName: phase
                  path: [status,clusterPhase]
                  list: [pending,provisioning,provisioned,running,create_failed,deleting,deleted,delete_failed,upgrading,upgrade_failed,eip_opening,eip_open_failed,eip_closing,eip_close_failed]
            - name: "step_cost_seconds"
              help: "cce cluster step cost time"
              each:
                type: Gauge
                gauge:
                  path: [status,reconcileSteps]
                  labelFromKey: step
                  valueFrom: [costSeconds]
                  nilIsZero: true
            - name: "step_retry_count"
              help: "cce cluster step cost time"
              each:
                type: Gauge
                gauge:
                  path: [status,reconcileSteps]
                  labelFromKey: step
                  valueFrom: [retryCount]
                  nilIsZero: true
            - name: "retry_count"
              help: "cce cluster retry count"
              each:
                type: Gauge
                gauge:
                  path: [status]
                  valueFrom: [retryCount]
            - name: "step_start_timestamp"
              help: "cce cluster step start timestamp"
              each:
                type: Gauge
                gauge:
                  path: [status,reconcileSteps]
                  labelFromKey: step
                  valueFrom: [startTime]
                  nilIsZero: true
            - name: "step_finish_timestamp"
              help: "cce cluster step finish timestamp"
              each:
                type: Gauge
                gauge:
                  path: [status,reconcileSteps]
                  labelFromKey: step
                  valueFrom: [finishedTime]
            - name: "step_ready"
              help: "cce cluster step ready"
              each:
                type: Gauge
                gauge:
                  path: [status,reconcileSteps]
                  labelFromKey: step
                  valueFrom: [ready]
                  nilIsZero: true
            - name: "infra_status"
              help: "cce cluster infrastructure status"
              each:
                type: Gauge
                gauge:
                  path: [status]
                  valueFrom: [infrastructureReady]
            - name: "access_success"
              help: "cce cluster access status"
              each:
                type: Gauge
                gauge:
                  path: [status]
                  valueFrom: [apiServerAccessSuccess]