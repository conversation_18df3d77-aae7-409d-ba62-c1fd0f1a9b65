apiVersion: trainer.infra.shiyak.com/v1alpha1
kind: Trainer
metadata:
  annotations:
    ft.driver.master/check-result: '{"checkStatus":"failed","checkTime":1752103492,"operations":{"jobEventRecord":[{"timestamp":1752103492,"jobAbnormal":true,"workerAbnormal":["job-9ws19lbqpq:job
      restart (restart attempt 1), failed pods: job-9ws19lbqpq-worker-7: container
      pytorch Error: exitCode: 1"],"reason":"container-exit-restart-error"}]}}'
    infra.shiyak.com/job-name: pretrain_moe_1b_3b_sparse0p5
    infra.shiyak.com/job-owner: llm-user
    infra.shiyak.com/job-type: pytorch_ft
    infra.shiyak.com/job-user-config: ''
    trainer.infra.shiyak.com/driver-master-command: /driver master --period 10s --batchSize
      5000 --heartBeatCheckInterval 30s --heartBeatCheckTimes 10 --heartBeatCheckTimeout
      120s --port 8080 --jobId job-9ws19lbqpq --namespace project-llm --upstreamCheckConfig
      "{\"logCheck\":{\"hang\":{\"enable\":true,\"period\":1800,\"delay\":600},\"pattern\":{\"enable\":false,\"keywords\":[]}},\"preCheck\":{\"gpuComputationPrecision\":{\"enable\":false},\"gpuFlops\":{\"enable\":false},\"rdma\":{\"enable\":false},\"nvlink\":{\"enable\":false},\"pcie\":{\"enable\":false}}}"
    trainer.infra.shiyak.com/driver-master-image: ghcr.io/shiyak-infra/ft-driver-master:v0.0.2-8-gf767e25
    trainer.infra.shiyak.com/driver-worker-command: /workspace/worker --dcgm-mode=0
      --enable-dcgm=false --v=3
    trainer.infra.shiyak.com/driver-worker-image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
    trainer.infra.shiyak.com/precheck-command: python /mlp-bench/main.py
    trainer.infra.shiyak.com/precheck-image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
  labels:
    infra.shiyak.com/job-id: job-9ws19lbqpq
    infra.shiyak.com/owner: llm-user
    infra.shiyak.com/project-id: llm
  name: job-9ws19lbqpq
  namespace: project-llm
spec:
  policy:
    failurePolicy:
      maxRestarts: 3
    pytorchJobPolicy:
      nprocPerNode: "8"
    runPolicy:
      cleanAfterFinish: true
  replicaSpecs:
    pytorch-master:
      replicas: 1
      template:
        metadata:
          annotations:
            k8s.v1.cni.cncf.io/networks: network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca
          labels:
            infra.shiyak.com/job-id: job-9ws19lbqpq
            infra.shiyak.com/owner: llm-user
            infra.shiyak.com/project-id: llm
        spec:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: infra.shiyak.com/resource-role
                    operator: In
                    values:
                    - training
                  - key: infra.shiyak.com/project-id
                    operator: In
                    values:
                    - llm
                  - key: nvidia.com/gpu.product
                    operator: In
                    values:
                    - NVIDIA-H20Z
                  - key: infra.shiyak.com/node-type
                    operator: In
                    values:
                    - H20Z
                  - key: blacknode.infra.shiyak.com/blacklist
                    operator: NotIn
                    values:
                    - "true"
          containers:
          - command:
            - /bin/bash
            - -c
            - ""
            env:
            - name: WANDB_API_KEY
              value: ****************************************
            - name: NETWORK_PREFIX
              value: http://poc1-mlp.shiyak-office.com
            - name: MLP_CLUSTER
              value: poc1
            - name: MLP_PROJECT
              value: llm
            - name: MLP_USER
              value: llm-user
            - name: MLP_URL
              value: http://poc1-mlp.shiyak-office.com/console/dlc_job/job-9ws19lbqpq
            - name: MLP_ID
              value: job-9ws19lbqpq
            - name: MLP_NAME
              value: pretrain_moe_1b_3b_sparse0p5
            - name: MLP_TYPE
              value: job
            - name: MLP_WORKERS
              value: "64"
            - name: MLP_GPUS
              value: "512"
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: HOST_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            image: sea-hub.shiyak-office.com/nvcr.io-proxy/nvidia/nemo:25.02.rc5
            name: pytorch
            resources:
              limits:
                cpu: "171"
                memory: 1992294Mi
                nvidia.com/gpu: "8"
                rdma/hca: "8"
              requests:
                cpu: "153"
                memory: 1782579Mi
                nvidia.com/gpu: "8"
                rdma/hca: "8"
            securityContext:
              capabilities:
                add:
                - IPC_LOCK
                - SYS_NICE
            volumeMounts:
            - mountPath: /dev/shm
              name: dshm
          restartPolicy: Never
          volumes:
          - emptyDir:
              medium: Memory
              sizeLimit: 1Ti
            name: dshm
    pytorch-worker:
      replicas: 63
      template:
        metadata:
          annotations:
            k8s.v1.cni.cncf.io/networks: network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca
          labels:
            infra.shiyak.com/job-id: job-9ws19lbqpq
            infra.shiyak.com/owner: llm-user
            infra.shiyak.com/project-id: llm
        spec:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: infra.shiyak.com/resource-role
                    operator: In
                    values:
                    - training
                  - key: infra.shiyak.com/project-id
                    operator: In
                    values:
                    - llm
                  - key: nvidia.com/gpu.product
                    operator: In
                    values:
                    - NVIDIA-H20Z
                  - key: infra.shiyak.com/node-type
                    operator: In
                    values:
                    - H20Z
                  - key: blacknode.infra.shiyak.com/blacklist
                    operator: NotIn
                    values:
                    - "true"
          containers:
          - command:
            - /bin/bash
            - -c
            - ""
            env:
            - name: WANDB_API_KEY
              value: ****************************************
            - name: NETWORK_PREFIX
              value: http://poc1-mlp.shiyak-office.com
            - name: MLP_CLUSTER
              value: poc1
            - name: MLP_PROJECT
              value: llm
            - name: MLP_USER
              value: llm-user
            - name: MLP_URL
              value: http://poc1-mlp.shiyak-office.com/console/dlc_job/job-9ws19lbqpq
            - name: MLP_ID
              value: job-9ws19lbqpq
            - name: MLP_NAME
              value: pretrain_moe_1b_3b_sparse0p5
            - name: MLP_TYPE
              value: job
            - name: MLP_WORKERS
              value: "64"
            - name: MLP_GPUS
              value: "512"
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: HOST_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            image: sea-hub.shiyak-office.com/nvcr.io-proxy/nvidia/nemo:25.02.rc5
            name: pytorch
            resources:
              limits:
                cpu: "171"
                memory: 1992294Mi
                nvidia.com/gpu: "8"
                rdma/hca: "8"
              requests:
                cpu: "153"
                memory: 1782579Mi
                nvidia.com/gpu: "8"
                rdma/hca: "8"
            securityContext:
              capabilities:
                add:
                - IPC_LOCK
                - SYS_NICE
            volumeMounts:
            - mountPath: /dev/shm
              name: dshm
          restartPolicy: Never
          volumes:
          - emptyDir:
              medium: Memory
              sizeLimit: 1Ti
            name: dshm
  type: PyTorchJob
status:
  conditions:
  - lastTransitionTime: "2025-07-15T03:53:11Z"
    message: job is pending
    reason: JobPending
    status: "True"
    type: Pending
  - lastTransitionTime: "2025-07-15T05:53:11Z"
    message: job is running
    reason: JobRunning
    status: "True"
    type: Running
  startTime: "2025-07-15T05:53:11Z"
  status: Running     