apiVersion: v1
kind: Pod
metadata:
  annotations:
    k8s.v1.cni.cncf.io/networks: network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca
    scheduling.k8s.io/group-name: job-nfc9y8fjev-baidu-0711-deepep
    volcano.sh/task-spec: worker
  creationTimestamp: "2025-07-15T06:38:12Z"
  labels:
    infra.shiyak.com/job-id: job-nfc9y8fjev
    infra.shiyak.com/owner: zuti.he
    infra.shiyak.com/project-id: infra
    training.kubeflow.org/job-name: job-nfc9y8fjev-baidu-0711-deepep
    training.kubeflow.org/operator-name: deepspeed-controller
    training.kubeflow.org/replica-index: "2"
    training.kubeflow.org/replica-type: worker
  name: job-nfc9y8fjev-baidu-0711-deepep-worker-2
  namespace: project-infra
  ownerReferences:
  - apiVersion: kubeflow.org/v1
    blockOwnerDeletion: true
    controller: true
    kind: DeepSpeedJob
    name: job-nfc9y8fjev-baidu-0711-deepep
    uid: d3d6311d-948c-484f-b525-0e12592cd886
  resourceVersion: "10719875"
  uid: 3efe03ff-3274-4d7f-a7d6-3eab36844e55
spec:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: nvidia.com/gpu.product
            operator: In
            values:
            - NVIDIA-H20Z
  containers:
  - command:
    - /bin/bash
    - -c
    - |
      echo Bootstrap worker ...
      if [ ! -x /usr/sbin/sshd ]; then
          echo /usr/sbin/sshd not found, installing ...
          DEBIAN_FRONTEND=noninteractive apt-get -y update > /dev/null
          DEBIAN_FRONTEND=noninteractive apt-get -y install openssh-server > /dev/null
          mkdir -p /run/sshd
      fi
      echo Launch sshd ...
      exec -a /usr/sbin/sshd /usr/sbin/sshd -D -e
    env:
    - name: MLP_CLUSTER
      value: hb
    - name: MLP_PROJECT
      value: infra
    - name: MLP_USER
      value: zuti.he
    - name: NCCL_DEBUG
      value: WARN
    - name: NCCL_SOCKET_IFNAME
      value: eth0
    - name: NCCL_IB_QPS_PER_CONNECTION
      value: "4"
    - name: NVIDIA_GDRCOPY
      value: enabled
    - name: NCCL_IB_HCA
      value: =mlx5_1,mlx5_2,mlx5_3,mlx5_4,mlx5_5,mlx5_6,mlx5_7,mlx5_8
    - name: NCCL_IB_TC
      value: "162"
    - name: NVSHMEM_IB_TRAFFIC_CLASS
      value: "130"
    - name: NVSHMEM_HCA_LIST
      value: mlx5_1:1,mlx5_2:1,mlx5_3:1,mlx5_4:1,mlx5_5:1,mlx5_6:1,mlx5_7:1,mlx5_8:1
    - name: UCX_TLS
      value: tcp
    - name: UCX_NET_DEVICES
      value: eth0
    - name: TORCH_CPP_LOG_LEVEL
      value: INFO
    - name: TORCH_DISTRIBUTED_DEBUG
      value: INFO
    - name: RAY_COLOR_PREFIX
      value: "0"
    image: registry.baidubce.com/ai-native-dev/aixl:latest
    imagePullPolicy: Always
    name: deepspeed
    ports:
    - containerPort: 22
      name: ssh
      protocol: TCP
    - containerPort: 9009
      name: deepspeed-port
      protocol: TCP
    resources:
      limits:
        nvidia.com/gpu: "8"
        rdma/hca: "8"
      requests:
        nvidia.com/gpu: "8"
        rdma/hca: "8"
    securityContext:
      capabilities:
        add:
        - IPC_LOCK
        - SYS_NICE
    terminationMessagePath: /dev/termination-log
    terminationMessagePolicy: File
    volumeMounts:
    - mountPath: /mnt/project
      name: host-path-project
    - mountPath: /mnt/personal
      name: host-path-personal
    - mountPath: /mnt/share
      name: host-path-share
    - mountPath: /dev/shm
      name: dshm
    - mountPath: /root/.ssh
      name: ssh-config
      subPath: .ssh
    - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
      name: kube-api-access-rzrxd
      readOnly: true
  dnsPolicy: ClusterFirst
  enableServiceLinks: false
  nodeName: **********
  preemptionPolicy: PreemptLowerPriority
  priority: 0
  restartPolicy: Never
  schedulerName: volcano
  securityContext: {}
  serviceAccount: default
  serviceAccountName: default
  terminationGracePeriodSeconds: 30
  tolerations:
  - effect: NoExecute
    key: node.kubernetes.io/not-ready
    operator: Exists
    tolerationSeconds: 300
  - effect: NoExecute
    key: node.kubernetes.io/unreachable
    operator: Exists
    tolerationSeconds: 300
  volumes:
  - hostPath:
      path: /mnt/pfs/project/project-infra
      type: Directory
    name: host-path-project
  - hostPath:
      path: /mnt/pfs/personal/zuti.he
      type: Directory
    name: host-path-personal
  - hostPath:
      path: /mnt/pfs/share
      type: Directory
    name: host-path-share
  - emptyDir:
      medium: Memory
      sizeLimit: 1Ti
    name: dshm
  - name: ssh-config
    secret:
      defaultMode: 256
      items:
      - key: id_rsa
        path: .ssh/id_rsa
      - key: id_rsa.pub
        path: .ssh/authorized_keys
      - key: config
        path: .ssh/config
      secretName: job-nfc9y8fjev-baidu-0711-deepep-ssh-config
  - name: kube-api-access-rzrxd
    projected:
      defaultMode: 420
      sources:
      - serviceAccountToken:
          expirationSeconds: 3607
          path: token
      - configMap:
          items:
          - key: ca.crt
            path: ca.crt
          name: kube-root-ca.crt
      - downwardAPI:
          items:
          - fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
            path: namespace
status:
  conditions:
  - lastProbeTime: null
    lastTransitionTime: "2025-07-15T06:39:00Z"
    status: "True"
    type: PodReadyToStartContainers
  - lastProbeTime: null
    lastTransitionTime: "2025-07-15T06:38:52Z"
    status: "True"
    type: Initialized
  - lastProbeTime: null
    lastTransitionTime: "2025-07-15T06:39:00Z"
    status: "True"
    type: Ready
  - lastProbeTime: null
    lastTransitionTime: "2025-07-15T06:39:00Z"
    status: "True"
    type: ContainersReady
  - lastProbeTime: null
    lastTransitionTime: "2025-07-15T06:38:48Z"
    status: "True"
    type: PodScheduled
  containerStatuses:
  - containerID: containerd://0d12685ba0b078c9e8c55079393d0c0dfbe759effd17bbc311d241c97261a9ea
    image: registry.baidubce.com/ai-native-dev/aixl:latest
    imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
    lastState: {}
    name: deepspeed
    ready: true
    restartCount: 0
    started: true
    state:
      running:
        startedAt: "2025-07-15T06:38:59Z"
  hostIP: **********
  hostIPs:
  - ip: **********
  phase: Running
  podIP: ************
  podIPs:
  - ip: ************
  qosClass: BestEffort
  startTime: "2025-07-15T06:38:52Z"
