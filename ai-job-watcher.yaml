apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: "3"
  labels:
    control-plane: ai-job-watcher
  name: ai-job-watcher
  namespace: cce-system
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      control-plane: ai-job-watcher
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        control-plane: ai-job-watcher
    spec:
      containers:
        - image: registry.baidubce.com/cce-service-dev/cce-ai-job-watcher:yrq-test4
          imagePullPolicy: Always
          name: watcher
          args:
            - --cluster-id=cce-xrg955qz
            - --region=cd
            - --kubeconfig=/etc/kubeconfig/kubeconfig
            - --report-url=http://api.nsc.baidu.com/aipod/2.0/platform/jobs
            - --app-name=aipod-cce
            - --token=FUSYzrC8gcsvcISJoMYEGH9Unkw
            - --log-level=info
            - --resync-period=30s
          resources:
            limits:
              cpu: 100m
              memory: 30Mi
            requests:
              cpu: 100m
              memory: 20Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - name: kubeconfig
              mountPath: /etc/kubeconfig
              readOnly: true
      restartPolicy: Always
      terminationGracePeriodSeconds: 10
      imagePullSecrets:
        - name: ccr-registry-secret
      serviceAccountName: ai-job-watcher
      volumes:
        - name: kubeconfig
          configMap:
            name: ai-job-watcher-kubeconfig
            defaultMode: 0644