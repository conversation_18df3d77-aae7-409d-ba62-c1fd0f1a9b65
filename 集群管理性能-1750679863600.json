{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 483, "iteration": *************, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 100, "panels": [], "title": "集群概览", "type": "row"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "异常集群数量"}, "properties": [{"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}]}]}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 1}, "id": 101, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "text": {}, "textMode": "value_and_name"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "count(cce_cluster_create_timestamp{accountID=~\"$accountID\",region=~\"$region\"})", "format": "time_series", "instant": true, "interval": "", "legendFormat": "集群总数", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "count(cce_cluster_phase{accountID=~\"$accountID\",phase=~\"create_failed|delete_failed|upgrade_failed\",region=~\"$region\"} == 1)", "hide": false, "instant": true, "interval": "", "legendFormat": "异常集群数量", "refId": "B"}, {"exemplar": true, "expr": "count(cce_cluster_phase{accountID=~\"$accountID\",phase=\"running\",region=~\"$region\"} == 1)", "hide": false, "instant": true, "interval": "", "legendFormat": "运行中集群数量", "refId": "C"}], "timeFrom": null, "timeShift": null, "title": "集群数量统计", "transformations": [], "type": "stat"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [], "max": 1, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 0.95}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 1}, "id": 102, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(cce_cluster_phase{accountID=~\"$accountID\",phase=\"running\",region=~\"$region\"}) / sum(cce_cluster_phase{accountID=~\"$accountID\",region=~\"$region\"})", "format": "time_series", "instant": false, "interval": "", "legendFormat": "集群健康度", "queryType": "randomWalk", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "集群健康度", "transformations": [], "type": "stat"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "id": 103, "options": {"displayLabels": [], "legend": {"displayMode": "table", "placement": "right", "values": ["value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "count(cce_cluster_phase{accountID=~\"$accountID\",region=~\"$region\"} == 1) by (phase)", "interval": "", "legendFormat": "{{phase}}", "queryType": "randomWalk", "refId": "A"}], "title": "集群状态分布", "type": "piechart"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}, "id": 104, "options": {"displayLabels": [], "legend": {"displayMode": "table", "placement": "right", "values": ["value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "count(cce_cluster_create_timestamp{accountID=~\"$accountID\",region=~\"$region\",masterType=\"managedPro\"}) or 0", "interval": "", "legendFormat": "新托管", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "count(cce_cluster_create_timestamp{accountID=~\"$accountID\",region=~\"$region\",masterType=\"managed\"}) or 0", "hide": false, "interval": "", "legendFormat": "老托管", "refId": "B"}, {"exemplar": true, "expr": "count(cce_cluster_create_timestamp{accountID=~\"$accountID\",region=~\"$region\",masterType=~\"custom|containerizedCustom\"}) or 0", "hide": false, "interval": "", "legendFormat": "独立", "refId": "C"}, {"exemplar": true, "expr": "count(cce_cluster_create_timestamp{accountID=~\"$accountID\",region=~\"$region\",masterType=\"serverless\"}) or 0", "hide": false, "interval": "", "legendFormat": "Serverless", "refId": "D"}, {"exemplar": true, "expr": "count(cce_cluster_create_timestamp{accountID=~\"$accountID\",region=~\"$region\",masterType=\"edge\"}) or 0", "hide": false, "interval": "", "legendFormat": "边缘", "refId": "E"}], "title": "主节点类型分布", "type": "piechart"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 105, "panels": [], "title": "集群性能监控", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 19}, "hiddenSeries": false, "id": 106, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(cce_cluster_step_cost_seconds{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) by (step)", "instant": false, "interval": "", "legendFormat": "{{step}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "集群步骤耗时", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xAxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yAxes": [{"$$hashKey": "object:1286", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1287", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yAxis": {"align": false, "alignLevel": null}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 3}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "耗时"}, "properties": [{"id": "unit", "value": "s"}, {"id": "color", "value": {"mode": "continuous-RdYlGr"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "重试次数"}, "properties": [{"id": "color", "value": {"mode": "thresholds"}}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 3}]}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 19}, "id": 107, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "重试次数"}]}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(cce_cluster_step_cost_seconds{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) by (name,step)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "sum(cce_cluster_step_retry_count{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) by (name,step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "集群步骤重试统计", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value #A": "耗时", "Value #B": "重试次数"}}}], "type": "table"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "percentunit"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 27}, "hiddenSeries": false, "id": 108, "legend": {"avg": false, "current": true, "max": false, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "avg(cce_cluster_infra_status{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) by (clusterID)", "instant": false, "interval": "", "legendFormat": "基础设施就绪率 - {{clusterID}}", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "avg(cce_cluster_access_success{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) by (clusterID)", "hide": false, "instant": false, "interval": "", "legendFormat": "API访问成功率 - {{clusterID}}", "refId": "B"}], "thresholds": [{"colorMode": "critical", "fill": true, "fillColorRGBA": {"a": 0.5, "b": 0, "g": 0, "r": 255}, "op": "lt", "value": 0.95, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "集群状态监控", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xAxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yAxes": [{"$$hashKey": "object:566", "format": "percentunit", "label": null, "logBase": 1, "max": 1, "min": 0, "show": true}, {"$$hashKey": "object:567", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yAxis": {"align": false, "alignLevel": null}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 27}, "hiddenSeries": false, "id": 109, "legend": {"avg": false, "current": true, "max": false, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(cce_cluster_retry_count{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\"}) by (clusterID)", "instant": false, "interval": "", "legendFormat": "重试次数 - {{clusterID}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [{"colorMode": "critical", "fill": true, "fillColorRGBA": {"a": 0.5, "b": 0, "g": 127, "r": 255}, "op": "gt", "value": 3, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "集群重试次数监控", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xAxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yAxes": [{"$$hashKey": "object:566", "format": "none", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"$$hashKey": "object:567", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yAxis": {"align": false, "alignLevel": null}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 35}, "id": 20, "panels": [], "title": "集群各阶段耗时趋势", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 0, "y": 36}, "hiddenSeries": false, "id": 21, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeat": "clusterStep", "scopedVars": {"clusterStep": {"selected": false, "text": "CreateClusterEIP", "value": "CreateClusterEIP"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "集群 $clusterStep 阶段耗时统计", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xAxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yAxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yAxis": {"align": false, "alignLevel": null}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 6, "y": 36}, "hiddenSeries": false, "id": 110, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeatIteration": *************, "repeatPanelId": 21, "scopedVars": {"clusterStep": {"selected": false, "text": "EnsureSystemInited", "value": "EnsureSystemInited"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "集群 $clusterStep 阶段耗时统计", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xAxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yAxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yAxis": {"align": false, "alignLevel": null}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 12, "y": 36}, "hiddenSeries": false, "id": 111, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeatIteration": *************, "repeatPanelId": 21, "scopedVars": {"clusterStep": {"selected": false, "text": "WaitCCEGatewayToken", "value": "WaitCCEGatewayToken"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "集群 $clusterStep 阶段耗时统计", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xAxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yAxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yAxis": {"align": false, "alignLevel": null}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 18, "y": 36}, "hiddenSeries": false, "id": 112, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeatIteration": *************, "repeatPanelId": 21, "scopedVars": {"clusterStep": {"selected": false, "text": "CreateCertificateAuthority", "value": "CreateCertificateAuthority"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "集群 $clusterStep 阶段耗时统计", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xAxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yAxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yAxis": {"align": false, "alignLevel": null}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 0, "y": 45}, "hiddenSeries": false, "id": 113, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeatIteration": *************, "repeatPanelId": 21, "scopedVars": {"clusterStep": {"selected": false, "text": "WaitMasterInfrastructure", "value": "WaitMasterInfrastructure"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "集群 $clusterStep 阶段耗时统计", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xAxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yAxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yAxis": {"align": false, "alignLevel": null}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 6, "y": 45}, "hiddenSeries": false, "id": 114, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeatIteration": *************, "repeatPanelId": 21, "scopedVars": {"clusterStep": {"selected": false, "text": "CreateClusterInfrastructure", "value": "CreateClusterInfrastructure"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "集群 $clusterStep 阶段耗时统计", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xAxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yAxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yAxis": {"align": false, "alignLevel": null}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 12, "y": 45}, "hiddenSeries": false, "id": 115, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeatIteration": *************, "repeatPanelId": 21, "scopedVars": {"clusterStep": {"selected": false, "text": "DeployK8SPlugin", "value": "DeployK8SPlugin"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "集群 $clusterStep 阶段耗时统计", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xAxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yAxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yAxis": {"align": false, "alignLevel": null}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 18, "y": 45}, "hiddenSeries": false, "id": 116, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeatIteration": *************, "repeatPanelId": 21, "scopedVars": {"clusterStep": {"selected": false, "text": "WaitAPIServerAccess", "value": "WaitAPIServerAccess"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "集群 $clusterStep 阶段耗时统计", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xAxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yAxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yAxis": {"align": false, "alignLevel": null}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 0, "y": 54}, "hiddenSeries": false, "id": 117, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeatIteration": *************, "repeatPanelId": 21, "scopedVars": {"clusterStep": {"selected": false, "text": "CreateExternalETCD", "value": "CreateExternalETCD"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "集群 $clusterStep 阶段耗时统计", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xAxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yAxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yAxis": {"align": false, "alignLevel": null}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 6, "y": 54}, "hiddenSeries": false, "id": 118, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeatIteration": *************, "repeatPanelId": 21, "scopedVars": {"clusterStep": {"selected": false, "text": "CreateClusterLB", "value": "CreateClusterLB"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "集群 $clusterStep 阶段耗时统计", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xAxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yAxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yAxis": {"align": false, "alignLevel": null}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}]}]}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 12, "y": 54}, "hiddenSeries": false, "id": 119, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "repeatIteration": *************, "repeatPanelId": 21, "scopedVars": {"clusterStep": {"selected": false, "text": "EnsureK8SServiceRunning", "value": "EnsureK8SServiceRunning"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MAX", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "AVG", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P99", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "MIN", "refId": "最小总耗时"}, {"exemplar": true, "expr": "quantile(0.9, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by ()", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "P90", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "集群 $clusterStep 阶段耗时统计", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [], "type": "graph", "xAxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yAxes": [{"$$hashKey": "object:488", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:489", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yAxis": {"align": false, "alignLevel": null}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 63}, "id": 22, "panels": [], "title": "集群各阶段耗时统计", "type": "row"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "最大耗时"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 60}, {"color": "red", "value": 300}]}}, {"id": "custom.width", "value": 76}]}, {"matcher": {"id": "byRegexp", "options": ".*(耗时|延迟)"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 60}, {"color": "red", "value": 300}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "clusterID"}, "properties": [{"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "step"}, "properties": [{"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "byRegexp", "options": ".*时间"}, "properties": [{"id": "unit", "value": "dateTimeAsLocalNoDateIfToday"}, {"id": "custom.width", "value": 150}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "P99 耗时"}, "properties": [{"id": "custom.width", "value": 83}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "最小耗时"}, "properties": [{"id": "custom.width", "value": 76}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "平均耗时"}, "properties": [{"id": "custom.width", "value": 80}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "最大重试次数"}, "properties": [{"id": "custom.width", "value": 109}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 2}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "集群ID"}, "properties": [{"id": "custom.width", "value": 123}, {"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "集群部署阶段"}, "properties": [{"id": "custom.width", "value": 207}, {"id": "custom.filterable", "value": true}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 64}, "id": 23, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "最大耗时"}]}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "max(cce_cluster_step_cost_seconds{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by (clusterID, step)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "最大单次耗时"}, {"exemplar": true, "expr": "max(cce_cluster_step_retry_count{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by (clusterID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最大重试次数"}, {"exemplar": true, "expr": "max(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by (clusterID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最大总耗时"}, {"exemplar": true, "expr": "avg(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by (clusterID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "平均总耗时"}, {"exemplar": true, "expr": "quantile(0.99, cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by (clusterID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "P99 总耗时"}, {"exemplar": true, "expr": "min(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"} - cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by (clusterID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最小总耗时"}, {"exemplar": true, "expr": "1000*min(cce_cluster_step_start_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by (clusterID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最早开始时间"}, {"exemplar": true, "expr": "1000*max(cce_cluster_step_finish_timestamp{accountID=~\"$accountID\",clusterID=~\"$clusterID\",region=~\"$region\",step=~\"$clusterStep\"}) by (clusterID, step)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "最晚结束时间"}], "timeFrom": null, "timeShift": null, "title": "集群各阶段耗时统计&重试次数", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Value #最大单次耗时": true}, "indexByName": {"Time": 0, "Value #P99 总耗时": 5, "Value #平均总耗时": 4, "Value #最大单次耗时": 7, "Value #最大总耗时": 3, "Value #最大重试次数": 6, "Value #最小总耗时": 8, "Value #最早开始时间": 9, "Value #最晚结束时间": 10, "clusterID": 1, "step": 2}, "renameByName": {"Value #P99 总耗时": "P99 耗时", "Value #平均总耗时": "平均耗时", "Value #最大单次耗时": "最大单次耗时", "Value #最大总耗时": "最大耗时", "Value #最大重试次数": "最大重试次数", "Value #最小总耗时": "最小耗时", "Value #最早开始时间": "最早开始时间", "Value #最晚结束时间": "最晚结束时间", "clusterID": "集群ID", "step": "集群部署阶段"}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": true, "field": "最大耗时"}]}}], "type": "table"}], "schemaVersion": 27, "style": "dark", "tags": ["CCE", "集群管理", "性能监控"], "templating": {"list": [{"current": {"selected": false, "text": "cce-vm-cluster", "value": "cce-vm-cluster"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": "数据源", "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": ".+", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "${datasource}", "definition": "label_values(cce_cluster_create_timestamp,region)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "区域", "multi": true, "name": "region", "options": [], "query": {"query": "label_values(cce_cluster_create_timestamp,region)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".+", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "${datasource}", "definition": "label_values(cce_cluster_create_timestamp{region=~\"$region\"},accountID)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "账号ID", "multi": true, "name": "accountID", "options": [], "query": {"query": "label_values(cce_cluster_create_timestamp{region=~\"$region\"},accountID)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".+", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "${datasource}", "definition": "label_values(cce_cluster_create_timestamp{accountID=~\"$accountID\",region=~\"$region\"},clusterID)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "集群ID", "multi": true, "name": "clusterID", "options": [], "query": {"query": "label_values(cce_cluster_create_timestamp{accountID=~\"$accountID\",region=~\"$region\"},clusterID)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".+", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "${datasource}", "definition": "label_values(cce_cluster_step_cost_seconds{accountID=~\"$accountID\",region=~\"$region\",clusterID=~\"$clusterID\"},step)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "集群步骤", "multi": true, "name": "clusterStep", "options": [], "query": {"query": "label_values(cce_cluster_step_cost_seconds{accountID=~\"$accountID\",region=~\"$region\",clusterID=~\"$clusterID\"},step)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "集群管理性能", "uid": "cluster-mgmt-perf", "version": 2}