apiVersion: v1
kind: Pod
metadata:
  annotations:
    BAIDU_COM_DEVICE_ASSIGNED: "false"
    BAIDU_COM_DEVICE_ASSUME_TIME: "1752076287639418893"
    BAIDU_COM_DEVICE_CORE_DEVICE: "0"
    BAIDU_COM_DEVICE_CORE_POD: "0"
    BAIDU_COM_DEVICE_DECODER_DEVICE: "0"
    BAIDU_COM_DEVICE_DECODER_POD: "0"
    BAIDU_COM_DEVICE_ENCODER_DEVICE: "0"
    BAIDU_COM_DEVICE_ENCODER_POD: "0"
    BAIDU_COM_DEVICE_IDX: 4,5,6,7,0,1,2,3
    BAIDU_COM_DEVICE_MEM_DEVICE: "0"
    BAIDU_COM_DEVICE_MEM_POD: "0"
    VOLCANO_END_BIND_POD_TIME: "2025-07-09T15:51:28Z"
    VOLCANO_START_BIND_POD_TIME: "2025-07-09T15:51:27Z"
    cce-workload-kind: Trainer
    cce-workload-name: job-t4l5pcuydi
    k8s.v1.cni.cncf.io/networks: network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca
    scheduling.k8s.io/group-name: job-t4l5pcuydi
  creationTimestamp: "2025-07-09T15:51:24Z"
  labels:
    infra.shiyak.com/job-id: job-t4l5pcuydi
    infra.shiyak.com/owner: llm-user
    infra.shiyak.com/project-id: llm
    trainer.infra.shiyak.com/managed-by: trainer-controller
    trainer.infra.shiyak.com/name: job-t4l5pcuydi
    trainer.infra.shiyak.com/replica-index: "0"
    trainer.infra.shiyak.com/replica-type: pytorch-worker
    trainer.infra.shiyak.com/restart-attempt: "0"
    trainer.infra.shiyak.com/type: PyTorchJob
  name: job-t4l5pcuydi-worker-0
  namespace: project-llm
  ownerReferences:
  - apiVersion: trainer.infra.shiyak.com/v1alpha1
    blockOwnerDeletion: true
    controller: true
    kind: Trainer
    name: job-t4l5pcuydi
    uid: 557d17a4-5463-44d6-a683-41fb10050fce
  resourceVersion: "230294496"
  uid: 93f72abb-1b29-47ac-86df-9e912e0ae8e0
spec:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: infra.shiyak.com/resource-role
            operator: In
            values:
            - training
          - key: infra.shiyak.com/project-id
            operator: In
            values:
            - llm
          - key: nvidia.com/gpu.product
            operator: In
            values:
            - NVIDIA-H20Z
          - key: infra.shiyak.com/node-type
            operator: In
            values:
            - H20Z
          - key: blacknode.infra.shiyak.com/blacklist
            operator: NotIn
            values:
            - "true"
          - key: aihc.baidu.com/dedicated-pool
            operator: DoesNotExist
  containers:
  - command:
    - /bin/bash
    - -c
    - "{\n#!/bin/bash\nWORK_DIR=$(pwd -P)\nif [[ \"$HOSTNAME\" == *\"notebook\"* ]];
      then\n    pkill -9 -f train\n    pkill -9 -f wandb\n    export WANDB_MODE=\"disabled\"\nfi\n\n################
      environment ################\nexport HYDRA_FULL_ERROR=1\nexport CUDA_DEVICE_MAX_CONNECTIONS=1\nexport
      CUDNN_LOGERR_DBG=1 \nexport CUDNN_LOGDEST_DBG=stderr\nexport enable_profiling_tool=0\nexport
      ANC_DISABLE_LOGGING=1\nexport ANC_DELAY_LOSS_REDUCE=1\nexport ANC_TRAIN_LOADER_WORKER=1\nexport
      ENABLE_MOE_LOG=1\nexport ANC_SET_VAL_REPEAT=1\nexport MEGATRON_LOG_BUCKETS=0\n\n\n################
      config ################\nREBUILD=true\nPROJECT_NAME=pretrain_moe_1b_3b_attn2\nCKPT_DIR=/mnt/project/llm/output/wangya/pretraining_model_research\n\nREPO_BRANCH_PAIRS=(\n
      \   \"/mnt/project/llm/users/wangya/project/Megatron-LM\" \"wangya/deepseek_main_0603\"
      \"937c960b\"    # \"latest\" for the latest commit\n    \"/mnt/project/llm/users/wangya/project/NeMo\"
      \"wangya/deepspeek_debug\" \"3e3292a14\"\n    \"/mnt/project/llm/users/wangya/project/Ocean\"
      \"wangya/main\" \"3ba9c94\"\n)\n\nBASE_YAML_CONFIG=(\n    --config_file ./Ocean/scripts/yaml_configs/moe_1b_3b.yaml\n)\n\nMODEL_ARGS=(\n
      \   --num_attention_heads 32\n    --moe_ffn_hidden_size 896\n    --ffn_hidden_size
      896\n    --moe_shared_expert_intermediate_size 1792\n    --moe_router_topk 6\n
      \   --num_moe_experts 36\n)\n\nPARALLEL_ARG=(\n)\n\nTOKENIZER_ARGS=(\n)\n\nDATA_ARGS=(\n)\n\nTRAINING_ARGS=(\n
      \   --global_batch_size 1024\n)\n\nVALIDATION_ARGS=(\n    --validation_interval
      1000\n)\n\nLOGGING_ARGS=(\n    --project_name $PROJECT_NAME\n    --model_ckpt_dir
      $CKPT_DIR\n    --wandb_project $(basename $CKPT_DIR)\n)\n\n\n################
      pip install & ssh-key ################\nif [ \"$REBUILD\" = true ]; then\n    #pip\n
      \   pip install --no-index --find-links=/mnt/project/llm/users/wangya/project/pip_package/
      protobuf==3.20.*\n    python3 -m pip install --no-index --find-links=/mnt/project/llm/users/wangya/project/pip_package/
      leptonai\n    python3 -m pip install --no-index --find-links=/mnt/project/llm/users/wangya/project/pip_package/
      toml\n    python3 -m pip install --no-index --find-links=/mnt/project/llm/users/wangya/project/pip_package/
      --upgrade multi-storage-client==0.21.0\n    python3 -m pip install --no-index
      --find-links=/mnt/project/llm/users/wangya/project/pip_package/ anc==0.4.0\n
      \   # ssh\n    mkdir -p ~/.ssh/\n    cp /mnt/project/llm/users/wangya/public_ssh/id_rsa
      ~/.ssh/id_rsa\n    cp /mnt/project/llm/users/wangya/public_ssh/id_rsa.pub ~/.ssh/id_rsa.pub\n
      \   # git config\n    git config --global --add safe.directory '*'\nfi\n\nfor
      (( i=0; i<${#REPO_BRANCH_PAIRS[@]}; i+=3 )); do\n    REPO_URL=\"${REPO_BRANCH_PAIRS[i]}\"\n
      \   BRANCH_NAME=\"${REPO_BRANCH_PAIRS[i+1]}\"\n    COMMIT_NAME=\"${REPO_BRANCH_PAIRS[i+2]}\"\n
      \   REPO_NAME=$(basename \"$REPO_URL\" .git)\n    NORMALIZED_NEW_PATH=\"${WORK_DIR}/${REPO_NAME}\"\n\n
      \   if [ \"$REBUILD\" = true ]; then\n        echo -e \"\\ndownloading: ${REPO_URL}\"\n
      \       echo \"  ↳ to: ${REPO_NAME}\"\n        echo \"  ↳ branch: ${BRANCH_NAME}\"\n\n
      \       rm -rf \"$NORMALIZED_NEW_PATH\"\n\n        if git clone --branch \"$BRANCH_NAME\"
      --single-branch \"$REPO_URL\" \"$NORMALIZED_NEW_PATH\"; then\n\n            LATEST_COMMIT=$(git
      rev-parse HEAD)\n            echo \"✅ clone succeed: ${REPO_NAME}\"\n\n            if
      [ \"$COMMIT_NAME\" != \"latest\" ]; then\n                cd \"$NORMALIZED_NEW_PATH\"
      || exit\n                echo \"  ↳ resetting to commit: ${COMMIT_NAME}\"\n
      \               if git reset --hard \"$COMMIT_NAME\"; then\n                    echo
      \"✅ reset succeed: ${REPO_NAME} @ ${COMMIT_NAME}\"\n                else\n                    echo
      \"❌ reset failed: commit ${COMMIT_NAME} may not exist in branch ${BRANCH_NAME}\"\n
      \               fi\n                cd \"$WORK_DIR\"\n            else\n                echo
      \"  ↳ at latest commit: ${LATEST_COMMIT}\"\n            fi\n\n        else\n
      \           echo \"❌ clone error ${REPO_NAME}, please check whether repo_name
      or branch_name is wrong!\"\n        fi\n    fi\n\n    # PYTHONPATH handling\n
      \   if [[ \":${PYTHONPATH}:\" != *\"${NORMALIZED_NEW_PATH}:\"* ]]; then\n        export
      PYTHONPATH=\"${NORMALIZED_NEW_PATH}\":$PYTHONPATH\n        echo \"  ↳ Added
      to PYTHONPATH: '${NORMALIZED_NEW_PATH}'\"\n    else\n        echo \"  ↳ Already
      in PYTHONPATH: '${NORMALIZED_NEW_PATH}' (skipped)\"\n    fi\ndone\n\n\n##############
      training env ################\nCOMMA_COUNT=$(echo \"$NVIDIA_VISIBLE_DEVICES\"
      | grep -o ',' | wc -l)\nexport PET_NPROC_PER_NODE=${PET_NPROC_PER_NODE:-$((COMMA_COUNT
      + 1))}\nexport PET_NNODES=${PET_NNODES:-1}\nexport PET_NODE_RANK=${PET_NODE_RANK:-0}\nexport
      PET_MASTER_ADDR=${PET_MASTER_ADDR:-$HOSTNAME}\nexport PET_MASTER_PORT=${PET_MASTER_PORT:-23456}\nexport
      ANC_DATA_IDX_DUMP_DIR=${CKPT_DIR}/${PROJECT_NAME}/dumpped_data\n\n\n################
      run training ################\ntorchrun --nproc_per_node=$PET_NPROC_PER_NODE
      \\\n    --nnodes=$PET_NNODES \\\n    --node_rank=$PET_NODE_RANK \\\n    --master_addr=$PET_MASTER_ADDR
      \\\n    --master_port=$PET_MASTER_PORT \\\n    ${WORK_DIR}/Ocean/scripts/nemo_2_scripts/launch_pretrain_model_training.py
      \\\n    --num_node $PET_NNODES \\\n    --num_gpus_per_node $PET_NPROC_PER_NODE
      \\\n    ${BASE_YAML_CONFIG[@]} \\\n    ${MODEL_ARGS[@]} \\\n    ${PARALLEL_ARG[@]}
      \\\n    ${TOKENIZER_ARGS[@]} \\\n    ${DATA_ARGS[@]} \\\n    ${TRAINING_ARGS[@]}
      \\\n    ${VALIDATION_ARGS[@]} \\\n    ${LOGGING_ARGS[@]}\n\n} 2>&1 | tee -a
      /data/log; exit ${PIPESTATUS[0]}"
    env:
    - name: MLP_CLUSTER
      value: poc1
    - name: MLP_PROJECT
      value: llm
    - name: MLP_USER
      value: llm-user
    - name: NCCL_DEBUG
      value: WARN
    - name: NCCL_SOCKET_IFNAME
      value: eth0
    - name: NCCL_IB_QPS_PER_CONNECTION
      value: "4"
    - name: NVIDIA_GDRCOPY
      value: enabled
    - name: NCCL_IB_HCA
      value: =mlx5_1,mlx5_2,mlx5_3,mlx5_4,mlx5_5,mlx5_6,mlx5_7,mlx5_8
    - name: NCCL_IB_TC
      value: "162"
    - name: NVSHMEM_IB_TRAFFIC_CLASS
      value: "130"
    - name: NVSHMEM_HCA_LIST
      value: mlx5_1:1,mlx5_2:1,mlx5_3:1,mlx5_4:1,mlx5_5:1,mlx5_6:1,mlx5_7:1,mlx5_8:1
    - name: UCX_TLS
      value: tcp
    - name: UCX_NET_DEVICES
      value: eth0
    - name: TORCH_CPP_LOG_LEVEL
      value: INFO
    - name: TORCH_DISTRIBUTED_DEBUG
      value: INFO
    - name: RAY_COLOR_PREFIX
      value: "0"
    - name: NETWORK_PREFIX
      value: http://poc1-mlp.shiyak-office.com
    - name: WANDB_API_KEY
      value: ****************************************
    - name: MLP_URL
      value: http://poc1-mlp.shiyak-office.com/console/dlc_job/job-t4l5pcuydi
    - name: MLP_ID
      value: job-t4l5pcuydi
    - name: MLP_NAME
      value: pretrain_moe_1b_3b_attn2_clone1
    - name: MLP_TYPE
      value: job
    - name: MLP_WORKERS
      value: "64"
    - name: MLP_GPUS
      value: "512"
    - name: POD_NAME
      valueFrom:
        fieldRef:
          apiVersion: v1
          fieldPath: metadata.name
    - name: POD_IP
      valueFrom:
        fieldRef:
          apiVersion: v1
          fieldPath: status.podIP
    - name: NODE_NAME
      valueFrom:
        fieldRef:
          apiVersion: v1
          fieldPath: spec.nodeName
    - name: HOST_IP
      valueFrom:
        fieldRef:
          apiVersion: v1
          fieldPath: status.hostIP
    - name: PYTHONUNBUFFERED
      value: "1"
    - name: PET_MASTER_ADDR
      value: job-t4l5pcuydi-master-0
    - name: PET_MASTER_PORT
      value: "23456"
    - name: PET_NPROC_PER_NODE
      value: "8"
    - name: PET_NODE_RANK
      value: "1"
    - name: PET_NNODES
      value: "64"
    - name: MASTER_ADDR
      value: job-t4l5pcuydi-master-0
    - name: MASTER_PORT
      value: "23456"
    - name: WORLD_SIZE
      value: "512"
    - name: RANK
      value: "1"
    - name: NODE_RANK
      value: "1"
    image: sea-hub.shiyak-office.com/nvcr.io-proxy/nvidia/nemo:25.02.rc5
    imagePullPolicy: IfNotPresent
    name: pytorch
    resources:
      limits:
        cpu: "171"
        memory: 1992294Mi
        nvidia.com/gpu: "8"
        rdma/hca: "8"
      requests:
        cpu: "153"
        memory: 1782579Mi
        nvidia.com/gpu: "8"
        rdma/hca: "8"
    securityContext:
      capabilities:
        add:
        - IPC_LOCK
        - SYS_NICE
    terminationMessagePath: /dev/termination-log
    terminationMessagePolicy: File
    volumeMounts:
    - mountPath: /mnt/share
      name: host-path-share
    - mountPath: /mnt/project
      name: host-path-project
    - mountPath: /mnt/personal
      name: host-path-personal
    - mountPath: /dev/shm
      name: dshm
    - mountPath: /data
      name: log-data
    - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
      name: kube-api-access-94hw9
      readOnly: true
  dnsPolicy: ClusterFirst
  enableServiceLinks: false
  imagePullSecrets:
  - name: ghcr-secret
  initContainers:
  - command:
    - /bin/bash
    - -c
    - /workspace/worker --dcgm-mode=0 --enable-dcgm=false --v=3
    env:
    - name: DRIVER_MASTER_ADDR
      value: job-t4l5pcuydi-driver:8080
    - name: POD_NAME
      valueFrom:
        fieldRef:
          apiVersion: v1
          fieldPath: metadata.name
    - name: POD_IP
      valueFrom:
        fieldRef:
          apiVersion: v1
          fieldPath: status.podIP
    - name: NODE_NAME
      valueFrom:
        fieldRef:
          apiVersion: v1
          fieldPath: spec.nodeName
    - name: HOST_IP
      valueFrom:
        fieldRef:
          apiVersion: v1
          fieldPath: status.hostIP
    - name: LOG_PATH
      value: /data/log
    - name: LOG_PATTERNS
      value: world
    image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
    imagePullPolicy: IfNotPresent
    name: ft-driver-worker
    resources:
      limits:
        cpu: "2"
        memory: 4Gi
      requests:
        cpu: "1"
        memory: 2Gi
    restartPolicy: Always
    securityContext:
      privileged: true
    terminationMessagePath: /dev/termination-log
    terminationMessagePolicy: File
    volumeMounts:
    - mountPath: /data
      name: log-data
    - mountPath: /host/sys
      name: host-sys
      readOnly: true
    - mountPath: /host/proc
      name: host-proc
      readOnly: true
    - mountPath: /host/dev
      name: host-dev
      readOnly: true
    - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
      name: kube-api-access-94hw9
      readOnly: true
  - command:
    - /bin/bash
    - -c
    - python /mlp-bench/main.py
    env:
    - name: DRIVER_MASTER_ADDR
      value: job-t4l5pcuydi-driver:8080
    - name: POD_NAME
      valueFrom:
        fieldRef:
          apiVersion: v1
          fieldPath: metadata.name
    - name: POD_IP
      valueFrom:
        fieldRef:
          apiVersion: v1
          fieldPath: status.podIP
    - name: NODE_NAME
      valueFrom:
        fieldRef:
          apiVersion: v1
          fieldPath: spec.nodeName
    - name: HOST_IP
      valueFrom:
        fieldRef:
          apiVersion: v1
          fieldPath: status.hostIP
    - name: PYTHONUNBUFFERED
      value: "1"
    - name: PET_MASTER_ADDR
      value: job-t4l5pcuydi-master-0-precheck
    - name: PET_MASTER_PORT
      value: "12345"
    - name: PET_NPROC_PER_NODE
      value: "8"
    - name: PET_NODE_RANK
      value: "1"
    - name: PET_NNODES
      value: "64"
    - name: MASTER_ADDR
      value: job-t4l5pcuydi-master-0-precheck
    - name: MASTER_PORT
      value: "12345"
    - name: WORLD_SIZE
      value: "512"
    - name: RANK
      value: "1"
    - name: NODE_RANK
      value: "1"
    image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
    imagePullPolicy: IfNotPresent
    name: precheck
    resources:
      limits:
        nvidia.com/gpu: "8"
      requests:
        nvidia.com/gpu: "8"
    securityContext:
      privileged: true
    terminationMessagePath: /dev/termination-log
    terminationMessagePolicy: File
    volumeMounts:
    - mountPath: /host/sys
      name: host-sys
      readOnly: true
    - mountPath: /host/proc
      name: host-proc
      readOnly: true
    - mountPath: /host/dev
      name: host-dev
      readOnly: true
    - mountPath: /dev/shm
      name: precheck-shm
    - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
      name: kube-api-access-94hw9
      readOnly: true
  - command:
    - /bin/sh
    - -c
    - err=1; for i in $(seq 120); do if nslookup job-t4l5pcuydi-master-0; then err=0
      && break; fi; echo waiting for master; sleep 10; done; exit $err
    image: alpine:3.10
    imagePullPolicy: IfNotPresent
    name: init-pytorch
    resources: {}
    terminationMessagePath: /dev/termination-log
    terminationMessagePolicy: File
    volumeMounts:
    - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
      name: kube-api-access-94hw9
      readOnly: true
  nodeName: ***********
  preemptionPolicy: PreemptLowerPriority
  priority: 0
  restartPolicy: Never
  schedulerName: volcano
  securityContext: {}
  serviceAccount: default
  serviceAccountName: default
  terminationGracePeriodSeconds: 30
  tolerations:
  - effect: NoExecute
    key: node.kubernetes.io/not-ready
    operator: Exists
    tolerationSeconds: 300
  - effect: NoExecute
    key: node.kubernetes.io/unreachable
    operator: Exists
    tolerationSeconds: 300
  volumes:
  - hostPath:
      path: /mnt/pfs/share
      type: Directory
    name: host-path-share
  - hostPath:
      path: /mnt/pfs/project/project-llm
      type: Directory
    name: host-path-project
  - hostPath:
      path: /mnt/pfs/personal/llm-user
      type: Directory
    name: host-path-personal
  - emptyDir:
      medium: Memory
      sizeLimit: 1Ti
    name: dshm
  - emptyDir: {}
    name: log-data
  - hostPath:
      path: /sys
      type: Directory
    name: host-sys
  - hostPath:
      path: /proc
      type: Directory
    name: host-proc
  - hostPath:
      path: /dev
      type: Directory
    name: host-dev
  - emptyDir:
      medium: Memory
      sizeLimit: 128Gi
    name: precheck-shm
  - name: kube-api-access-94hw9
    projected:
      defaultMode: 420
      sources:
      - serviceAccountToken:
          expirationSeconds: 3607
          path: token
      - configMap:
          items:
          - key: ca.crt
            path: ca.crt
          name: kube-root-ca.crt
      - downwardAPI:
          items:
          - fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
            path: namespace
status:
  conditions:
  - lastProbeTime: null
    lastTransitionTime: "2025-07-09T15:51:35Z"
    status: "True"
    type: PodReadyToStartContainers
  - lastProbeTime: null
    lastTransitionTime: "2025-07-09T15:54:24Z"
    status: "True"
    type: Initialized
  - lastProbeTime: null
    lastTransitionTime: "2025-07-09T15:54:29Z"
    status: "True"
    type: Ready
  - lastProbeTime: null
    lastTransitionTime: "2025-07-09T15:54:29Z"
    status: "True"
    type: ContainersReady
  - lastProbeTime: null
    lastTransitionTime: "2025-07-09T15:51:28Z"
    status: "True"
    type: PodScheduled
  containerStatuses:
  - containerID: containerd://3560a4c3e99872980b586e0f58b592a53715c6a6ec3c8d54b70a551f26e770dd
    image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
    imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
    lastState: {}
    name: pytorch
    ready: true
    restartCount: 0
    started: true
    state:
      running:
        startedAt: "2025-07-09T15:54:29Z"
  hostIP: ***********
  hostIPs:
  - ip: ***********
  initContainerStatuses:
  - containerID: containerd://99eaa6369bd3515ae83b16839bdd08dacbc013d6092f91927a65f12e8f1160b1
    image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
    imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
    lastState: {}
    name: ft-driver-worker
    ready: true
    restartCount: 0
    started: true
    state:
      running:
        startedAt: "2025-07-09T15:51:35Z"
  - containerID: containerd://5b5d2df62b3a69cfb1de5590ff7ab705ce11e1b7299265ac7d695d49ce551197
    image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
    imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
    lastState: {}
    name: precheck
    ready: true
    restartCount: 0
    started: false
    state:
      terminated:
        containerID: containerd://5b5d2df62b3a69cfb1de5590ff7ab705ce11e1b7299265ac7d695d49ce551197
        exitCode: 0
        finishedAt: "2025-07-09T15:53:52Z"
        reason: Completed
        startedAt: "2025-07-09T15:51:37Z"
  - containerID: containerd://8955fae7de634cc45435e3afc02459c85043bd29147e82bd8390936a5e29bb0e
    image: docker.io/library/alpine:3.10
    imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
    lastState: {}
    name: init-pytorch
    ready: true
    restartCount: 0
    started: false
    state:
      terminated:
        containerID: containerd://8955fae7de634cc45435e3afc02459c85043bd29147e82bd8390936a5e29bb0e
        exitCode: 0
        finishedAt: "2025-07-09T15:54:23Z"
        reason: Completed
        startedAt: "2025-07-09T15:53:53Z"
  phase: Running
  podIP: *************
  podIPs:
  - ip: *************
  qosClass: Burstable
  startTime: "2025-07-09T15:51:33Z"
