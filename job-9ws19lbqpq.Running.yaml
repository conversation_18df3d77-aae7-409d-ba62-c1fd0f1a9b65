apiVersion: trainer.infra.shiyak.com/v1alpha1
kind: Trainer
metadata:
  annotations:
    ft.driver.master/check-result: '{"checkStatus":"failed","checkTime":1752103492,"operations":{"jobEventRecord":[{"timestamp":1752103492,"jobAbnormal":true,"workerAbnormal":["job-9ws19lbqpq:job
      restart (restart attempt 1), failed pods: job-9ws19lbqpq-worker-7: container
      pytorch Error: exitCode: 1"],"reason":"container-exit-restart-error"}]}}'
    infra.shiyak.com/job-name: pretrain_moe_1b_3b_sparse0p5
    infra.shiyak.com/job-owner: llm-user
    infra.shiyak.com/job-type: pytorch_ft
    infra.shiyak.com/job-user-config: '{"name":"pretrain_moe_1b_3b_sparse0p5","owner":"llm-user","type":"pytorch_ft","docker_image":{"repository_id":"repository-4kv4h49ev3","image_tag":"25.02.rc5"},"request":{"CPU":180,"GPU":8,"Memory":2199023255552,"Rdma":8},"workers":64,"selectors":{"infra.shiyak.com/node-type":"H20Z","infra.shiyak.com/project-id":"llm","infra.shiyak.com/resource-role":"training","nvidia.com/gpu.product":"NVIDIA-H20Z"},"command":"#!/bin/bash\nWORK_DIR=$(pwd
      -P)\nif [[ \"$HOSTNAME\" == *\"notebook\"* ]]; then\n    pkill -9 -f train\n    pkill
      -9 -f wandb\n    export WANDB_MODE=\"disabled\"\nfi\n\n################ environment
      ################\nexport HYDRA_FULL_ERROR=1\nexport CUDA_DEVICE_MAX_CONNECTIONS=1\nexport
      CUDNN_LOGERR_DBG=1 \nexport CUDNN_LOGDEST_DBG=stderr\nexport enable_profiling_tool=0\nexport
      ANC_DISABLE_LOGGING=1\nexport ANC_DELAY_LOSS_REDUCE=1\nexport ANC_TRAIN_LOADER_WORKER=1\nexport
      ENABLE_MOE_LOG=1\nexport ANC_SET_VAL_REPEAT=1\nexport MEGATRON_LOG_BUCKETS=0\n\n\n################
      config ################\nREBUILD=true\nPROJECT_NAME=pretrain_moe_1b_3b_sparse0p5\nCKPT_DIR=/mnt/project/llm/output/wangya/pretraining_model_research\n\nREPO_BRANCH_PAIRS=(\n    \"/mnt/project/llm/users/wangya/project/Megatron-LM\"
      \"wangya/deepseek_main_0603\" \"937c960b\"    # \"latest\" for the latest commit\n    \"/mnt/project/llm/users/wangya/project/NeMo\"
      \"wangya/deepspeek_debug\" \"3e3292a14\"\n    \"/mnt/project/llm/users/wangya/project/Ocean\"
      \"wangya/main\" \"3ba9c94\"\n)\n\nBASE_YAML_CONFIG=(\n    --config_file ./Ocean/scripts/yaml_configs/moe_1b_3b.yaml\n)\n\nMODEL_ARGS=(\n    --moe_ffn_hidden_size
      2048\n    --ffn_hidden_size 2048\n    --moe_shared_expert_intermediate_size
      4096\n    --moe_router_topk 2\n    --num_moe_experts 16\n)\n\nPARALLEL_ARG=(\n)\n\nTOKENIZER_ARGS=(\n)\n\nDATA_ARGS=(\n)\n\nTRAINING_ARGS=(\n    --global_batch_size
      1024\n)\n\nVALIDATION_ARGS=(\n    --validation_interval 1000\n)\n\nLOGGING_ARGS=(\n    --project_name
      $PROJECT_NAME\n    --model_ckpt_dir $CKPT_DIR\n    --wandb_project $(basename
      $CKPT_DIR)\n)\n\n\n################ pip install \u0026 ssh-key ################\nif
      [ \"$REBUILD\" = true ]; then\n    #pip\n    pip install --no-index --find-links=/mnt/project/llm/users/wangya/project/pip_package/
      protobuf==3.20.*\n    python3 -m pip install --no-index --find-links=/mnt/project/llm/users/wangya/project/pip_package/
      leptonai\n    python3 -m pip install --no-index --find-links=/mnt/project/llm/users/wangya/project/pip_package/
      toml\n    python3 -m pip install --no-index --find-links=/mnt/project/llm/users/wangya/project/pip_package/
      --upgrade multi-storage-client==0.21.0\n    python3 -m pip install --no-index
      --find-links=/mnt/project/llm/users/wangya/project/pip_package/ anc==0.4.0\n    #
      ssh\n    mkdir -p ~/.ssh/\n    cp /mnt/project/llm/users/wangya/public_ssh/id_rsa
      ~/.ssh/id_rsa\n    cp /mnt/project/llm/users/wangya/public_ssh/id_rsa.pub ~/.ssh/id_rsa.pub\n    #
      git config\n    git config --global --add safe.directory ''*''\nfi\n\nfor ((
      i=0; i\u003c${#REPO_BRANCH_PAIRS[@]}; i+=3 )); do\n    REPO_URL=\"${REPO_BRANCH_PAIRS[i]}\"\n    BRANCH_NAME=\"${REPO_BRANCH_PAIRS[i+1]}\"\n    COMMIT_NAME=\"${REPO_BRANCH_PAIRS[i+2]}\"\n    REPO_NAME=$(basename
      \"$REPO_URL\" .git)\n    NORMALIZED_NEW_PATH=\"${WORK_DIR}/${REPO_NAME}\"\n\n    if
      [ \"$REBUILD\" = true ]; then\n        echo -e \"\\ndownloading: ${REPO_URL}\"\n        echo
      \"  ↳ to: ${REPO_NAME}\"\n        echo \"  ↳ branch: ${BRANCH_NAME}\"\n\n        rm
      -rf \"$NORMALIZED_NEW_PATH\"\n\n        if git clone --branch \"$BRANCH_NAME\"
      --single-branch \"$REPO_URL\" \"$NORMALIZED_NEW_PATH\"; then\n\n            LATEST_COMMIT=$(git
      rev-parse HEAD)\n            echo \"✅ clone succeed: ${REPO_NAME}\"\n\n            if
      [ \"$COMMIT_NAME\" != \"latest\" ]; then\n                cd \"$NORMALIZED_NEW_PATH\"
      || exit\n                echo \"  ↳ resetting to commit: ${COMMIT_NAME}\"\n                if
      git reset --hard \"$COMMIT_NAME\"; then\n                    echo \"✅ reset
      succeed: ${REPO_NAME} @ ${COMMIT_NAME}\"\n                else\n                    echo
      \"❌ reset failed: commit ${COMMIT_NAME} may not exist in branch ${BRANCH_NAME}\"\n                fi\n                cd
      \"$WORK_DIR\"\n            else\n                echo \"  ↳ at latest commit:
      ${LATEST_COMMIT}\"\n            fi\n\n        else\n            echo \"❌ clone
      error ${REPO_NAME}, please check whether repo_name or branch_name is wrong!\"\n        fi\n    fi\n\n    #
      PYTHONPATH handling\n    if [[ \":${PYTHONPATH}:\" != *\"${NORMALIZED_NEW_PATH}:\"*
      ]]; then\n        export PYTHONPATH=\"${NORMALIZED_NEW_PATH}\":$PYTHONPATH\n        echo
      \"  ↳ Added to PYTHONPATH: ''${NORMALIZED_NEW_PATH}''\"\n    else\n        echo
      \"  ↳ Already in PYTHONPATH: ''${NORMALIZED_NEW_PATH}'' (skipped)\"\n    fi\ndone\n\n\n##############
      training env ################\nCOMMA_COUNT=$(echo \"$NVIDIA_VISIBLE_DEVICES\"
      | grep -o '','' | wc -l)\nexport PET_NPROC_PER_NODE=${PET_NPROC_PER_NODE:-$((COMMA_COUNT
      + 1))}\nexport PET_NNODES=${PET_NNODES:-1}\nexport PET_NODE_RANK=${PET_NODE_RANK:-0}\nexport
      PET_MASTER_ADDR=${PET_MASTER_ADDR:-$HOSTNAME}\nexport PET_MASTER_PORT=${PET_MASTER_PORT:-23456}\nexport
      ANC_DATA_IDX_DUMP_DIR=${CKPT_DIR}/${PROJECT_NAME}/dumpped_data\n\n\n################
      run training ################\ntorchrun --nproc_per_node=$PET_NPROC_PER_NODE
      \\\n    --nnodes=$PET_NNODES \\\n    --node_rank=$PET_NODE_RANK \\\n    --master_addr=$PET_MASTER_ADDR
      \\\n    --master_port=$PET_MASTER_PORT \\\n    ${WORK_DIR}/Ocean/scripts/nemo_2_scripts/launch_pretrain_model_training.py
      \\\n    --num_node $PET_NNODES \\\n    --num_gpus_per_node $PET_NPROC_PER_NODE
      \\\n    ${BASE_YAML_CONFIG[@]} \\\n    ${MODEL_ARGS[@]} \\\n    ${PARALLEL_ARG[@]}
      \\\n    ${TOKENIZER_ARGS[@]} \\\n    ${DATA_ARGS[@]} \\\n    ${TRAINING_ARGS[@]}
      \\\n    ${VALIDATION_ARGS[@]} \\\n    ${LOGGING_ARGS[@]}\n","envs":{"NETWORK_PREFIX":"http://poc1-mlp.shiyak-office.com","WANDB_API_KEY":"****************************************"},"fault_tolerance_spec":{"backoff_limit":3,"check_config":{"logCheck":{"hang":{"enable":true,"period":1800,"delay":600},"pattern":{"enable":false,"keywords":[]}},"preCheck":{"gpuComputationPrecision":{"enable":false},"gpuFlops":{"enable":false},"rdma":{"enable":false},"nvlink":{"enable":false},"pcie":{"enable":false}}}}}'
    trainer.infra.shiyak.com/driver-master-command: /driver master --period 10s --batchSize
      5000 --heartBeatCheckInterval 30s --heartBeatCheckTimes 10 --heartBeatCheckTimeout
      120s --port 8080 --jobId job-9ws19lbqpq --namespace project-llm --upstreamCheckConfig
      "{\"logCheck\":{\"hang\":{\"enable\":true,\"period\":1800,\"delay\":600},\"pattern\":{\"enable\":false,\"keywords\":[]}},\"preCheck\":{\"gpuComputationPrecision\":{\"enable\":false},\"gpuFlops\":{\"enable\":false},\"rdma\":{\"enable\":false},\"nvlink\":{\"enable\":false},\"pcie\":{\"enable\":false}}}"
    trainer.infra.shiyak.com/driver-master-image: ghcr.io/shiyak-infra/ft-driver-master:v0.0.2-8-gf767e25
    trainer.infra.shiyak.com/driver-worker-command: /workspace/worker --dcgm-mode=0
      --enable-dcgm=false --v=3
    trainer.infra.shiyak.com/driver-worker-image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
    trainer.infra.shiyak.com/precheck-command: python /mlp-bench/main.py
    trainer.infra.shiyak.com/precheck-image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
  creationTimestamp: "2025-07-09T15:50:18Z"
  generation: 1
  labels:
    infra.shiyak.com/job-id: job-9ws19lbqpq
    infra.shiyak.com/owner: llm-user
    infra.shiyak.com/project-id: llm
  name: job-9ws19lbqpq
  namespace: project-llm
  resourceVersion: "232050508"
  uid: af904836-da99-49d9-8817-07982ef70e6f
spec:
  policy:
    failurePolicy:
      maxRestarts: 3
    pytorchJobPolicy:
      nprocPerNode: "8"
    runPolicy:
      cleanAfterFinish: true
  replicaSpecs:
    pytorch-master:
      replicas: 1
      template:
        metadata:
          annotations:
            k8s.v1.cni.cncf.io/networks: network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca
          labels:
            infra.shiyak.com/job-id: job-9ws19lbqpq
            infra.shiyak.com/owner: llm-user
            infra.shiyak.com/project-id: llm
        spec:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: infra.shiyak.com/resource-role
                    operator: In
                    values:
                    - training
                  - key: infra.shiyak.com/project-id
                    operator: In
                    values:
                    - llm
                  - key: nvidia.com/gpu.product
                    operator: In
                    values:
                    - NVIDIA-H20Z
                  - key: infra.shiyak.com/node-type
                    operator: In
                    values:
                    - H20Z
                  - key: blacknode.infra.shiyak.com/blacklist
                    operator: NotIn
                    values:
                    - "true"
          containers:
          - command:
            - /bin/bash
            - -c
            - "#!/bin/bash\nWORK_DIR=$(pwd -P)\nif [[ \"$HOSTNAME\" == *\"notebook\"*
              ]]; then\n    pkill -9 -f train\n    pkill -9 -f wandb\n    export WANDB_MODE=\"disabled\"\nfi\n\n################
              environment ################\nexport HYDRA_FULL_ERROR=1\nexport CUDA_DEVICE_MAX_CONNECTIONS=1\nexport
              CUDNN_LOGERR_DBG=1 \nexport CUDNN_LOGDEST_DBG=stderr\nexport enable_profiling_tool=0\nexport
              ANC_DISABLE_LOGGING=1\nexport ANC_DELAY_LOSS_REDUCE=1\nexport ANC_TRAIN_LOADER_WORKER=1\nexport
              ENABLE_MOE_LOG=1\nexport ANC_SET_VAL_REPEAT=1\nexport MEGATRON_LOG_BUCKETS=0\n\n\n################
              config ################\nREBUILD=true\nPROJECT_NAME=pretrain_moe_1b_3b_sparse0p5\nCKPT_DIR=/mnt/project/llm/output/wangya/pretraining_model_research\n\nREPO_BRANCH_PAIRS=(\n
              \   \"/mnt/project/llm/users/wangya/project/Megatron-LM\" \"wangya/deepseek_main_0603\"
              \"937c960b\"    # \"latest\" for the latest commit\n    \"/mnt/project/llm/users/wangya/project/NeMo\"
              \"wangya/deepspeek_debug\" \"3e3292a14\"\n    \"/mnt/project/llm/users/wangya/project/Ocean\"
              \"wangya/main\" \"3ba9c94\"\n)\n\nBASE_YAML_CONFIG=(\n    --config_file
              ./Ocean/scripts/yaml_configs/moe_1b_3b.yaml\n)\n\nMODEL_ARGS=(\n    --moe_ffn_hidden_size
              2048\n    --ffn_hidden_size 2048\n    --moe_shared_expert_intermediate_size
              4096\n    --moe_router_topk 2\n    --num_moe_experts 16\n)\n\nPARALLEL_ARG=(\n)\n\nTOKENIZER_ARGS=(\n)\n\nDATA_ARGS=(\n)\n\nTRAINING_ARGS=(\n
              \   --global_batch_size 1024\n)\n\nVALIDATION_ARGS=(\n    --validation_interval
              1000\n)\n\nLOGGING_ARGS=(\n    --project_name $PROJECT_NAME\n    --model_ckpt_dir
              $CKPT_DIR\n    --wandb_project $(basename $CKPT_DIR)\n)\n\n\n################
              pip install & ssh-key ################\nif [ \"$REBUILD\" = true ];
              then\n    #pip\n    pip install --no-index --find-links=/mnt/project/llm/users/wangya/project/pip_package/
              protobuf==3.20.*\n    python3 -m pip install --no-index --find-links=/mnt/project/llm/users/wangya/project/pip_package/
              leptonai\n    python3 -m pip install --no-index --find-links=/mnt/project/llm/users/wangya/project/pip_package/
              toml\n    python3 -m pip install --no-index --find-links=/mnt/project/llm/users/wangya/project/pip_package/
              --upgrade multi-storage-client==0.21.0\n    python3 -m pip install --no-index
              --find-links=/mnt/project/llm/users/wangya/project/pip_package/ anc==0.4.0\n
              \   # ssh\n    mkdir -p ~/.ssh/\n    cp /mnt/project/llm/users/wangya/public_ssh/id_rsa
              ~/.ssh/id_rsa\n    cp /mnt/project/llm/users/wangya/public_ssh/id_rsa.pub
              ~/.ssh/id_rsa.pub\n    # git config\n    git config --global --add safe.directory
              '*'\nfi\n\nfor (( i=0; i<${#REPO_BRANCH_PAIRS[@]}; i+=3 )); do\n    REPO_URL=\"${REPO_BRANCH_PAIRS[i]}\"\n
              \   BRANCH_NAME=\"${REPO_BRANCH_PAIRS[i+1]}\"\n    COMMIT_NAME=\"${REPO_BRANCH_PAIRS[i+2]}\"\n
              \   REPO_NAME=$(basename \"$REPO_URL\" .git)\n    NORMALIZED_NEW_PATH=\"${WORK_DIR}/${REPO_NAME}\"\n\n
              \   if [ \"$REBUILD\" = true ]; then\n        echo -e \"\\ndownloading:
              ${REPO_URL}\"\n        echo \"  ↳ to: ${REPO_NAME}\"\n        echo \"
              \ ↳ branch: ${BRANCH_NAME}\"\n\n        rm -rf \"$NORMALIZED_NEW_PATH\"\n\n
              \       if git clone --branch \"$BRANCH_NAME\" --single-branch \"$REPO_URL\"
              \"$NORMALIZED_NEW_PATH\"; then\n\n            LATEST_COMMIT=$(git rev-parse
              HEAD)\n            echo \"✅ clone succeed: ${REPO_NAME}\"\n\n            if
              [ \"$COMMIT_NAME\" != \"latest\" ]; then\n                cd \"$NORMALIZED_NEW_PATH\"
              || exit\n                echo \"  ↳ resetting to commit: ${COMMIT_NAME}\"\n
              \               if git reset --hard \"$COMMIT_NAME\"; then\n                    echo
              \"✅ reset succeed: ${REPO_NAME} @ ${COMMIT_NAME}\"\n                else\n
              \                   echo \"❌ reset failed: commit ${COMMIT_NAME} may
              not exist in branch ${BRANCH_NAME}\"\n                fi\n                cd
              \"$WORK_DIR\"\n            else\n                echo \"  ↳ at latest
              commit: ${LATEST_COMMIT}\"\n            fi\n\n        else\n            echo
              \"❌ clone error ${REPO_NAME}, please check whether repo_name or branch_name
              is wrong!\"\n        fi\n    fi\n\n    # PYTHONPATH handling\n    if
              [[ \":${PYTHONPATH}:\" != *\"${NORMALIZED_NEW_PATH}:\"* ]]; then\n        export
              PYTHONPATH=\"${NORMALIZED_NEW_PATH}\":$PYTHONPATH\n        echo \"  ↳
              Added to PYTHONPATH: '${NORMALIZED_NEW_PATH}'\"\n    else\n        echo
              \"  ↳ Already in PYTHONPATH: '${NORMALIZED_NEW_PATH}' (skipped)\"\n
              \   fi\ndone\n\n\n############## training env ################\nCOMMA_COUNT=$(echo
              \"$NVIDIA_VISIBLE_DEVICES\" | grep -o ',' | wc -l)\nexport PET_NPROC_PER_NODE=${PET_NPROC_PER_NODE:-$((COMMA_COUNT
              + 1))}\nexport PET_NNODES=${PET_NNODES:-1}\nexport PET_NODE_RANK=${PET_NODE_RANK:-0}\nexport
              PET_MASTER_ADDR=${PET_MASTER_ADDR:-$HOSTNAME}\nexport PET_MASTER_PORT=${PET_MASTER_PORT:-23456}\nexport
              ANC_DATA_IDX_DUMP_DIR=${CKPT_DIR}/${PROJECT_NAME}/dumpped_data\n\n\n################
              run training ################\ntorchrun --nproc_per_node=$PET_NPROC_PER_NODE
              \\\n    --nnodes=$PET_NNODES \\\n    --node_rank=$PET_NODE_RANK \\\n
              \   --master_addr=$PET_MASTER_ADDR \\\n    --master_port=$PET_MASTER_PORT
              \\\n    ${WORK_DIR}/Ocean/scripts/nemo_2_scripts/launch_pretrain_model_training.py
              \\\n    --num_node $PET_NNODES \\\n    --num_gpus_per_node $PET_NPROC_PER_NODE
              \\\n    ${BASE_YAML_CONFIG[@]} \\\n    ${MODEL_ARGS[@]} \\\n    ${PARALLEL_ARG[@]}
              \\\n    ${TOKENIZER_ARGS[@]} \\\n    ${DATA_ARGS[@]} \\\n    ${TRAINING_ARGS[@]}
              \\\n    ${VALIDATION_ARGS[@]} \\\n    ${LOGGING_ARGS[@]}\n"
            env:
            - name: WANDB_API_KEY
              value: ****************************************
            - name: NETWORK_PREFIX
              value: http://poc1-mlp.shiyak-office.com
            - name: MLP_CLUSTER
              value: poc1
            - name: MLP_PROJECT
              value: llm
            - name: MLP_USER
              value: llm-user
            - name: MLP_URL
              value: http://poc1-mlp.shiyak-office.com/console/dlc_job/job-9ws19lbqpq
            - name: MLP_ID
              value: job-9ws19lbqpq
            - name: MLP_NAME
              value: pretrain_moe_1b_3b_sparse0p5
            - name: MLP_TYPE
              value: job
            - name: MLP_WORKERS
              value: "64"
            - name: MLP_GPUS
              value: "512"
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: HOST_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            image: sea-hub.shiyak-office.com/nvcr.io-proxy/nvidia/nemo:25.02.rc5
            name: pytorch
            resources:
              limits:
                cpu: "171"
                memory: 1992294Mi
                nvidia.com/gpu: "8"
                rdma/hca: "8"
              requests:
                cpu: "153"
                memory: 1782579Mi
                nvidia.com/gpu: "8"
                rdma/hca: "8"
            securityContext:
              capabilities:
                add:
                - IPC_LOCK
                - SYS_NICE
            volumeMounts:
            - mountPath: /dev/shm
              name: dshm
          restartPolicy: Never
          volumes:
          - emptyDir:
              medium: Memory
              sizeLimit: 1Ti
            name: dshm
    pytorch-worker:
      replicas: 63
      template:
        metadata:
          annotations:
            k8s.v1.cni.cncf.io/networks: network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca
          labels:
            infra.shiyak.com/job-id: job-9ws19lbqpq
            infra.shiyak.com/owner: llm-user
            infra.shiyak.com/project-id: llm
        spec:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: infra.shiyak.com/resource-role
                    operator: In
                    values:
                    - training
                  - key: infra.shiyak.com/project-id
                    operator: In
                    values:
                    - llm
                  - key: nvidia.com/gpu.product
                    operator: In
                    values:
                    - NVIDIA-H20Z
                  - key: infra.shiyak.com/node-type
                    operator: In
                    values:
                    - H20Z
                  - key: blacknode.infra.shiyak.com/blacklist
                    operator: NotIn
                    values:
                    - "true"
          containers:
          - command:
            - /bin/bash
            - -c
            - "#!/bin/bash\nWORK_DIR=$(pwd -P)\nif [[ \"$HOSTNAME\" == *\"notebook\"*
              ]]; then\n    pkill -9 -f train\n    pkill -9 -f wandb\n    export WANDB_MODE=\"disabled\"\nfi\n\n################
              environment ################\nexport HYDRA_FULL_ERROR=1\nexport CUDA_DEVICE_MAX_CONNECTIONS=1\nexport
              CUDNN_LOGERR_DBG=1 \nexport CUDNN_LOGDEST_DBG=stderr\nexport enable_profiling_tool=0\nexport
              ANC_DISABLE_LOGGING=1\nexport ANC_DELAY_LOSS_REDUCE=1\nexport ANC_TRAIN_LOADER_WORKER=1\nexport
              ENABLE_MOE_LOG=1\nexport ANC_SET_VAL_REPEAT=1\nexport MEGATRON_LOG_BUCKETS=0\n\n\n################
              config ################\nREBUILD=true\nPROJECT_NAME=pretrain_moe_1b_3b_sparse0p5\nCKPT_DIR=/mnt/project/llm/output/wangya/pretraining_model_research\n\nREPO_BRANCH_PAIRS=(\n
              \   \"/mnt/project/llm/users/wangya/project/Megatron-LM\" \"wangya/deepseek_main_0603\"
              \"937c960b\"    # \"latest\" for the latest commit\n    \"/mnt/project/llm/users/wangya/project/NeMo\"
              \"wangya/deepspeek_debug\" \"3e3292a14\"\n    \"/mnt/project/llm/users/wangya/project/Ocean\"
              \"wangya/main\" \"3ba9c94\"\n)\n\nBASE_YAML_CONFIG=(\n    --config_file
              ./Ocean/scripts/yaml_configs/moe_1b_3b.yaml\n)\n\nMODEL_ARGS=(\n    --moe_ffn_hidden_size
              2048\n    --ffn_hidden_size 2048\n    --moe_shared_expert_intermediate_size
              4096\n    --moe_router_topk 2\n    --num_moe_experts 16\n)\n\nPARALLEL_ARG=(\n)\n\nTOKENIZER_ARGS=(\n)\n\nDATA_ARGS=(\n)\n\nTRAINING_ARGS=(\n
              \   --global_batch_size 1024\n)\n\nVALIDATION_ARGS=(\n    --validation_interval
              1000\n)\n\nLOGGING_ARGS=(\n    --project_name $PROJECT_NAME\n    --model_ckpt_dir
              $CKPT_DIR\n    --wandb_project $(basename $CKPT_DIR)\n)\n\n\n################
              pip install & ssh-key ################\nif [ \"$REBUILD\" = true ];
              then\n    #pip\n    pip install --no-index --find-links=/mnt/project/llm/users/wangya/project/pip_package/
              protobuf==3.20.*\n    python3 -m pip install --no-index --find-links=/mnt/project/llm/users/wangya/project/pip_package/
              leptonai\n    python3 -m pip install --no-index --find-links=/mnt/project/llm/users/wangya/project/pip_package/
              toml\n    python3 -m pip install --no-index --find-links=/mnt/project/llm/users/wangya/project/pip_package/
              --upgrade multi-storage-client==0.21.0\n    python3 -m pip install --no-index
              --find-links=/mnt/project/llm/users/wangya/project/pip_package/ anc==0.4.0\n
              \   # ssh\n    mkdir -p ~/.ssh/\n    cp /mnt/project/llm/users/wangya/public_ssh/id_rsa
              ~/.ssh/id_rsa\n    cp /mnt/project/llm/users/wangya/public_ssh/id_rsa.pub
              ~/.ssh/id_rsa.pub\n    # git config\n    git config --global --add safe.directory
              '*'\nfi\n\nfor (( i=0; i<${#REPO_BRANCH_PAIRS[@]}; i+=3 )); do\n    REPO_URL=\"${REPO_BRANCH_PAIRS[i]}\"\n
              \   BRANCH_NAME=\"${REPO_BRANCH_PAIRS[i+1]}\"\n    COMMIT_NAME=\"${REPO_BRANCH_PAIRS[i+2]}\"\n
              \   REPO_NAME=$(basename \"$REPO_URL\" .git)\n    NORMALIZED_NEW_PATH=\"${WORK_DIR}/${REPO_NAME}\"\n\n
              \   if [ \"$REBUILD\" = true ]; then\n        echo -e \"\\ndownloading:
              ${REPO_URL}\"\n        echo \"  ↳ to: ${REPO_NAME}\"\n        echo \"
              \ ↳ branch: ${BRANCH_NAME}\"\n\n        rm -rf \"$NORMALIZED_NEW_PATH\"\n\n
              \       if git clone --branch \"$BRANCH_NAME\" --single-branch \"$REPO_URL\"
              \"$NORMALIZED_NEW_PATH\"; then\n\n            LATEST_COMMIT=$(git rev-parse
              HEAD)\n            echo \"✅ clone succeed: ${REPO_NAME}\"\n\n            if
              [ \"$COMMIT_NAME\" != \"latest\" ]; then\n                cd \"$NORMALIZED_NEW_PATH\"
              || exit\n                echo \"  ↳ resetting to commit: ${COMMIT_NAME}\"\n
              \               if git reset --hard \"$COMMIT_NAME\"; then\n                    echo
              \"✅ reset succeed: ${REPO_NAME} @ ${COMMIT_NAME}\"\n                else\n
              \                   echo \"❌ reset failed: commit ${COMMIT_NAME} may
              not exist in branch ${BRANCH_NAME}\"\n                fi\n                cd
              \"$WORK_DIR\"\n            else\n                echo \"  ↳ at latest
              commit: ${LATEST_COMMIT}\"\n            fi\n\n        else\n            echo
              \"❌ clone error ${REPO_NAME}, please check whether repo_name or branch_name
              is wrong!\"\n        fi\n    fi\n\n    # PYTHONPATH handling\n    if
              [[ \":${PYTHONPATH}:\" != *\"${NORMALIZED_NEW_PATH}:\"* ]]; then\n        export
              PYTHONPATH=\"${NORMALIZED_NEW_PATH}\":$PYTHONPATH\n        echo \"  ↳
              Added to PYTHONPATH: '${NORMALIZED_NEW_PATH}'\"\n    else\n        echo
              \"  ↳ Already in PYTHONPATH: '${NORMALIZED_NEW_PATH}' (skipped)\"\n
              \   fi\ndone\n\n\n############## training env ################\nCOMMA_COUNT=$(echo
              \"$NVIDIA_VISIBLE_DEVICES\" | grep -o ',' | wc -l)\nexport PET_NPROC_PER_NODE=${PET_NPROC_PER_NODE:-$((COMMA_COUNT
              + 1))}\nexport PET_NNODES=${PET_NNODES:-1}\nexport PET_NODE_RANK=${PET_NODE_RANK:-0}\nexport
              PET_MASTER_ADDR=${PET_MASTER_ADDR:-$HOSTNAME}\nexport PET_MASTER_PORT=${PET_MASTER_PORT:-23456}\nexport
              ANC_DATA_IDX_DUMP_DIR=${CKPT_DIR}/${PROJECT_NAME}/dumpped_data\n\n\n################
              run training ################\ntorchrun --nproc_per_node=$PET_NPROC_PER_NODE
              \\\n    --nnodes=$PET_NNODES \\\n    --node_rank=$PET_NODE_RANK \\\n
              \   --master_addr=$PET_MASTER_ADDR \\\n    --master_port=$PET_MASTER_PORT
              \\\n    ${WORK_DIR}/Ocean/scripts/nemo_2_scripts/launch_pretrain_model_training.py
              \\\n    --num_node $PET_NNODES \\\n    --num_gpus_per_node $PET_NPROC_PER_NODE
              \\\n    ${BASE_YAML_CONFIG[@]} \\\n    ${MODEL_ARGS[@]} \\\n    ${PARALLEL_ARG[@]}
              \\\n    ${TOKENIZER_ARGS[@]} \\\n    ${DATA_ARGS[@]} \\\n    ${TRAINING_ARGS[@]}
              \\\n    ${VALIDATION_ARGS[@]} \\\n    ${LOGGING_ARGS[@]}\n"
            env:
            - name: WANDB_API_KEY
              value: ****************************************
            - name: NETWORK_PREFIX
              value: http://poc1-mlp.shiyak-office.com
            - name: MLP_CLUSTER
              value: poc1
            - name: MLP_PROJECT
              value: llm
            - name: MLP_USER
              value: llm-user
            - name: MLP_URL
              value: http://poc1-mlp.shiyak-office.com/console/dlc_job/job-9ws19lbqpq
            - name: MLP_ID
              value: job-9ws19lbqpq
            - name: MLP_NAME
              value: pretrain_moe_1b_3b_sparse0p5
            - name: MLP_TYPE
              value: job
            - name: MLP_WORKERS
              value: "64"
            - name: MLP_GPUS
              value: "512"
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: HOST_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            image: sea-hub.shiyak-office.com/nvcr.io-proxy/nvidia/nemo:25.02.rc5
            name: pytorch
            resources:
              limits:
                cpu: "171"
                memory: 1992294Mi
                nvidia.com/gpu: "8"
                rdma/hca: "8"
              requests:
                cpu: "153"
                memory: 1782579Mi
                nvidia.com/gpu: "8"
                rdma/hca: "8"
            securityContext:
              capabilities:
                add:
                - IPC_LOCK
                - SYS_NICE
            volumeMounts:
            - mountPath: /dev/shm
              name: dshm
          restartPolicy: Never
          volumes:
          - emptyDir:
              medium: Memory
              sizeLimit: 1Ti
            name: dshm
  type: PyTorchJob
status:
  conditions:
  - lastTransitionTime: "2025-07-09T23:25:33Z"
    message: job is pending
    reason: JobPending
    status: "True"
    type: Pending
  - lastTransitionTime: "2025-07-09T23:26:20Z"
    message: job is running
    reason: JobRunning
    status: "True"
    type: Running
  - lastTransitionTime: "2025-07-09T23:24:47Z"
    message: 'job restart (restart attempt 1), failed pods: job-9ws19lbqpq-worker-7:
      container pytorch Error: exitCode: 1'
    reason: JobRestart
    status: "False"
    type: Restart
  replicaStatuses:
    pytorch-master:
      podStatuses:
      - name: job-9ws19lbqpq-master-0
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:25:39Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:25:44Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:25:44Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://35f68af2d0f4b6103764af40ac7caf63fd46f2c9377c927820910b44583e473c
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:44Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://b8c2b3863660bef3a6613c713a4cbd82a80dba57e66699e681d8e386d3eb59c8
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://b810821e379c0ddec2b7a826ef66e5cdc50cc293ab6804419a2b87d9f241bda3
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://b810821e379c0ddec2b7a826ef66e5cdc50cc293ab6804419a2b87d9f241bda3
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
    pytorch-worker:
      podStatuses:
      - name: job-9ws19lbqpq-worker-0
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://7c47c3669caf6f94d40973b7a9cccd627b1dfb31a4ca43b9ce5a2278d7af825a
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://eeaecf2e714cf86f761138eaaa885fe245b2170ff64bc7ba358a5f8e7f3294e0
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://cdd69649608284ca647e3c82dce6b09766f95724aebc28bcc0f4898af3fd7988
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://cdd69649608284ca647e3c82dce6b09766f95724aebc28bcc0f4898af3fd7988
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://43bf1239f3afdbd0b6345d9f1923378fff66d45f0807fe932512ed4efb84f829
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://43bf1239f3afdbd0b6345d9f1923378fff66d45f0807fe932512ed4efb84f829
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-1
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://73bf2282a476dad84ee9840e82e7395cf6983c72247c1714cc00cccd17e7af3a
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:15Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://cc9003882a2ab78cd66347f7bfddf5d250fc751068119bc5ef793492833149fa
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://ff2486b5dfa0492e7bb34d9e8a45bd2df14497c669041d3586fcd5facc697475
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://ff2486b5dfa0492e7bb34d9e8a45bd2df14497c669041d3586fcd5facc697475
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://296a81cc500f2bdfcc72f74edcb01c156a3fa9a05c6b3659dce74b6a92bc413a
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://296a81cc500f2bdfcc72f74edcb01c156a3fa9a05c6b3659dce74b6a92bc413a
                exitCode: 0
                finishedAt: "2025-07-09T23:26:09Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:39Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-2
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:15Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:15Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://03f74c86513ce221d122513bb9508148d3b262994b29b79249f6be9644ebe166
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:15Z"
          hostIP: ***********4
          hostIPs:
          - ip: ***********4
          initContainerStatuses:
          - containerID: containerd://6511289675dd3119917baee92cebfd278e15f2adfb8a779b865e9e51488feffe
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:35Z"
          - containerID: containerd://0f5b766eb551380f81cb17da8279f456c8df18690615cc57fb6a2fd3bdde92a4
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0f5b766eb551380f81cb17da8279f456c8df18690615cc57fb6a2fd3bdde92a4
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://552f5dc2562795eb9b52bbc574795ab1845937ca44a3b0a4b6990a65aee43b63
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://552f5dc2562795eb9b52bbc574795ab1845937ca44a3b0a4b6990a65aee43b63
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-3
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:15Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:15Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://32c02b8762370d9c827af0a6c9033b0504637c3c6f6e81f6392a11a3155e6c82
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:15Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://152258d08aa2710b991e8856354b2aab71031b90933cec532822f96808cc201c
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://257f67328d9f07b1be8de266f2a5e18d94c34aaf448221789ac57a82e9040db1
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://257f67328d9f07b1be8de266f2a5e18d94c34aaf448221789ac57a82e9040db1
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://ef0fb487c48cf86c470496b81be91833b7164b07e7eef8796a22cfc11e967d2d
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://ef0fb487c48cf86c470496b81be91833b7164b07e7eef8796a22cfc11e967d2d
                exitCode: 0
                finishedAt: "2025-07-09T23:26:09Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:39Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-4
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:39Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:13Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:19Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:19Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://1a497c62b5334cd98fb6bdc3c1760d63c01db24151cd859a8e0d3d4514a9064f
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:19Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://43c128606f5496b2ce926dbb00a005c7a7e53f3b10189fae2d4a536eb85c42f6
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://16799f097ab5d68ff34bc4dbe6fb8484855a5940dc323b66b218c7dedfa75037
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://16799f097ab5d68ff34bc4dbe6fb8484855a5940dc323b66b218c7dedfa75037
                exitCode: 0
                finishedAt: "2025-07-09T23:25:42Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:41Z"
          - containerID: containerd://7cdbc3630e41f37c3704b6b38a45d29de5aec9f147acbe91320173cb4e680b4f
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://7cdbc3630e41f37c3704b6b38a45d29de5aec9f147acbe91320173cb4e680b4f
                exitCode: 0
                finishedAt: "2025-07-09T23:26:13Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:43Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:35Z"
      - name: job-9ws19lbqpq-worker-5
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://cb1c0a49ce108814b344c909df165a44d7b480f1fab389d64858e83af2e5d41a
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:17Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://4baba0b5faaf5eb0d6e5b85d3837cebfcdde067c800609cc9a04ab86327a45d8
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:35Z"
          - containerID: containerd://c90de91a865626765eb889fa8fd08a7b753fdbf24b5016627ca4b9272d3e23f1
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://c90de91a865626765eb889fa8fd08a7b753fdbf24b5016627ca4b9272d3e23f1
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://5cde30910d6c961b39fdee67e0da8e9a649a2e020f8a78b9c292f031f750c808
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://5cde30910d6c961b39fdee67e0da8e9a649a2e020f8a78b9c292f031f750c808
                exitCode: 0
                finishedAt: "2025-07-09T23:26:11Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:41Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-6
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://a02c9d5a7232f6e690394e8e8ea23d71d4a4ca2c4c823795192194b9882d086d
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://98cac02391f99fc84d21a395b3cf8dd453093a05ef2516f020bdfb2fd65967f7
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://5d17a2b2f80e959ffb1a57b8a82a62d5ca5ad589e2d3fe77a9c8c462d33a21fe
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://5d17a2b2f80e959ffb1a57b8a82a62d5ca5ad589e2d3fe77a9c8c462d33a21fe
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://7bc7598f5a0823d946040fafe044fbd3f229b302649397912846d745810fa7d7
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://7bc7598f5a0823d946040fafe044fbd3f229b302649397912846d745810fa7d7
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-7
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://65e96184c9f28df545e29743fef2da4bfdd5206eb53e9142bd5996b17d2bbfcd
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://cf25cefc327b28c211f99f5e4799959b221a096811ffa821074278bcda9cfc97
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://c4dd90df1fab21af9df1b162be87337876bb5f12b3b9ebafcd0457e0c9e510e7
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://c4dd90df1fab21af9df1b162be87337876bb5f12b3b9ebafcd0457e0c9e510e7
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://4a0a544b98c093cab12acbd2689b9b268142fb8c1642f8ed1b62b541cfd61787
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://4a0a544b98c093cab12acbd2689b9b268142fb8c1642f8ed1b62b541cfd61787
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-8
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://34f4fd83894c4394aedd83afd389735d83ad0b26044a90b2997129342ff38eae
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:15Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://41c71882389bb24b5bc28da9d083bcc800979376ea5d8330d2c1da0b696303b5
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://ab4a3bd780bcceafd1f788803fc82b2a7609c16deb9a40c4794b534110fe1aa3
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://ab4a3bd780bcceafd1f788803fc82b2a7609c16deb9a40c4794b534110fe1aa3
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://24229567192248c8a1b8eca18c38438f24d1c7f4d2128c949b3f55f4ec094414
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://24229567192248c8a1b8eca18c38438f24d1c7f4d2128c949b3f55f4ec094414
                exitCode: 0
                finishedAt: "2025-07-09T23:26:09Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:39Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-9
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://64b3e4467cb460453791c693f6d6d6104a6b75f254b84bc540026483bcb2e1d6
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:15Z"
          hostIP: ***********5
          hostIPs:
          - ip: ***********5
          initContainerStatuses:
          - containerID: containerd://2c30e07ed382cc56e1dd50d21c35b2a4d966de5968561c3f1aab51880e8bc89b
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://0acd3ff8058c6c4367bcc9115fca2ee4c39074abae104d7f1f740ded4f1c9805
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0acd3ff8058c6c4367bcc9115fca2ee4c39074abae104d7f1f740ded4f1c9805
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://fa9ded66b413276e24a7abc186909ac0b71cbf428ed75176bae5ba9041e99e7e
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://fa9ded66b413276e24a7abc186909ac0b71cbf428ed75176bae5ba9041e99e7e
                exitCode: 0
                finishedAt: "2025-07-09T23:26:09Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:39Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-10
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:19Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:19Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://8276297324021212469b5020b79b04651b72f1d1a45a6740f29e4c0ee3a300d6
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:19Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://11abd162e319fce963734b159fa7a060bdb0ee19bd7c8e1f441f8d1528c7715b
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://86d99a8f0048f90169566381dd67fec5466fdab25d3a31a39ff423f40f41c6c1
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://86d99a8f0048f90169566381dd67fec5466fdab25d3a31a39ff423f40f41c6c1
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://5d169f818a376fe66708be5d30f2c078d0786c5cf8744fac939bc403b6afe9f4
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://5d169f818a376fe66708be5d30f2c078d0786c5cf8744fac939bc403b6afe9f4
                exitCode: 0
                finishedAt: "2025-07-09T23:26:09Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:39Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-11
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://d2841355efcafb40350182d20fb5021d11e889ced9284abaa6c5ae4f74db883f
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://e13ade4ff9fe48e6a28b40c975f0977e94e95c32dd785d8278b22c65833f0dd4
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:35Z"
          - containerID: containerd://7792a05925a3c6cc4f99727e6ff33d9e698f1bddfe12d4215944f28696b19e82
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://7792a05925a3c6cc4f99727e6ff33d9e698f1bddfe12d4215944f28696b19e82
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://615a3385281db0b44813c83dc9a68c24eddb9fa8566cb07405dea81855833608
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://615a3385281db0b44813c83dc9a68c24eddb9fa8566cb07405dea81855833608
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-12
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://1e5cd41c1ad9d50e46f67c05a3afd17180744396ae955805bffcd84477502fdd
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://c681489b9f9e50951b374a7bdc57ef72f8c6df6cabd868418590c1525863e5a4
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://1f5aff142a6f629260b2e72215769878d2d5ac05bc22a9f1def7e30269491363
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://1f5aff142a6f629260b2e72215769878d2d5ac05bc22a9f1def7e30269491363
                exitCode: 0
                finishedAt: "2025-07-09T23:25:40Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://a9de7e1a26169bf69a8d5f81a601f5cbab57478ef1a3d2ba4c2f9bdea94769bd
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://a9de7e1a26169bf69a8d5f81a601f5cbab57478ef1a3d2ba4c2f9bdea94769bd
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:34Z"
      - name: job-9ws19lbqpq-worker-13
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://5bf91f2d41a0f3df60bc7023aa1f5cb8e31b996d0f8afac0f0de52fe979fc736
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://ebaf90872d6ce0daef2f9ff3e0e8ddeae46a47ba392bc08ad192b134a151d4c9
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://236534f081956e0a6fbd813f35fd96e3fce449f79c452f82d4049d2dc07cacb6
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://236534f081956e0a6fbd813f35fd96e3fce449f79c452f82d4049d2dc07cacb6
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://46c87976d9d264b34324b13ce02856597837d30e630de8fefa5f0fcd7600b817
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://46c87976d9d264b34324b13ce02856597837d30e630de8fefa5f0fcd7600b817
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-14
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://3b348bdc9585ebd3fb2b439b2cb38465d1043c5539d138f9cb15284e285b9a9b
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://61537c95cccf9a73c6361c387298e72849acac5cf1e741d505f2111c22dafc1d
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://ab19f3a04e6babab7e4adb1ac7ea9722dd25c95692abda7ab9718619a594c01d
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://ab19f3a04e6babab7e4adb1ac7ea9722dd25c95692abda7ab9718619a594c01d
                exitCode: 0
                finishedAt: "2025-07-09T23:25:40Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://7c50ed4f0952854a4c880524aa20861a04e4a8242293e825ffe4215070240413
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://7c50ed4f0952854a4c880524aa20861a04e4a8242293e825ffe4215070240413
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:34Z"
      - name: job-9ws19lbqpq-worker-15
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://3af9d2cc047b1f0d5deaee5dec3840dde0328b09f9e1bb01658e70ee4ae8eb6f
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:17Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://092c667d348ff376cf8449d751ccbb683b711dcc1e40b486cc9e6ae470a7e1ec
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://eb5a8959c4a2dd6d5aad6b015ee945ab9eb31e563c7c64ff270c20e641e18682
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://eb5a8959c4a2dd6d5aad6b015ee945ab9eb31e563c7c64ff270c20e641e18682
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://858750f935fc3762a945772548044c99b3819513da066e269131054651186dec
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://858750f935fc3762a945772548044c99b3819513da066e269131054651186dec
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:34Z"
      - name: job-9ws19lbqpq-worker-16
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:15Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:15Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://6d8f10c7a13c3a808a1bb5e061cb30566cef4bf1cc9de513fc0e2ca669860b97
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:15Z"
          hostIP: ***********0
          hostIPs:
          - ip: ***********0
          initContainerStatuses:
          - containerID: containerd://78d56c2190bb110b1a42db3857fad8485b93521957febe0ba7558d35fd300aba
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://2e4e04ffec820b79f92346c7c323c9d2805915cdf9d658a8bd5532c7b079939a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://2e4e04ffec820b79f92346c7c323c9d2805915cdf9d658a8bd5532c7b079939a
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://e5eae22af4e04224c3de04bb1d53a159839f5e85628bddfafc66870c28dcccf4
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://e5eae22af4e04224c3de04bb1d53a159839f5e85628bddfafc66870c28dcccf4
                exitCode: 0
                finishedAt: "2025-07-09T23:26:09Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:39Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-17
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://942267c5222a7e022e962d88ef2a16c7a59e990246bcadff98bed06b44f4679e
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://c88dac53669f1457216fc083e4fd99b310e22eadc9e454eec0cef55a291ad909
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://ab004b64e82e4a3bc7e45ebf8eb17bc94dbc0ed824dab74d7f8f7fd9dd6ab1c4
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://ab004b64e82e4a3bc7e45ebf8eb17bc94dbc0ed824dab74d7f8f7fd9dd6ab1c4
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://87248f35a0290dafb62e8254a72c29cdcc39e02dfb8dfa4df45b19fdecbee9a9
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://87248f35a0290dafb62e8254a72c29cdcc39e02dfb8dfa4df45b19fdecbee9a9
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-18
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:19Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:19Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://34e7f4cbef3eb79e3478a824715d8fa993832a8b425fa8e2640075a1cebae3c9
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:19Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://72a4fa3a8a16ac5c6fcd35af3b471a82cb1c0b59d0b998e5cd4f5b7f5c180fb8
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://4d6d12697ba082a495ede49b027ef8f0b489c47007cf8c736cca79638fe90608
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://4d6d12697ba082a495ede49b027ef8f0b489c47007cf8c736cca79638fe90608
                exitCode: 0
                finishedAt: "2025-07-09T23:25:40Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://e7b26e1b3ef71e7acae7da5a4304df3dbb39c439c5f6c303a1cc6e1da31b3440
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://e7b26e1b3ef71e7acae7da5a4304df3dbb39c439c5f6c303a1cc6e1da31b3440
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-19
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://a84e4a5a952adc8be7747689f838fc6116bfbf3c34aff59743b2072c3d5510c6
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:17Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://80e1665dcb683180ea5172301b40aec997a566dca72a20762e59dd63e6a1cb46
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://4a617f055ddbd0016a00bf5ee653d1ae12546b5ccd8ac0bedabc45f2d9b1f16c
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://4a617f055ddbd0016a00bf5ee653d1ae12546b5ccd8ac0bedabc45f2d9b1f16c
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://0759badd724d7e6a249ffc4d31fe96556ce33f92d3be0c5a0c5606b616266870
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0759badd724d7e6a249ffc4d31fe96556ce33f92d3be0c5a0c5606b616266870
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-20
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://91968be3a8fb8980d4fd26b835d3b4c93156b60a728e2db43c0c7b5c367595b0
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://0830ea758efb59aa37d89e73ae7cc27e3dea4b1b1e4ccb9db0a0e88bcdf5b34c
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://7e35b93dd6e784c0ed61a9a9799d77d3402b4024705b95f04c3513253b160a5f
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://7e35b93dd6e784c0ed61a9a9799d77d3402b4024705b95f04c3513253b160a5f
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://8c9daadb655b907c53cd1ee47b2a00a070b59ebfe8b686576a84ed1314b3a5cf
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://8c9daadb655b907c53cd1ee47b2a00a070b59ebfe8b686576a84ed1314b3a5cf
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-21
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://32334d680519292047c9cfd2e30c4971af920fd5648bdb3ef9964a75f79f635d
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://e023a1eda8dc51b46c4059fff906b6997a109e12aad737739d9fefb7c707c594
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://883940e74b3c5c450146f010f95f73352fe1778719ea4d86aeac7ceb5eaf65fb
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://883940e74b3c5c450146f010f95f73352fe1778719ea4d86aeac7ceb5eaf65fb
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://1c55cc19a24c0ad4a54bf221e9868ce3696f3e251bf7acb3d7c1b23bfb972775
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://1c55cc19a24c0ad4a54bf221e9868ce3696f3e251bf7acb3d7c1b23bfb972775
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:34Z"
      - name: job-9ws19lbqpq-worker-22
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://0e0bb0842481f10c0192186cc6b32662f33493c6e4ebaccfbad5a028482f7072
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:15Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://49af1c315524e1ddc6fd5a86b04c7d16c0ff66d5e2b752a4e3980085f6434bf7
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:35Z"
          - containerID: containerd://9d84324fddf317c8502b2862ccfa46747cff71ba3aaf6e66681d44a4de73bd91
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://9d84324fddf317c8502b2862ccfa46747cff71ba3aaf6e66681d44a4de73bd91
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://71d9b5ac414109508d926c04f1c41610d1892b6597ffd6c3c6653d886ab78f28
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://71d9b5ac414109508d926c04f1c41610d1892b6597ffd6c3c6653d886ab78f28
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:39Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-23
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:35Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:15Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:15Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://a8f4b0ad9dd1d8c570c1d5ddf09e5dc860ce9bbbe0187e6cc6460effc20ba0f6
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:14Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://afd0888076084f8f8865e22a6c49559104a63085bb1329400fe794808f3853c5
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:35Z"
          - containerID: containerd://3ffc5be807fc25cec44947c97ea77c50c3e6f8be9f4d2ebb225dc3a40edbda84
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://3ffc5be807fc25cec44947c97ea77c50c3e6f8be9f4d2ebb225dc3a40edbda84
                exitCode: 0
                finishedAt: "2025-07-09T23:25:38Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://ef26a29f084c9d74bc49dac6820d0215fcc5df5e1b54fa9c92a1bd613373f145
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://ef26a29f084c9d74bc49dac6820d0215fcc5df5e1b54fa9c92a1bd613373f145
                exitCode: 0
                finishedAt: "2025-07-09T23:26:09Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:39Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-24
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://e738074ffb6a5a319f3719e05ffdc96b888801bf06e3dabf55175fde7fdcb03e
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:15Z"
          hostIP: **********
          hostIPs:
          - ip: **********
          initContainerStatuses:
          - containerID: containerd://a8b9c8565d59b6d61aebb2bb29b6b8d419e02e8476ae346bea18cb9f9f3fe03e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://0b35f8fd966fceac6f7a076d40593d35be96d8b16f74fed18a2e5e2814f45f57
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0b35f8fd966fceac6f7a076d40593d35be96d8b16f74fed18a2e5e2814f45f57
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://a0b56ae187de8b021ff3b159371e951d5fabc8a98947be4f94c87b06d6fd52e9
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://a0b56ae187de8b021ff3b159371e951d5fabc8a98947be4f94c87b06d6fd52e9
                exitCode: 0
                finishedAt: "2025-07-09T23:26:09Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:39Z"
          phase: Running
          podIP: ************
          podIPs:
          - ip: ************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-25
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://3ec573dfdca5e80a45c72767b0e9b22cf03dcf8d038150baa715e84be70395a3
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:15Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://c96cbf810c0bdda6ed845cabe6f95b254646f8ea63d58869ecb2e7dbe5ddd9fe
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://9df4459518652355449db40f09302eab255729b941ce3047a083498f94300673
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://9df4459518652355449db40f09302eab255729b941ce3047a083498f94300673
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://e9c06381e2e4b55e5aeec5e2d384074edaf8e74002db8ade1c687cce6ffcd20f
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://e9c06381e2e4b55e5aeec5e2d384074edaf8e74002db8ade1c687cce6ffcd20f
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:34Z"
      - name: job-9ws19lbqpq-worker-26
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://9d76b003974355c61d0d5c847c21a2e35137fe49717b9234713e392490766e63
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://3854d880e97fa9e6212064be53e078bf4f33ec27f54740888502d22b5b480f7e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://84971ebed4baf0bbaaaf372cfd8eff8c3bd6d3fbe26d2363d5047b13bd64ac96
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://84971ebed4baf0bbaaaf372cfd8eff8c3bd6d3fbe26d2363d5047b13bd64ac96
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://a1fd7b63c16cff9e3a747742e028d59ddfb0ab65c72652d230133210945e1fad
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://a1fd7b63c16cff9e3a747742e028d59ddfb0ab65c72652d230133210945e1fad
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-27
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://abd209ee2d1fe90ef6700a5095b54580b7b01d06c3142c1044459892a8dbe1b4
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:15Z"
          hostIP: **********01
          hostIPs:
          - ip: **********01
          initContainerStatuses:
          - containerID: containerd://cb15cd0e1f4309b1a97969be49c2a7c575f90a54ef70eba3d22efad16048e31b
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://fec0acecb27d62697bb046dfaccd760cc6c59845469506079ecdaa3a6f6c1f64
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://fec0acecb27d62697bb046dfaccd760cc6c59845469506079ecdaa3a6f6c1f64
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://0aeb81fa728b23ec96458ea489359d4785e41010c20156c100d28c6883dc3197
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0aeb81fa728b23ec96458ea489359d4785e41010c20156c100d28c6883dc3197
                exitCode: 0
                finishedAt: "2025-07-09T23:26:09Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:39Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-28
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://c2356ff062fab9450189afbc7af273a22c7caf6e62e0b7e6b34b4333fe488653
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://a5ecde70d9172255ca00b390a224cf0ab0bb29e26675505d5f75ce83edf9debb
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://e2f595460a161853ec7d23cf89df0785412f652087c5b1177bafcf5c71ce30d0
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://e2f595460a161853ec7d23cf89df0785412f652087c5b1177bafcf5c71ce30d0
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://87826d5f985c00bef55bb048c242afd9fb62d1e0e00031ba0e69e39727a9de6a
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://87826d5f985c00bef55bb048c242afd9fb62d1e0e00031ba0e69e39727a9de6a
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-29
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:19Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:19Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://a7f4f9f56da460ce2cf5442781d71c6ed9933fef9e6a8d4a019e604df4ad3cc6
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:19Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://1576d335d040445900188c7be7f537a830cc0f7335af3dfa0c64aa4840b747dd
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://9ad4542c6a855198fade9e158bb6a8e6e6d13bf223682588c21ea11440e43e88
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://9ad4542c6a855198fade9e158bb6a8e6e6d13bf223682588c21ea11440e43e88
                exitCode: 0
                finishedAt: "2025-07-09T23:25:40Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:39Z"
          - containerID: containerd://ea5fe1275a386c1a7b8f733870525cfa7ecc311e03f3374a7ab5aceefda6c430
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://ea5fe1275a386c1a7b8f733870525cfa7ecc311e03f3374a7ab5aceefda6c430
                exitCode: 0
                finishedAt: "2025-07-09T23:26:11Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:41Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:34Z"
      - name: job-9ws19lbqpq-worker-30
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://5219df1d2d7e4de7579765cf57b314fb16e8f22c1c4d8bc4fdf62975d18c922d
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:17Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://e3947ca71df414c4dcbafb67acba325ce90e479179de73947557f6633ff12d65
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://622580a305728c6482e42400f79374ef93c944fa69680793e850822ece90df41
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://622580a305728c6482e42400f79374ef93c944fa69680793e850822ece90df41
                exitCode: 0
                finishedAt: "2025-07-09T23:25:40Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:39Z"
          - containerID: containerd://32f6f0035961db6279149302890b1b9c3202db76e92160b0c1577b8bb7dd09c9
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://32f6f0035961db6279149302890b1b9c3202db76e92160b0c1577b8bb7dd09c9
                exitCode: 0
                finishedAt: "2025-07-09T23:26:11Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:41Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:34Z"
      - name: job-9ws19lbqpq-worker-31
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://d459a7be333d5fe34d1997453258c480ebbc1dcc3453c4086f25d3e94bf74115
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:15Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://a93f7bf4d1707b6f8c01761acc94db9a09480e3a2c25632f9f133a255c50741a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:35Z"
          - containerID: containerd://0c98ea854aad704e952a8e9da9018fcdce566474004a9257ec47c7a78980dab5
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0c98ea854aad704e952a8e9da9018fcdce566474004a9257ec47c7a78980dab5
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://97f8bf64246488256025973c707f574e83d70e6fa2dc46953b197f22150a254e
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://97f8bf64246488256025973c707f574e83d70e6fa2dc46953b197f22150a254e
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-32
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://fbad2bf0bc964f7dd608a71da98caa7d8bbeffac93c9e22343f857b4b4cefa42
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:17Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://e18febb44a058bc0d89e8ded6a918b2b2743f58d01d87d9cf34275e23e30487d
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://721736f3b29631f3b0c79329d8fbfa8b816fdaf4e9fdc7f4358de0ec94059e8c
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://721736f3b29631f3b0c79329d8fbfa8b816fdaf4e9fdc7f4358de0ec94059e8c
                exitCode: 0
                finishedAt: "2025-07-09T23:25:40Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://8ab3fab6dee17dadace8920163df811d204bd47a0b05ca298d62d3af2ddacde1
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://8ab3fab6dee17dadace8920163df811d204bd47a0b05ca298d62d3af2ddacde1
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-33
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://c43264a93c197962e3ecb5e736c84d026207cad3bd283f473f2425fdd1a567b2
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://a1ec15555499538ab7f59ec7587f4114dabe4728875a81fea0f6ee2a227bdaa5
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://0120b9d6b7b98eb153d2f2a9fb73c03c018f4e24246f167a9ac40961a7e37399
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0120b9d6b7b98eb153d2f2a9fb73c03c018f4e24246f167a9ac40961a7e37399
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://1536b64bff86bab10c39057c17d32520a17e59108b6cb34943b408eb6571ac93
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://1536b64bff86bab10c39057c17d32520a17e59108b6cb34943b408eb6571ac93
                exitCode: 0
                finishedAt: "2025-07-09T23:26:09Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:39Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-34
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:39Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:13Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:19Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:19Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://cefbf687104c2eeea8836d4c13c745f06505b0c776ae7e394971c62493d0f6e3
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:18Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://1d8c5f81755c5a820abf43caff859248bbf022f8b12f7a3a82fd0f1d053249e5
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:39Z"
          - containerID: containerd://e04d9bfabdb685db9fa88e83aa3ec6a4a260047e7ee52ba85f2476d818625c2e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://e04d9bfabdb685db9fa88e83aa3ec6a4a260047e7ee52ba85f2476d818625c2e
                exitCode: 0
                finishedAt: "2025-07-09T23:25:42Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          - containerID: containerd://6abaa8d593679561dd6a8fa9bc94b9cf679f47180a340461e48a5a5c87d695cf
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://6abaa8d593679561dd6a8fa9bc94b9cf679f47180a340461e48a5a5c87d695cf
                exitCode: 0
                finishedAt: "2025-07-09T23:26:12Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:42Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:36Z"
      - name: job-9ws19lbqpq-worker-35
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:20Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:20Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://69d23121afa7093e5270356938dba45e460ca9f465e17abcc19031dc42378c9b
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:20Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://43c013577bdb3df455b6058300b783b4b5406305777e54921353588b17fb89ea
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://06d1ba0664a277de571d3e30d4c73edecb7c8c2f51a83a1df4462ee10dd0a9a9
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://06d1ba0664a277de571d3e30d4c73edecb7c8c2f51a83a1df4462ee10dd0a9a9
                exitCode: 0
                finishedAt: "2025-07-09T23:25:40Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://30a1f31a47862ef1f0a41401dc7fd99dab087bd5ead7392ddc1e20752ff8e777
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://30a1f31a47862ef1f0a41401dc7fd99dab087bd5ead7392ddc1e20752ff8e777
                exitCode: 0
                finishedAt: "2025-07-09T23:26:11Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:41Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:34Z"
      - name: job-9ws19lbqpq-worker-36
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:15Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:15Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://d691b41b6dd1b1887cb091a35a30595ab94d53f66ad9ad60391f46a5498266d7
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:15Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://3c390131a31bc9fa0bb0e00479a689fde040aca3637781f6efb4fcdb5e934535
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://2c564561187836c3d7611b150c35167445a8246c46cf257b05bb640ddc2c4783
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://2c564561187836c3d7611b150c35167445a8246c46cf257b05bb640ddc2c4783
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://3bdbb681fd8ce6c8d8b8a7e711353ab6f1a024bb189206a506d7ac21e2a35882
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://3bdbb681fd8ce6c8d8b8a7e711353ab6f1a024bb189206a506d7ac21e2a35882
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-37
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:12Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:18Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:18Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://8f152a05a374f815063c2b71c558a8b1176b6aa1372503f3a0e66fb073414fcd
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:17Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://317cb01e1ee516cac7607cc6cd5e6a05f979d29ccd09b40923f84638ce9cb0b3
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://faf725f6a324b377651ee553ad6911f4c57c5acc57b2b27e586acd0af375925c
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://faf725f6a324b377651ee553ad6911f4c57c5acc57b2b27e586acd0af375925c
                exitCode: 0
                finishedAt: "2025-07-09T23:25:40Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://f434d4d4c10c41eba307b379277a95a43f2f5afef8cb0c8da390b258da4c2a8d
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://f434d4d4c10c41eba307b379277a95a43f2f5afef8cb0c8da390b258da4c2a8d
                exitCode: 0
                finishedAt: "2025-07-09T23:26:11Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:41Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-38
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://cfbb10ea3827674c3a01a8e182aeaeaa0a171bd5e3cffb5a620165aac5352b9c
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:17Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://a6af22c834cd4d2afd38a634f325c7f441eab4681a8608e99f32a46041bbe1f8
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://9873d514fb83982b8d3702731a206201f1354bc1e3e5361c44896f1c2d0df15a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://9873d514fb83982b8d3702731a206201f1354bc1e3e5361c44896f1c2d0df15a
                exitCode: 0
                finishedAt: "2025-07-09T23:25:40Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://e127890e00f48019df01a2bbe11072ba5860f3704d16edca3df3a44d80d90c32
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://e127890e00f48019df01a2bbe11072ba5860f3704d16edca3df3a44d80d90c32
                exitCode: 0
                finishedAt: "2025-07-09T23:26:11Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:41Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:34Z"
      - name: job-9ws19lbqpq-worker-39
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://1c0bcfa4f7ab0ce3b0f6741c52f9f487748d8ff16566904bfea82d5565c60c74
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://355374a3206e09535dbbe2a8aa352ab3c13074f2d5c6b7daf3413c446d1a1762
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://c0ed95ca5bdd07397fe4d5e98e72b37909983ba4b44b6f31a8a8311d2f6ae830
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://c0ed95ca5bdd07397fe4d5e98e72b37909983ba4b44b6f31a8a8311d2f6ae830
                exitCode: 0
                finishedAt: "2025-07-09T23:25:40Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://3be9078bfbfa15c4dc2cdb16ba6806f1d4cdfa330b9b2adfef49c347471a3bee
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://3be9078bfbfa15c4dc2cdb16ba6806f1d4cdfa330b9b2adfef49c347471a3bee
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-40
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://7e08065c4a3610c4c8f25c11c55105e9d2011616136e024542cf3227e2a4dc51
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://275e7b5c40c4f52c58498aa51d8d928401feb7667e89454db289842199eafee9
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://27c2754259960219eddc8a74cbec78ed5da5391570afb73672075fe55f5dfce5
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://27c2754259960219eddc8a74cbec78ed5da5391570afb73672075fe55f5dfce5
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://3792fa2cc6b9aac063695a1fa896a17db29f915b8112d15d65ee2b3fab737eb2
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://3792fa2cc6b9aac063695a1fa896a17db29f915b8112d15d65ee2b3fab737eb2
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-41
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://27e741716af998eae77777efdcf281e394e57efad695c8dc6b8229498c226311
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://ebb7852ce2153246ee83c4c248c7e888f0dc44afac6eed8820124241a83afb4e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://e9679d37bef9553a69ece1810fb7e5ecb85086c35142f37724d9c8509093eee7
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://e9679d37bef9553a69ece1810fb7e5ecb85086c35142f37724d9c8509093eee7
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://c1ee4bcf928f0144d82d082e6c6ae19b1f45c57bce8899322df07a154377788a
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://c1ee4bcf928f0144d82d082e6c6ae19b1f45c57bce8899322df07a154377788a
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-42
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://55d3f879ec38ece6aaf697c8d57d12df5468f070134e40f9b250391d88f93646
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ***********1
          hostIPs:
          - ip: ***********1
          initContainerStatuses:
          - containerID: containerd://0c7e9e9b60b65ef530560a55f7dcff8bb8f1a5e2756a1e3522a76b7c673609e8
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://3f9586fd0882a7aaaf1b625511038d178cf66879c20391c616e0ace0e30827da
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://3f9586fd0882a7aaaf1b625511038d178cf66879c20391c616e0ace0e30827da
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://fbfbfbec6fd4ee4238804281a2f3ca2a58d8870c587461f6390a028871bba2f2
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://fbfbfbec6fd4ee4238804281a2f3ca2a58d8870c587461f6390a028871bba2f2
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-43
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://56c80868bd62d295ba35afdfe050a1d4673f03752f65388b5c14c923e25fb3a7
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:17Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://3d529779c0b93ef189af397ba9b230d47d15bb0370a25f79bd0a8e8b27b1d73c
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://8219f737e9e5aa697556850efeb1a16a4cba80c57bd6194ef3bc1f57bccb522f
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://8219f737e9e5aa697556850efeb1a16a4cba80c57bd6194ef3bc1f57bccb522f
                exitCode: 0
                finishedAt: "2025-07-09T23:25:40Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:39Z"
          - containerID: containerd://3a1c2bb79a51d09064c441c8b2625c54a96b3861786adc75d367abec970487d5
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://3a1c2bb79a51d09064c441c8b2625c54a96b3861786adc75d367abec970487d5
                exitCode: 0
                finishedAt: "2025-07-09T23:26:11Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:41Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:34Z"
      - name: job-9ws19lbqpq-worker-44
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://c1978d4587ae3b7ff7196e0f4abdf480eba5f2798d22c375a68a5484d0be7d53
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:17Z"
          hostIP: **********
          hostIPs:
          - ip: **********
          initContainerStatuses:
          - containerID: containerd://a22a9cdd77468e1d51799151dd6bd5b488f36c8ae9dd5b42eb66e3f8ec9414b9
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://a8d5ca746446b2c679c22a49518745975d0d9ebc4b0b2c570997af4b0d0ea36b
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://a8d5ca746446b2c679c22a49518745975d0d9ebc4b0b2c570997af4b0d0ea36b
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://fccf366eddc03e2722b88c89747d8aaca4ec05e6c436806a1d9a205e8d2187d6
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://fccf366eddc03e2722b88c89747d8aaca4ec05e6c436806a1d9a205e8d2187d6
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:34Z"
      - name: job-9ws19lbqpq-worker-45
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:19Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:19Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://a61c24c9f2ef911b89b56c907229288edef70ec661d14babcec069994254bc57
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:19Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://52309fa4958769eeeb0b2aa5c8fcd78a2629509795f6265915b05bb7f3ea17b5
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://8259e3e8abf4d23c5e4336698d8d08b4755d23dca0b58e98486287cbe3d27608
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://8259e3e8abf4d23c5e4336698d8d08b4755d23dca0b58e98486287cbe3d27608
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://0fcab039a74990ccd1dda83b831f6ebfaf548284f40c0ddfed6b13e8f38bdfc5
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0fcab039a74990ccd1dda83b831f6ebfaf548284f40c0ddfed6b13e8f38bdfc5
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-46
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://5a0b2da07b0d4118a7195383ea927939eb3707848b78cc4ca4bb90ab2316a4bb
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:15Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://9eb69f74b73821a155787975ae4e7675c9a2cdc5ec8e27a75de5a9725226e4ce
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://9490062b6a150db22881b173b67b0e8f745fe8799b084a8585102a0e292da593
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://9490062b6a150db22881b173b67b0e8f745fe8799b084a8585102a0e292da593
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://723f1f2163cc3fc6ce0fd786ba04f6e2c7511af3c1632c7f78e6d339004e74a3
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://723f1f2163cc3fc6ce0fd786ba04f6e2c7511af3c1632c7f78e6d339004e74a3
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-47
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://4369edc3d98521e5a39572807f0ff3a5af83486c1647ddad8cbd7e9e701d6ef4
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:15Z"
          hostIP: **********
          hostIPs:
          - ip: **********
          initContainerStatuses:
          - containerID: containerd://9f6249e0b25e0745752235de499a11b04b28790353cc58d2a8d58314cf9782cb
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://2a975fbe6c55b83d4ea459979b4c8a930674b2958e09932ed8eca3436d0f62a0
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://2a975fbe6c55b83d4ea459979b4c8a930674b2958e09932ed8eca3436d0f62a0
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://a7e6d484e6cf5a0108187a593811fb7791377a56b37c816873a07b66d0a50d43
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://a7e6d484e6cf5a0108187a593811fb7791377a56b37c816873a07b66d0a50d43
                exitCode: 0
                finishedAt: "2025-07-09T23:26:09Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:39Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-48
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://128c5d670f7fdd9d292d09e0e5a8f9e5a79c593c6aa0fe718565a862e0b8245b
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://64624ef0b65d4a2b80604de2f11e68395817674e3f898cc9ceba57f6cb284cba
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://0700e424ef7a48fb3db04da8237fc6c5820b65eb7c7f5550dfe8573de7f4476c
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0700e424ef7a48fb3db04da8237fc6c5820b65eb7c7f5550dfe8573de7f4476c
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://c43d06d38d17f2b9487da0f0e0ab8b610ee443303f16b70f3ebf45d3ffc0a4db
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://c43d06d38d17f2b9487da0f0e0ab8b610ee443303f16b70f3ebf45d3ffc0a4db
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-49
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://26ededa7e43c47de56ff449b56a9a7a7722ca067fac8bbc753acc653a0c764fc
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://a70c5ee7aa7abb93f6742581e4adf75da45bae157814dd0af26a6abcce77890a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://d6db00ce6357860e9363ad0cedcb245f5887a3ff044483fc340e80c2987688d1
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://d6db00ce6357860e9363ad0cedcb245f5887a3ff044483fc340e80c2987688d1
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://01b307f477cb27ede1fae45f54b938cba16b07a4b7d973da923e571d6cc21f19
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://01b307f477cb27ede1fae45f54b938cba16b07a4b7d973da923e571d6cc21f19
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-50
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:38Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:12Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:18Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:18Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://a6dffc54c3d68835e2218ac0985cbea83c4cb29c927fb40099c83d8c6520649e
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:17Z"
          hostIP: **********3
          hostIPs:
          - ip: **********3
          initContainerStatuses:
          - containerID: containerd://d01b4a463639e5908fe8731d9f27e2f07bc60c10a7e597fa5e2e13dec4d43f7e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://aa2bab6a65dcf72c576ef24f46405214e9d78055454f18d4708ee8d16caa3132
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://aa2bab6a65dcf72c576ef24f46405214e9d78055454f18d4708ee8d16caa3132
                exitCode: 0
                finishedAt: "2025-07-09T23:25:41Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:39Z"
          - containerID: containerd://3a7ec9f5f099b180d80c8c0862a5bb8f9e5b4060768ee8914b3d7cec6dd84a4a
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://3a7ec9f5f099b180d80c8c0862a5bb8f9e5b4060768ee8914b3d7cec6dd84a4a
                exitCode: 0
                finishedAt: "2025-07-09T23:26:11Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:41Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:34Z"
      - name: job-9ws19lbqpq-worker-51
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://3a487fab6bb85c7bfcdeeb7f315b35bef499d0a9d3b8ee26adc0d78cdeab701f
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://dd9ba99d212823f8622d4772ea58739e11b0da47a9049e1218be724ceb9a757f
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://d5c7f392cc3f31898fd86907adbb67546dc697125d7f000d1a0d1e62981e9852
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://d5c7f392cc3f31898fd86907adbb67546dc697125d7f000d1a0d1e62981e9852
                exitCode: 0
                finishedAt: "2025-07-09T23:25:40Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://cb104cd187740fa4edb837dc5685b4b7c03c447b2af396a0326ea85519051e99
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://cb104cd187740fa4edb837dc5685b4b7c03c447b2af396a0326ea85519051e99
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-52
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:40Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:14Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:20Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:20Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://3c08e5cf86aa32fa0b2b93b838e410baabbaa727cd0c6f1f9b3fb4a106d7c133
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:19Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://845be8c0be327eb607d4a272a779560e072facd0f7a6d6ba8cafe23dd13c0871
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:39Z"
          - containerID: containerd://a472ee13dc093d30ed14ef3edae8018eece9ba4f43ecb58d512144480a969dd9
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://a472ee13dc093d30ed14ef3edae8018eece9ba4f43ecb58d512144480a969dd9
                exitCode: 0
                finishedAt: "2025-07-09T23:25:43Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:41Z"
          - containerID: containerd://f25e7eaaa4a81bfc624fdc4f9d7dad7c497a2c96a9200f453614e8bcc1c01ffc
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://f25e7eaaa4a81bfc624fdc4f9d7dad7c497a2c96a9200f453614e8bcc1c01ffc
                exitCode: 0
                finishedAt: "2025-07-09T23:26:13Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:43Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:37Z"
      - name: job-9ws19lbqpq-worker-53
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:15Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:15Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://6964d9aa6012cfc707981e60c3112815a1ac173ea216e8bff525ef2ec6038988
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:15Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://56b12fd067a3fe8b413b3ef278d0b61e9c3f1d9612e206eedfa0055ffa745ffc
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://415e04f442f47e0e9189cea644d5ab7751271d351a05088aa7666f8487f7a389
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://415e04f442f47e0e9189cea644d5ab7751271d351a05088aa7666f8487f7a389
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://d714e1c6dd62d15f2b9a998f56e84dda91cfbdc643cb8f1bd8524c360ff744a0
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://d714e1c6dd62d15f2b9a998f56e84dda91cfbdc643cb8f1bd8524c360ff744a0
                exitCode: 0
                finishedAt: "2025-07-09T23:26:09Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:39Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-54
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://3c38e0b2018c14525df969cae0ff5013642137632a71137a53c52e1474eef95b
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://5cf22feabc46bcaec5f02732a4b6820da0e5a0ae0501e1919561c24e3eeb641e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://012be963dafa9a5e8ce6c962e0a1d14b59862a6f5beff5638f250fa392917727
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://012be963dafa9a5e8ce6c962e0a1d14b59862a6f5beff5638f250fa392917727
                exitCode: 0
                finishedAt: "2025-07-09T23:25:40Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://0be015b9727e0282ee8f0a84648d673f494453d040809135a945fd3ac23ae080
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0be015b9727e0282ee8f0a84648d673f494453d040809135a945fd3ac23ae080
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-55
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://b1e6ed031669c9f69480dc597b165be6a7bb55e69e7a458a8afc17ab18ee15f9
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://9421b8b4571e992d5cb65c9c4d1297313ea3f5379b46b9a9bb9e662009f2c215
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://67321a8611b1ccd75e8f4a8eac45566a5a595797c1c1f993dfda3fb5918c07ba
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://67321a8611b1ccd75e8f4a8eac45566a5a595797c1c1f993dfda3fb5918c07ba
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://1f7b7bdbf2a84609f003ba4357bc225f256f76eaec7fed9e3e5c1f27f1eda190
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://1f7b7bdbf2a84609f003ba4357bc225f256f76eaec7fed9e3e5c1f27f1eda190
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-56
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://77b8f93ca848758a6574353fc36ce327150d80c96541e8537141ac901a7270bb
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://073e7bed4975110db4e98139fe7711dc4546291b3b83ebe70dc02c4955f9ac8d
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://26ab80ffa86a932634c51fee9269b39a917100982125276d9c747ebe3dd2e28e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://26ab80ffa86a932634c51fee9269b39a917100982125276d9c747ebe3dd2e28e
                exitCode: 0
                finishedAt: "2025-07-09T23:25:40Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://5843b5830fef7c8c110b6385c01372e45effd19a872d682ea9ad99a95e5e221c
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://5843b5830fef7c8c110b6385c01372e45effd19a872d682ea9ad99a95e5e221c
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-57
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://f9ac16bb6bd38356312ca1732ce0566b6b8041a53794c0c3e543f1a98be413e4
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:15Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://99b36bdecfce451a6961a66973ce44374adb772ba118af7eef6b64253660a212
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://711bb86c03bf13c35fec9a5f13a5f7eb327326f6013407d904cebe45bb50ceb4
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://711bb86c03bf13c35fec9a5f13a5f7eb327326f6013407d904cebe45bb50ceb4
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://2ee66dc42dfaec5ad10337af8d0e5be99f77615f7deb584d0d83d3243cfcf275
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://2ee66dc42dfaec5ad10337af8d0e5be99f77615f7deb584d0d83d3243cfcf275
                exitCode: 0
                finishedAt: "2025-07-09T23:26:09Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:39Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-58
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:15Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:15Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://05ca49be0bcc4b0f98a3c8ffeece7a0aa16aa9e4f1cba8d98fc7bd6768228b5e
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:15Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://ece4899d5696e14a43a2b6eb2e04ef3d8944b6ddc324d7113a7e613c86e9f43a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://eccdb5cf09d67adba8903585ed131564888e79ff47c03d0fdbf76c245020065b
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://eccdb5cf09d67adba8903585ed131564888e79ff47c03d0fdbf76c245020065b
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://036251b337ddd723dd7f832d35e671ee45fd6e7fa8aad9ac4b31d72e4e7a6b1b
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://036251b337ddd723dd7f832d35e671ee45fd6e7fa8aad9ac4b31d72e4e7a6b1b
                exitCode: 0
                finishedAt: "2025-07-09T23:26:09Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:39Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-59
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:38Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:12Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:18Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:18Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://b34b5410c4cd890d9a9276bbeeeae707959e3d1ddb504a22dd58482383ac8be0
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:17Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://cb4f195a7d6e6686c70c4158e85cf490182e97580489fb20829970b29cf4fddd
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://b9cdf8fc724e217c889aea1c4aafa6cd021d5f1338be0bb95a90dfdcb5298373
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://b9cdf8fc724e217c889aea1c4aafa6cd021d5f1338be0bb95a90dfdcb5298373
                exitCode: 0
                finishedAt: "2025-07-09T23:25:40Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:39Z"
          - containerID: containerd://0a33eb624e7210cdf2eb2dc07b1e20bf00ad5b70907286077603e7310ff0e118
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0a33eb624e7210cdf2eb2dc07b1e20bf00ad5b70907286077603e7310ff0e118
                exitCode: 0
                finishedAt: "2025-07-09T23:26:11Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:41Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:34Z"
      - name: job-9ws19lbqpq-worker-60
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://a685c6c673e10adb0b3041ee2dd1a3566d9ffa9a16276effb82c738b9fea113d
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:17Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://f2116bf5383a1c4a8da3ab67016426aa8c2e04a124bff1d5cf8ad4543f981249
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://8498ac649f9012a5ad87b5412b1866276be73ed618828daa3490a314518308d5
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://8498ac649f9012a5ad87b5412b1866276be73ed618828daa3490a314518308d5
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://daec6adee31acfb183cfbf0fd509cd0e2ed090dfff99db6de6018dc4c497628b
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://daec6adee31acfb183cfbf0fd509cd0e2ed090dfff99db6de6018dc4c497628b
                exitCode: 0
                finishedAt: "2025-07-09T23:26:11Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:41Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-61
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:36Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:11Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:17Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://ce79ba9b137205639c3c4a3cd0cd4a6a85462eda065aba60ac0f691fa5e11ca1
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:16Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://59385452256132e848ea3ddb75f1c677b1869e4fcc2aafe995b85f53fdc0ce1a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://a00b08e4011a3034c413435e3022ba747f16a7b21d73c85a8d012d3a9a7cf635
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://a00b08e4011a3034c413435e3022ba747f16a7b21d73c85a8d012d3a9a7cf635
                exitCode: 0
                finishedAt: "2025-07-09T23:25:39Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:37Z"
          - containerID: containerd://2c5e30cbad961c2c996dfc4c59ca37b0a8d8470b2179fcf16177b889ae09f448
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://2c5e30cbad961c2c996dfc4c59ca37b0a8d8470b2179fcf16177b889ae09f448
                exitCode: 0
                finishedAt: "2025-07-09T23:26:10Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
      - name: job-9ws19lbqpq-worker-62
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T23:25:37Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T23:26:12Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T23:26:18Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T23:26:18Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T23:25:29Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://8f98b0f67aee6b70f03bc10cb12ff1cde81cd53a3b10e5f0f00f50607728803a
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:26:17Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://e8bfb448d0a246df0bba04395c69badf3e96a7e05321bba51647c794d20f72b6
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T23:25:36Z"
          - containerID: containerd://dc80582dcf70b0a638aeab6fc86ffcd434de3dd3bf78c5676a455402cb2bbdf5
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://dc80582dcf70b0a638aeab6fc86ffcd434de3dd3bf78c5676a455402cb2bbdf5
                exitCode: 0
                finishedAt: "2025-07-09T23:25:40Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:38Z"
          - containerID: containerd://ee3ff428c073755c54aa4dfaeb2af65016e93a3b501977690ff5dbc930bfe013
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://ee3ff428c073755c54aa4dfaeb2af65016e93a3b501977690ff5dbc930bfe013
                exitCode: 0
                finishedAt: "2025-07-09T23:26:11Z"
                reason: Completed
                startedAt: "2025-07-09T23:25:41Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T23:25:33Z"
  restarts: 1
  startTime: "2025-07-09T15:51:30Z"
  status: Running
