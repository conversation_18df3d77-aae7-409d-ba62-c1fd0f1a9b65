apiVersion: v1
kind: Pod
metadata:
  name: job-9ws19lbqpq-master-0
  namespace: project-llm
  labels:
    infra.shiyak.com/job-id: job-9ws19lbqpq
    infra.shiyak.com/owner: llm-user
    infra.shiyak.com/project-id: llm
    trainer.infra.shiyak.com/managed-by: trainer-controller
    trainer.infra.shiyak.com/name: job-9ws19lbqpq
    trainer.infra.shiyak.com/replica-index: "0"
    trainer.infra.shiyak.com/replica-type: pytorch-master
    trainer.infra.shiyak.com/restart-attempt: "0"
    trainer.infra.shiyak.com/type: PyTorchJob
  ownerReferences:
  - apiVersion: trainer.infra.shiyak.com/v1alpha1
    blockOwnerDeletion: true
    controller: true
    kind: Trainer
    name: job-9ws19lbqpq
    uid: d9e5902f-897d-428b-bb08-6be871cbcb71
spec:
  containers:
  - name: nginx
    image: registry.baidubce.com/cce/nginx-alpine-go:latest
    command: ["sleep", "9999999"]