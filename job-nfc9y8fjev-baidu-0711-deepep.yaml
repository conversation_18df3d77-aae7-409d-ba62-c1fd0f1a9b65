apiVersion: kubeflow.org/v1
kind: DeepSpeedJob
metadata:
  annotations:
    infra.shiyak.com/job-name: test_clone4
    infra.shiyak.com/job-owner: zuti.he
    infra.shiyak.com/job-type: deepspeed
    kubectl.kubernetes.io/last-applied-configuration: |
      {"apiVersion":"kubeflow.org/v1","kind":"DeepSpeedJob","metadata":{"annotations":{"infra.shiyak.com/job-name":"test_clone4","infra.shiyak.com/job-owner":"zuti.he","infra.shiyak.com/job-type":"deepspeed"},"labels":{"infra.shiyak.com/job-id":"job-nfc9y8fjev","infra.shiyak.com/owner":"zuti.he","infra.shiyak.com/project-id":"infra"},"name":"job-nfc9y8fjev-baidu-0711-deepep","namespace":"project-infra"},"spec":{"replicaSpecs":{"launcher":{"replicas":1,"restartPolicy":"Never","template":{"metadata":{"labels":{"infra.shiyak.com/job-id":"job-nfc9y8fjev","infra.shiyak.com/owner":"zuti.he","infra.shiyak.com/project-id":"infra"}},"spec":{"affinity":{"nodeAffinity":{"requiredDuringSchedulingIgnoredDuringExecution":{"nodeSelectorTerms":[{"matchExpressions":[{"key":"nvidia.com/gpu.product","operator":"In","values":["NVIDIA-H20Z"]}]}]}}},"containers":[{"command":["/bin/bash","-c","echo Bootstrap launcher ...\nif [ ! -x /usr/bin/pdsh ]; then\n    echo /usr/bin/pdsh not found, installing ...\n    DEBIAN_FRONTEND=noninteractive apt-get -y -q update \u003e /dev/null\n    DEBIAN_FRONTEND=noninteractive apt-get -y install openssh-client pdsh \u003e /dev/null\n    chown root /usr/lib/\nfi\necho Execute user command ...\nsleep inf"],"env":[{"name":"NVIDIA_VISIBLE_DEVICES","value":"void"}],"image":"registry.baidubce.com/ai-native-dev/aixl:latest","imagePullPolicy":"Always","name":"deepspeed","resources":{"limits":{"cpu":"2","memory":"4Gi"}}}]}}},"worker":{"replicas":337,"restartPolicy":"Never","template":{"metadata":{"annotations":{"k8s.v1.cni.cncf.io/networks":"network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca"},"labels":{"infra.shiyak.com/job-id":"job-nfc9y8fjev","infra.shiyak.com/owner":"zuti.he","infra.shiyak.com/project-id":"infra"}},"spec":{"affinity":{"nodeAffinity":{"requiredDuringSchedulingIgnoredDuringExecution":{"nodeSelectorTerms":[{"matchExpressions":[{"key":"nvidia.com/gpu.product","operator":"In","values":["NVIDIA-H20Z"]}]}]}}},"containers":[{"command":["/bin/bash","-c","echo Bootstrap worker ...\nif [ ! -x /usr/sbin/sshd ]; then\n    echo /usr/sbin/sshd not found, installing ...\n    DEBIAN_FRONTEND=noninteractive apt-get -y update \u003e /dev/null\n    DEBIAN_FRONTEND=noninteractive apt-get -y install openssh-server \u003e /dev/null\n    mkdir -p /run/sshd\nfi\necho Launch sshd ...\nexec -a /usr/sbin/sshd /usr/sbin/sshd -D -e\n"],"image":"registry.baidubce.com/ai-native-dev/aixl:latest","imagePullPolicy":"Always","name":"deepspeed","resources":{"limits":{"nvidia.com/gpu":"8","rdma/hca":"8"},"requests":{"nvidia.com/gpu":"8","rdma/hca":"8"}},"securityContext":{"capabilities":{"add":["IPC_LOCK","SYS_NICE"]}},"volumeMounts":[{"mountPath":"/dev/shm","name":"dshm"}]}],"volumes":[{"emptyDir":{"medium":"Memory","sizeLimit":"1Ti"},"name":"dshm"}]}}}},"runPolicy":{"cleanPodPolicy":"None","schedulingPolicy":{},"suspend":false},"slotsPerWorker":8}}
  creationTimestamp: "2025-07-15T06:38:10Z"
  generation: 1
  labels:
    infra.shiyak.com/job-id: job-nfc9y8fjev
    infra.shiyak.com/owner: zuti.he
    infra.shiyak.com/project-id: infra
  name: job-nfc9y8fjev-baidu-0711-deepep
  namespace: project-infra
  resourceVersion: "3749450"
  uid: d3d6311d-948c-484f-b525-0e12592cd886
spec:
  replicaSpecs:
    launcher:
      replicas: 1
      restartPolicy: Never
      template:
        metadata:
          labels:
            infra.shiyak.com/job-id: job-nfc9y8fjev
            infra.shiyak.com/owner: zuti.he
            infra.shiyak.com/project-id: infra
        spec:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: nvidia.com/gpu.product
                    operator: In
                    values:
                    - NVIDIA-H20Z
          containers:
          - command:
            - /bin/bash
            - -c
            - |-
              echo Bootstrap launcher ...
              if [ ! -x /usr/bin/pdsh ]; then
                  echo /usr/bin/pdsh not found, installing ...
                  DEBIAN_FRONTEND=noninteractive apt-get -y -q update > /dev/null
                  DEBIAN_FRONTEND=noninteractive apt-get -y install openssh-client pdsh > /dev/null
                  chown root /usr/lib/
              fi
              echo Execute user command ...
              sleep inf
            env:
            - name: NVIDIA_VISIBLE_DEVICES
              value: void
            image: registry.baidubce.com/ai-native-dev/aixl:latest
            imagePullPolicy: Always
            name: deepspeed
            resources:
              limits:
                cpu: "2"
                memory: 4Gi
    worker:
      replicas: 337
      restartPolicy: Never
      template:
        metadata:
          annotations:
            k8s.v1.cni.cncf.io/networks: network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca
          labels:
            infra.shiyak.com/job-id: job-nfc9y8fjev
            infra.shiyak.com/owner: zuti.he
            infra.shiyak.com/project-id: infra
        spec:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: nvidia.com/gpu.product
                    operator: In
                    values:
                    - NVIDIA-H20Z
          containers:
          - command:
            - /bin/bash
            - -c
            - |
              echo Bootstrap worker ...
              if [ ! -x /usr/sbin/sshd ]; then
                  echo /usr/sbin/sshd not found, installing ...
                  DEBIAN_FRONTEND=noninteractive apt-get -y update > /dev/null
                  DEBIAN_FRONTEND=noninteractive apt-get -y install openssh-server > /dev/null
                  mkdir -p /run/sshd
              fi
              echo Launch sshd ...
              exec -a /usr/sbin/sshd /usr/sbin/sshd -D -e
            image: registry.baidubce.com/ai-native-dev/aixl:latest
            imagePullPolicy: Always
            name: deepspeed
            resources:
              limits:
                nvidia.com/gpu: "8"
                rdma/hca: "8"
              requests:
                nvidia.com/gpu: "8"
                rdma/hca: "8"
            securityContext:
              capabilities:
                add:
                - IPC_LOCK
                - SYS_NICE
            volumeMounts:
            - mountPath: /dev/shm
              name: dshm
          volumes:
          - emptyDir:
              medium: Memory
              sizeLimit: 1Ti
            name: dshm
  runPolicy:
    cleanPodPolicy: None
    schedulingPolicy: {}
    suspend: false
  slotsPerWorker: 8
status:
  conditions:
  - lastTransitionTime: "2025-07-15T06:38:10Z"
    lastUpdateTime: "2025-07-15T06:38:10Z"
    message: DeepSpeedJob job-nfc9y8fjev-baidu-0711-deepep is created.
    reason: DeepSpeedJobCreated
    status: "True"
    type: Created
  - lastTransitionTime: "2025-07-15T06:39:54Z"
    lastUpdateTime: "2025-07-15T06:39:54Z"
    message: DeepSpeedJob job-nfc9y8fjev-baidu-0711-deepep is running.
    reason: DeepSpeedJobRunning
    status: "True"
    type: Running
  lastReconcileTime: "2025-07-15T06:39:05Z"
  pods:
  - name: job-nfc9y8fjev-baidu-0711-deepep-launcher-0
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:39:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:54Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:54Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://0e08a9bf73401ef1b3c5900434295b3ff37416c35248f370f126849bf82a944d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:54Z"
      hostIP: **********
      initContainerStatuses:
      - containerID: containerd://971d979159f23904adc60b2ec008fcf0b8c6b72a6af1b02cc875edbbc08a0e39
        image: ghcr.io/shiyak-infra/deepspeed-init:v0.0.1
        imageID: ghcr.io/shiyak-infra/deepspeed-init@sha256:e17faad87f610e82216dde59b9422c9412b362cf37ed6e775c13d9e145074de7
        lastState: {}
        name: init-hostfile
        ready: true
        restartCount: 0
        started: false
        state:
          terminated:
            containerID: containerd://971d979159f23904adc60b2ec008fcf0b8c6b72a6af1b02cc875edbbc08a0e39
            exitCode: 0
            finishedAt: "2025-07-15T06:39:53Z"
            reason: Completed
            startedAt: "2025-07-15T06:38:50Z"
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: Burstable
      startTime: "2025-07-15T06:38:48Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-0
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:56Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1e4eaf9339c721202d0a4d53ea469cabbda0ee9d82a785545de0fc4bdf61143f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:03Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:56Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-1
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://4365c1d819c0f8a351f32cc061a966ac54d422a4d7a1edaaf2e0b8d231bd8c36
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-2
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://0d12685ba0b078c9e8c55079393d0c0dfbe759effd17bbc311d241c97261a9ea
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-3
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b1d3373ef2e6052932dad2d2b4082cf3f2603f3a3443b702fde78828648c55d7
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-4
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e0a35ef9c48c7b87841211d096c5765df135628fdc72197588ecfeac7f3a07ad
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-5
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://59a2fa1b569b72ff04ffa903d6931b210f74a82ca842ec9570bc8ed1f3751717
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-6
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ccbce77c3f11ba4e4a7e54f4bb4c84e0d567ce1c2011f8403fbb28fdde59ca6d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-7
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://dc8f2a3a9bead0cbf43134917df78bede373d88769b101de0a2bd9ec800a2e0d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-8
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://793b2e5c790072bfaa021279a93b75d322d18d4aff45edae97c3d642756d3125
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-9
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:54Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b567cc2d8c0e7135194196459b70b2f624c5bde83e079f8e529497a729136945
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:54Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-10
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://74b02701606e69ed94ffe405c9072ae6897f5efdaa1435e698e21e49ca21335c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-11
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b3070006e5f446a227ebc83246ee8699db22e5699bd58055912a121cef740598
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-12
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://10a0ac8b902ca13c12eb2943047ca29aa93e2a7fdb908220f6d56216fd2d5388
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-13
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:55Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a3c910dc9e658422828531bcbf94bf0f6058b7633b69239950a7531b5c00cfdc
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:02Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:55Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-14
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6fbf8682e73af113f25050f5e4ab24fd4b7246c028a85f06f022d20c35400ee8
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-15
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d1f357aa622aea0facce709f78086c9a99a8c158ef06fad9d79e4e2d4351382b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********1
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-16
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://9d72e4d2508a26af5d09a4ee6d7604e23d6a9cac38c1e788e369ba541bd53c82
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: ***********99
      podIPs:
      - ip: ***********99
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-17
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:38:59Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:38:59Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:38:59Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://2ac4f881bb18706c47fe8ccd24ab731d09ebb6065196521ddb649926aa896c01
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-18
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://265c9c7cd97f4de3f997e84c24a50186a9626502497c2f17c8cb6af8d693e7e9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-19
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f1a9c6fb2f37484881525f0fcbb0a0675a24e3d84d517f4a5a7e25ae856efa91
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-20
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7d31a0c108c65c2236614f933d8cd772baddea7cb81ec2b9a00746e4b13c6d53
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:02Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-21
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:57Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1b2cfe587715e77c6c8f70a646450fa516975bf0e4acb8cb1dedd628ba3439f6
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:04Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:57Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-22
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://53b67a1c6094af25b5821daaffeea3600b3e6abeb2f5bdbff84662596716f23b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-23
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b9604baf2351b7a420c4b8cdb3110214fceef6402335dfd10540ae11194c727d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-24
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://19572a60df59a6ed92c438671d7da6367ed3ee1f0a4c97a247c03ca52a7616c3
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-25
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://24194fa92d7dfca661b79fa508a649818ce4129eee227748e9b82ec7d8b64afa
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-26
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://bc2ff3429a7df5e0fef00e96d86ac4c49bc49e12c48cd5e0ca59279af3e15c61
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-27
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://93d5598d025a0d86176aca6925507e113d2b52b8c1fdb3ba1c05528636e1c37f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-28
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://71abfa1e040dd1e7ad3772133dd46d46f49689a44c9151b9f56571f1d92f8c60
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-29
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://604a385bd9358ad9d53d1228d8d05178ad99ec3db68dae4b81a9c9d6ea91e5f1
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-30
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d53a43709302eee790e6cadaef869b160b83fb4b6fb71b823c9cc3466c9613db
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-31
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e2fd191907ef731615eba0be945ae1df0a1536432e3bb25a0b1e12a73f459443
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-32
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b2107811a8d596392b0fb8854f7965c86bda6c3ced6e4d9f8aaf635f3691c7d8
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-33
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://bf0b62c6a5884cdd9bf6786aae32dd3361e856b31d6c84baff0383167d174dad
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-34
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://902fbd72ee75326c68ed95e98132847ef10d96cbcd1ddf777b5504b4f056f676
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-35
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://3496545e7bf659c5765aff8abe310f982b3c03e2b232035b59fc3c6732c0a594
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-36
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://803f85d64749a3feb26f56bbda39f41ed900281c55ee65ff7811583b829510c8
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-37
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7575f28d70ccae1528b7b76db9be2f534f7bc859e4529040dd5101c2239e1321
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-38
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5e0cac1f08e45e034beb9534c005e1c6d0d17804d22f850e709b030dd0b422bd
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-39
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://94efb46857d47eac0fdf9c037bbe4e6af2710686b3f1073bb7ce198312e22c98
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-40
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://9a3e29e8e0d76fd64d077c22bd54ba81ecc9b3d28aa9a430d8eb892b51bbb3af
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-41
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://10d354f325b410ac319e6421ce044bf2e9c82b92a4dc2f8e9fcdea5c670e8eae
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-42
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7e4b322a8b504c46cb7de966c01bb91a82aa6678ef99ac9f0381f82a30ccb3b6
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********3
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-43
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://47682ecfc4691daa1e618a76fb1e8252e0e5e8522ebec0db24bbb8ed585b7334
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:02Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-44
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ce460bed6aef9613f0a6a3c2d67bef9b7922cca2d3bad8e0ac42bcdb13e9b8fd
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-45
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://9bc7d9f2e7148fd8651a16ad9cdad9171281825bf214851579c2708002d49934
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-46
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f98488c6650e7afa1b7809be702728e807dad424f12311b70b7e48ef2c8d5188
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-47
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1fde0e5859fc79a1f817e8a2be53e741b960fdc98cfd1b08a1e74cc1e1487f89
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:03Z"
      hostIP: **********3
      phase: Running
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-48
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://31acc5ba022d951086b2fc3c5922bd7e5d82dba0f4a8607164fad05207b3fcca
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********2
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-49
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b3858bb2f57a751425798b07cde06560ba152281145288d2eee2b47364d4fd4d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-50
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b85c103e0f7c4476b0deffd560917db199135257e071d0a574fb8594f48f000c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-51
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://9b19b23f4bd477212d5d95784e8ccf352c99611bb266f0d80d7c3b6f108255a4
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-52
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://4451332784bef95b883dcb828ccce3ff31df835a1289cce35feab2f11532e223
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-53
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:38:59Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:38:59Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:38:59Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://69945f9c68160100e2bf242e4f6a002ffd16a3b98aed100a312b3ece6737922a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-54
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://31afd9ec13f6201301e1c2882f2f8b3af3c7e501d0cec2fb4a2b517e8efc3775
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-55
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a7dba5b1e24131467703c11f930c28c040942c15f6211699877025f274254b23
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-56
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a6e0be4821ea3268c8be924eb23f939ec0052ec78f42e39a4dce13ec26d7b828
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-57
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://691e1ada64bc4ed930c53da2d50ef4935713b49b33cb03d192abcf532ca6b540
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-58
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://774ab1ac1e1465cdb007f159c18d709d7affbf251984a4356ee732fea5263f0e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-59
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://bd842fd0c6625e40850ca92ce23d5c0dbc94a7cf789436646b2c341bfc45f85d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-60
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://528d2ac4a0441b1b9dac9a32b4dd4303b09dde2dc3605593f65478f78a6d2b13
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-61
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://55f8032fe5a0b572308c024cda1f91e11d289da2a2701e07d637a87eaa2101cd
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ***********7
      podIPs:
      - ip: ***********7
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-62
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://73dab23555309e7042f2da40e8c013fef28685f41d76b67e50bf5062549bac8d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-63
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://92ba28eb05248fb09731ccf4c81fc14c4d6501a2a3f8234053a6af456e1b1753
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-64
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ff661e39dc21a8280b33104524dde5a1f63e9682f382378c0aa8c7870af56275
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-65
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://416ef982c7c02b013676dd30318e73fb080dba8dfa0d4031b2aa6c9e4663ec50
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-66
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1bf4982e5a223b93d94c31c1241d4dde67e2f5a90f14f483a345442ab4a965c5
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-67
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://3530d7a359cdd5dedd5a285a81596ea6ddf5f2e3a2af7a66c7bd5c7c4e9f226c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-68
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a20da7012d07232a8d068d32d5edc0b3bbff71778eadca749e69df1fbaa86273
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-69
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ec2dccd88b7e2d98ab4639a630bd844df805e08ad234a69f84d760a798cdbee2
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-70
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:38:59Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:38:59Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:38:59Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ff0b30daf140ca89c8c031835b9ff24c60d7b87a070d2720da12e5374cd37636
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-71
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://2e37857a55e5bca891070f5ba7f01ba6c03f85ffba64ecb192da74cdbb2d375c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-72
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ea808e50b962c0ae999dcf708b104a403d3d79c87eba31952bd1f667cf26988c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-73
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://156198cd7e6a488f8243e825e5e9d54a3efd7c0cc5793e318d4fa925fd4aa31d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-74
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b789e586bdc3a13efdf330caaf5c3d7c7efd7d128d90c2dbd2c49d44470fd310
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-75
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://99b3325eb03c8a89521642f2d948ca98ca6b0def587a40dd927613d10ae5e6d6
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-76
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d4b2074f7fcfaa81bfa92621d7084f70f374acb0e867d9781af3c576e35965f8
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-77
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://52572f36f61b9523c60cf1a9f78f0a46e00c3825101042b972d872f9cf9e0c4e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-78
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://9cf23f695c13aacee751f05f99e8b0a3ca30f0630c471c4d800e1ac8e06f9bf4
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-79
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:56Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://3882740ae10bce6c882ddadf032ffbb6ae91b76bbcfaa195dc7018d1acb9d9a2
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:03Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:56Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-80
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://cfaef5ca6dfccbd784f704772929c5c70b2157b987ce0a7da2326bb7c77dd7ae
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-81
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f66602dd7e8e0a9759c524ca1de2f21182f5540a9caff9192c5ca70c7e5d32d6
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-82
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://0205a5572cb07a98c622407bd5a75829e951619bf4a1ba48ee118833ed4c7cef
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-83
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6790fa78c379a5a27257d801404c81bd4e8017525536c9242ecdb8ffe9418e65
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-84
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://0078a015d781575dc9627578c4b72fcb588b080c541df3359685343f3d0e6b31
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********5
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-85
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:56Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://48d9b6ffdcab1da7f3f34018d9915747394eea138fa63f358b65e5e81ede690c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:04Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:56Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-86
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://29553041fd51db64800ef157f2dea715df71ceb772c3082e751af54204160954
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********5
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-87
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://3f05cc15c3687b98664ba725ff4a59780e310c2f15d6b1b0967a276e043775ea
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********5
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-88
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://4d4d42501966feb95a014a948040f857c2c61c06d466badec8faffe2ebedb396
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-89
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://39dc08c7e8097b21fc79be6162f9eb252cf17719752de0a9e07a188d9a586372
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-90
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1b13dac60b82def4bd4fecffb1229d88123fac00eab9e78ea18787c012ce93a9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********7
      phase: Running
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-91
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://065f1268a682953bdf7a00b70c92ec4a549f79943cdb97a0b22341d6734e1853
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-92
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://55beedad9cd7efb9506a463bb9c650428f18200c22f63c7db3fdfbecf9807079
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-93
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://71c5f0bb47c87dd3cbab07e07ceb488dc585f9804e1a253bccb557a7560e7a5c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********7
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-94
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:56Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ae9c919bef08d32a4d9a86588fc792f58fb000d4d754494696da132989980460
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:03Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:56Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-95
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a5c0659d9695c5a2d5feb088675e4c70be3d6b4745718eaf0c747e36ffbdabaa
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-96
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1971bcc95918af9d2580dc7f65b117f37f7c3aee44322f621631392fb0989814
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-97
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://4456adcbfb7624ecd47c0ae45029d325d54031ff84f9f0c98aa6e9f130324219
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-98
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d621d1574ee30882aea41ae1060c2865559da062603b6fd7b77d74a32a73098e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********8
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-99
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://dec50661e6be7d4796cb3203f188cf0ca08598654fc56ccf3a40e86b2b807339
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-100
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:54Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5e897e9887b60b66564c9e0997b69cbbef36d1bc902696b11b629227bb17c48c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:02Z"
      hostIP: *********8
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:54Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-101
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d3ce8ecb971eeffec938a69c17c06c9b9ea33be381a200f81ab7aadf99a04526
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-102
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d662c373dbc1820dc03f92c7f8e312e300538431a18463b861017872b82d2e4c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:03Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-103
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://07be65e71715cbea1b238ef887736233a18e16c154217bb8530922558998dfc0
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: *********2
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-104
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://215679744f083747cfdf68a9fb9ea5b7c4401d93dccb35fef559d0e920051088
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********6
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-105
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://9a88855997c3caf4b53e2748d9d16d8cc53cdeed8780cdff0442a4493d52a998
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-106
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7bfbad61d5081406b5a33a780a4a9d2bd8caaef895424385ae6ba7283d048192
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-107
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://0ac39307e207a1d691cd35d54bc5fc0323af10d80c975cd1c29ef6606a2f16cf
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-108
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://9223028d705a21f710daaf77ba1b880ce0108cbb4d1b5458b24c7c5f5fd4d1a3
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-109
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://72407a85966c4380f75b2d943bed1b3b92e0874b95000e9f8b12ce3c319123d2
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ***********35
      podIPs:
      - ip: ***********35
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-110
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://3a4551f501992f42ff75b66fd063b9e33d1fb89d131ce8c1d48ce5f099313e0e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-111
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://65f14905ad76f297725f611bc5a559f9fa5f8425d04bf6f6bbce082ede4c54b9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-112
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://218dce6a169c3ae4f636ddbf38ee231f81fd82791a69a8870a7cf3e4e480f7eb
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-113
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f28184fbe23286fb40a1ab2d233f81688ebe3c786a90c4f7d97ec3f832f0ef47
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-114
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c27b1087cc2313deb87076b71ebc1aeec94aacfa2676971bf5d3595bd7441e20
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-115
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b7cbe57510db4accc9c8eecba9c5447b20a3a225ae06861b3353ad1110298536
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-116
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://3b5a6ed066072eb5988f5b91f864508ed344210662846d5971e4af8abfbf9387
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:03Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-117
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5d3b92030ebdced41c2e83182b7938f4701dcc16fa7994d6bf088ac16c33cb4c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********
      phase: Running
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-118
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c3580dcd2f134abe5a1fa5c920eeff2c4cff238382960d5f4037962738c35340
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-119
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ff3ea86f1741c7e3819bef6b9cee8cfb68436abab03f9b9c6a44ecc69ef8a181
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-120
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://10fa90c6a3f068aa76e31a6b7fcb5961ef3be8b6976469348e68b0ebc9cccf51
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-121
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a06ee12bfe623b4909ff91610bff575bd87bf9540c4b0f088d6d23d805b8b4ed
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-122
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ae4b724ff31dab46830a3f3b8e94af17e6fc9bdf7e56538de2735d3c3188c89f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********28
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-123
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://cbfa536e352fe04809336d517d5e3b03e597fded212ba473fd97133e53c91041
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********3
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-124
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f1e1122edf316d51b0b29a1df2376f52b8fdfafaed20910801be05deb2a7a6a7
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:02Z"
      hostIP: *********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-125
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a69bac2a3b143ad34397fa371e13da4b8f4d550ac420dc5def1416e54e42e2c4
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-126
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://8b63b79845351d82027985ecc4fb9cb85e8c6068bc209d3353092f0e8942911b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-127
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c3d5e7f66958ffb4721ccf26f6781e6292f22b9a29eab58d7b2c9786a4003769
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-128
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ef110d9abf61f6ada8a0346d0cac4276e5f856085d09dcff67a1bd0201b7521a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********5
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-129
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://4cb7b0d40fe6972ba99d14ade00976b343449e1c18db3134c281aae913443bb0
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********6
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-130
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c8011991b350320fbf392092ea8308140defddb54a35ccbe2d5c2acb1b97c788
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-131
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c789b45291eeac30b90ff552a1c8253c195afe43bf402dcf4ff5fcf3c4f379ee
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-132
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6dd05eabd76bcf6501d1d2a00c174f139f6742225795f808a069227666f7e4f9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-133
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b8e41f31e7da683ebb96bcecd1af2a4aff00ea2fc7c6747ff656cec58c076ad7
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********9
      phase: Running
      podIP: ***********7
      podIPs:
      - ip: ***********7
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-134
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://21249129e7cd0f3fe3f47828a4b8c8b595ea86ea663db3f8ac30fb530b5e9268
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ***********55
      podIPs:
      - ip: ***********55
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-135
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6c725bb788fed15202c5d9c8f50862dd27768828aa9bf5a5ab655a5000a1acd6
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********0
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-136
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f2a7e207b2e64e8e0d31d86b46551a758a27fc410198c41a1d230afe6837df79
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-137
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b440f8c868ed24ed5dd7ab027486f7b5b546ef574c7f1c81a29f7317901996f9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-138
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://67dba0c23134e19ab1a7331d1d6d7838c87134db9e404dbaa830c3bb23e3e47f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ***********2
      podIPs:
      - ip: ***********2
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-139
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://146b776a3f6bbef9b0ccaddbd6556b08da76678a5949aa70598e244eb69cb043
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-140
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://37b2acf873218daf489752309e54f15db85983a4041a83402977fd444017f250
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-141
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://be4dcee9a56571cb2a5b4c5653b4b53c15d67be1c91e00c2f9197d95b3dd9797
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ***********64
      podIPs:
      - ip: ***********64
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-142
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://4d7f22e5e49bab9a86ff9aeb0549fd586b940184a1120a5707e82c24b5acec82
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-143
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7203a18bfa1415778c2cfeec0010dbdf448e45f4c9e8e36dedb6a6bcebc01e61
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:02Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-144
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://dd3782b912b6431d8c9e5569657458a6d1e5fac17774cc7f77ce399480047b2a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:03Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-145
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://21fb1673d9f1c0161f4519f2828d270e480640755a7e0530404439a1de36d0e2
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-146
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f929adb0069ef73985a2ad57949cf1d087dcca85855f5787a4d1d6e0cb05e085
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-147
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://139ad3b794f659d12148708fab626712776708d10f7f3e7a1af61753f930f94b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-148
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://379e377965417f3e77575cb16ed255b3cc70953bcfea3626c7f901109934fb73
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-149
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://9a6fea2c6affc7e45c2c8b37b3852a9fb3e8f463eeb29af02f8e4cd6b7fd180a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ***********16
      podIPs:
      - ip: ***********16
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-150
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://665a3fbb125e03d092a0cc3d75e21cd7b56264c07fd6ae42dc48b5ff3e3d4c1d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-151
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://67904e0042e35006ed6225def31ba78f5679befff7f9f82271e9afe0a74e0157
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-152
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://866f16d91e8db0fbbc78a792dfbcf06fc1047044d35d4aa6974dc48dc3dd3e39
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********4
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-153
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://4062703502045e89c995838ae20d88ddc8d2f8c6e5efe6e499dcb2f84cbd9d4a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-154
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://08365d49e99c30e9d6884c7be7228edf3e93d83a603bdcaa76e3b32a9918f1ec
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-155
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://bd247378fad5935598197b79cb4711bc1e118311360c5501d8bc593a4eb1d1e9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********0
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-156
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1d1ad2ac1873880643fb49ee10b6da594ba3857ed541312f13b13c518ac09ef8
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********2
      phase: Running
      podIP: ************0
      podIPs:
      - ip: ************0
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-157
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1020c5d1aeb550a7187384823dd4d19798f0c39fc64008f767c77cb3e4952d9a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-158
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://967a8e3ea6a7b23ea8de1bbe832685daecbddf8a27309185e14d4f0bdcb486e7
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-159
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ac5eaedadaedc0ae03d3ef2c2ae3b0d1cd4e2b27eee810e0b3a521a698f904d4
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ***********70
      podIPs:
      - ip: ***********70
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-160
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ca9bcbe0f6c96e8fc30f8d8f6e3fcc1e2ba3281c9bcb7b781ff49b7059b919d2
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********5
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-161
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://bef8a0d987df88f59aca5590f618d3546fce785baa80b12ff9e417f525e8b6bc
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********5
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-162
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://8515d9d39b4cc6268af09cd2a5d81f81a1590fc35b760c60e79c17ce61435dd3
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-163
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:38:59Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:38:59Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:38:59Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://aa0c09ac5e6d298704781df47bcefbf860abdf1297976147cde8c951c4ecfccf
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-164
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://0d02b8cb8270d1e76520c8535c260536416250ab01965ee82e26fdcf931ca312
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ***********58
      podIPs:
      - ip: ***********58
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-165
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ab3d504c46d96285567bdd0571baf142bca634d183d49e71d108d5f2cbf66345
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********7
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-166
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e8e79c119feb241982f1672f6aeb3389a1c77b5736af4e7d15da5582dd8388d8
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-167
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f8a56417257610e480b6f13075e04b65451685b2b1faa4d1a55689b1729154f9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********7
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-168
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:56Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://59a71e1a111ad6164e993a881795e085f0af6870edb596f535c18e6269b7cd38
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:03Z"
      hostIP: **********
      phase: Running
      podIP: ***********99
      podIPs:
      - ip: ***********99
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:56Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-169
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://634963958949dd4106a233d6705f336774da00722346465d1267812df031e284
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-170
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b22c30570f6e665432b26857a97f12e019e0de96428167d612ab4e03b9df7d92
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-171
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://22f0fa3c6010a2be73be7555ad924ba7f03fe9dc8ae0c777cc64d3853836be15
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********7
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-172
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://fb7e7b4cb2ac70a67fc2b32df4ac80c98b8b4e5da12ba5ea057edd74f7839530
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-173
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://655c0a4b070a9cd0176489af8445cc74c190113ca22a00ab5937e0f95831a8ce
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********5
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-174
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f312b3bb885bf80fd6ce59184f426a13d2a9565646fd06e5899c4c00e327455e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:03Z"
      hostIP: *********9
      phase: Running
      podIP: ***********24
      podIPs:
      - ip: ***********24
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-175
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6375e74be52cd67ecbfe2528ddfc022ac370666c676a7ee4e144c07408536b69
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********7
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-176
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d0474d2ce6db2d1c81c6b65629b7f4c7ceb2a2f57d12fd1581fd44ed764605a2
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-177
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1fcebcdb5adcd9d29554cff13bac23ed257cd3b3d04eacb25c4f037f7a187097
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: *********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-178
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://0e50ed90dbd36642c6763198c6b31d9ba41a363ae59c3ac80894bcde972a10ee
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-179
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d91e94fa0728f533a8dd030fe43ec40ef3d5646ee711cebe57f52b37c3b95d48
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:03Z"
      hostIP: *********2
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-180
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://0e6fd921d1b3908546ff913c912a2b2eceff4321b30af2cf867fdd3db5c0da47
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********3
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-181
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d703a68f48608da9a801717f03866f0a9b2a8e392e42e378f6a859b2d7ab0a5a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-182
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://efe024aa041445a06db149fdd948e96e0570ba43e015401b8693f176139bb8fd
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: *********1
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-183
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://8939525478a9ac09318896fd1402512aefee3c668eebb85405351cda31dea412
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-184
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e21cfc52fc3241754a4d1bac2b9c3c71d1de0f5a1712f050373e86689a2ff508
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-185
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://bb4684ae320924c2b65eec0ad4137ec2a65dd09e0631cf772d9c5a72bd4b2a44
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-186
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://dc1037b261c19350c004c068d4183cee4a639c2f812c21bcff073d45d77f89ec
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-187
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f12fc951cdae08247b0fb4fb78ea77ffc43e0c97d3a7d0262e273b126147b9b5
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********1
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-188
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://911b93619e93e5c211ea5d56f15f4da8263bf0f0a7d05aab5005b0d7b076b33b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ***********73
      podIPs:
      - ip: ***********73
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-189
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f18abdb035ebfe8fb586b6602900c7bf5c67753a1cbf2980b90cd04112e294ef
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********4
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-190
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f9c3c12cdb3328f4816d6a9cd99fc1143901f653526bfdc035a7ed792685df23
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ***********13
      podIPs:
      - ip: ***********13
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-191
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5140ba1966a42cd047e73da2d271b986d451b0a1baf706035e306bb012581743
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********4
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-192
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f9e45144977bdfb9436db6d60284ee9f2a4d903d5e5b1be3083f49d9ca371723
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-193
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://2abce9af42867e95470955b4782da552ee0f392f3f7727ed97cc914c79ad3166
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: *********9
      phase: Running
      podIP: ************3
      podIPs:
      - ip: ************3
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-194
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e1aa3987cc79d41af40e310f90c6548b11cb2dba527bec2826184ba7c7471178
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-195
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1e8b7793779c15d55dae527fdd8b7d9bed02818f3bd97fbbbadc191bafa2f9dc
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-196
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:56Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://23c6b1a71fc06af6badad7c003f30163aa8865616b9407ddc427aca4af03a6b2
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:03Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:56Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-197
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6a7edeb6f2aa01f017efa7b9e3b017ff20d31e45693f7aebf0d458c3331e2586
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********3
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-198
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ec796bbdaf1d8600a57805ebf82d89df98bf87bb15eb6379136d5649be6fef0e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-199
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://63d98ea2aca89b80ca458402dcc699b65b8bf3351fa02051eadf1ff8b59a19da
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: ***********
      phase: Running
      podIP: ************8
      podIPs:
      - ip: ************8
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-200
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://4f95f61fc4baf6e5da2a1cd59eaee4bb4ebf29323b19b36266285cf41578986b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-201
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://56a79c8338651b7a5e266896a1de4841cf6ea5f5e5fce05415a85d41c39da355
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********4
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-202
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://0f72592c30e9ed910b5f8c021eeaffd0c62ba3ee6cf70820976f745e5d65b897
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-203
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://95b695e133a292319aadf5ce55b24e5cd0cb839318714fe996b592670117b9c0
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-204
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ab1062a913d9769981344c6a5a51bfe7db9a4e7d8e496b17a768ec717e5483ac
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-205
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:56Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://9ce911439c2f4ce01664637145d804f0449a03f5f7cfcacebef215ee9f304b90
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:04Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:56Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-206
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://dc83fe25d2d01c1393a3d2deace0ab89cbe7a00665e2d88db98ea2588c53868e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-207
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ab0443a5d760cc399c33b6f293c271680e31bc4c02188679776452900e152046
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-208
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://fa37c984a3a98afe36c38fe94d533f8a7f9ee055a9292303d38259e50a42e08f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-209
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:54Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://331b293b8cd71e668199dfcb24fedabd0a1720e981137740502f4a9c9922897f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:54Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-210
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f70092153f6d300660f5dd235221af7e41dbe9e83008ba9ddefe16eb9d8c5956
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********2
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-211
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://929e1afbaa36298af854663ae6731e616089da5e74b3d86049a1ed50043b3e9b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-212
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://dd1aa9999b34bb560e51c1925c8442afaf9a77752e9b8e16b088c5db6e70571d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:04Z"
      hostIP: *********6
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-213
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f0e03b6b8fec592bfe516244b9d8f59be68150828691c2882a29163757622147
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-214
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6da0c66a2f577ce6b502fb1f6f4a92a9c922a5078605b0a3da994948a56c18b5
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-215
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://3291d537a504f4f780adf925ad02691be45bb1b5abbf3348c77c477a60cb8764
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-216
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://66e41596a3fe8e235e6d4c582e94dbbe2848bc512a9d272488a8f2c8ba49697d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-217
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:56Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b20fa6875393be8e971d2420ab439748fe8d65d175ecb7dd486fdf6b54d93bce
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:04Z"
      hostIP: ***********
      phase: Running
      podIP: ***********33
      podIPs:
      - ip: ***********33
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:56Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-218
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:54Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c44d910d2440be3dcd78b7ff7b443d269b5a0cb6a931d3b9d5b80812e0afedbd
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: *********0
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:54Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-219
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://836ab091d089d91265bf093b273e49508393be3dbeb91bf7449ef378d234d2cb
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********5
      phase: Running
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-220
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c70c9b4c2111d9e0aab2c83cfaa91b6bdf2bb350c6e3db20a047971a1f416bea
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********3
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-221
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:57Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://eda5bf6befe96a9d39fe306ec8d12c031416439dd66686d0613a118703a739f9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:04Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:57Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-222
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f4d8ef7719e61c8cf1f6c4171a88edf8c96117c5e30c211e50d12453f7510a92
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********1
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-223
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://bd6d136012f47ae666083d0d4b8c888c06da53297aa37d2abd78c2bc8512fed8
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-224
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://27501553ade2490f54d55095dcbd818a2f1fe75509bb0a464913f60727846f36
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-225
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://01a5352803a84a9a34abc4374a479d8097f1407b867b2627c87ff54efb41bea1
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:03Z"
      hostIP: *********24
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-226
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://86ab98fa2423b430c8a69d0d87a29e28215bf2216d5b4288e2748ea65b8268ac
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: *********6
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-227
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://26c0c0357b4f319be9ae1e6883c94902b61828674e6337a6ddc8fec4edea9a2f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-228
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f21bfa951f5017c693db0650b3dfe6a69e12cee5535e6ad7d2b3e0f322c524f3
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-229
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e5555c331518a570a2c61d15aba39d387998540a6bbf7059814e861ecb5dfb59
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********8
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-230
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://67ed0446b976c9e0f821297b48af900bdf4904bae1955df0a04ecd12e8672190
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********2
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-231
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e8ccfc75e6a8dc0ec1e5dec7079ce354e53b0559f823ddd74e4521f2aa2c2a2f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-232
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c536cae610958d8de43c5891752dfe38695e443af6948223ac01929cc2b9d878
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-233
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://3d12c4f37ac2e37b5e6142ae4f7fd203f88a5ba329d470956998e3738f0d4f78
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-234
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://292154bea54905b904576b1218925b180e49e47930f2c426b1cc108d5881eea7
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-235
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:57Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://bd1f4714b4062437f14af91ee77e3b1ac62cdefb9e12e20bbdecd7d68810683a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:04Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:57Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-236
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://23c78b944c729611d8af1d904d1080ae1f4112d7db9b9d6ea3947ffc7c761410
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********9
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-237
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1ec02d3ed5e0cc8639441ffae197c0bceff74b4b5eff0d5e099a1fadfefc90d4
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********7
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-238
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f25f5e7fe9da26b561af7fa66a9d1a7dce4513022bbabb477e890fbe6a677f96
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-239
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://8e1d28f234c0cec4e44bdfa359fc93f35fe6e1821f0131e2776f9f4f055976f2
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-240
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a233299b1da749bf80c79b0890cfdc0b43ac7a9b7f094cea937b74f7e3e96509
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-241
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a3ef1e919f3dc9640de21e3b8be1a4c4c4ac49de8943aa679ac46374effd9d35
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ***********50
      podIPs:
      - ip: ***********50
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-242
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b0dc57f1edabad12f58a2054f3d3539744a610a3375fd4d6f9768b58f6446275
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-243
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://08763ee105c721477e9adf00ee483b6423fce75e2b7839be4f3f6940b59f68fc
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-244
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e394041d991ef168e867c23410efaa776019f09df8be9d2622670cf7e8464081
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-245
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:56Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://45305ad6630a7f3cc89108e8f1a86ffec74535d5924a9b6e0f87488abada48a6
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:03Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:56Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-246
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1cf4d3e8f3e663e6a84693b6e87ad1c033d70864b966c9dc6127e8c59075119b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ***********93
      podIPs:
      - ip: ***********93
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-247
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7e581335fc6b9c9f7a383bf58a4338043a2e1fc49b69f7ce0a35467378bb683c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-248
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ef81d2f0b65d91005ebf017e25babe38791d27de0c0533964ee0073533fb61ee
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-249
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:55Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://48127608ee4c8b7f1ced9c24167471453da1f1a488b4607c8a3083c9719b124c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:02Z"
      hostIP: **********
      phase: Running
      podIP: ***********44
      podIPs:
      - ip: ***********44
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:55Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-250
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b2509e8e73ff7bd3e50441946ac41eb0913e0aa7eb66ac84540f3999b1d09c2f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-251
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://40621de5664dc0bdf8fc3120bcc08e004c263bac86565f52f05c4f034b81a780
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-252
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a33ba79090f0250b20ce98eac9cd43522bf801bb2b63e65fdf3c38f851507f51
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********7
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-253
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:54Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7cf86722b673e131d7b0fda2d8531747c93e427142b7ab2b2f50c1d6982d75e5
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:54Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-254
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5191880c5ef7f60832bbfc3ddd3373bd6e729dff962d629d1ab846fe5e9e2261
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-255
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://24de40729fc6fe1b49686a2995aac80225d519466657cd83accbd62b73d9b10c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-256
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:57Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a5d842bf497671a145fda0b07976010c5e0060d7908406562167fd38794fab75
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:04Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:57Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-257
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://91a2e24a0ee79defe163378831674cc1b0ada5c4cd4aabafe296e4d82858c7d0
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********9
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-258
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://2226eca26f77d24e2e8a5aabf4a4088c1be900f8ec95d48612718d6f5d43c598
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********5
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-259
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1c8237d50681e4f5d49502fad4d480c46a2d39358b63f4f144b2e3e6c3158aa5
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-260
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://956413ebf685afd2d9606baa3379e03bcd86f07be707d0890ce8140bbecf3412
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-261
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://81423b7077851c5a4a9e66647224d2a03b5d89016ebbe3ee79e9ad678deba84a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-262
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://2aefb5cbf886beefdfed7e63acd9ec9be95cbdc672d780d8da866776f1be4396
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-263
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e890ea100d2475f3c36ce75dbd08476154c24eb237bc97126c515c86e2b8e641
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********5
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-264
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://db98de6a3e22243ebb449a98a87f43d166693ff13dc85bfc41593829323e15a1
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: ***********
      phase: Running
      podIP: ***********56
      podIPs:
      - ip: ***********56
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-265
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://9e1711460a5d4534c664c5907eabef89057573d5b9bdce949b57fe0d4f1a0b82
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********8
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-266
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://99ad7bc1a52ed8c04d41b99bc07fd8370213c6d29a24f9bdbc65473dde603a4d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-267
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7e40187799bddbab352774c10fc0ef0021a494f282b8cffeac4e91392c5107cd
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-268
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://621874c8316e59abcbbb3cad426705f316b687d8ab7407e200f71303ed63b9b8
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********9
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-269
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7fdeeb6e2ac80fd4f1886243360eda4870f9aff6816c234669a7abd0856244a2
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-270
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://db88c039615dfe728f4593cb8b46e1103167112b731a0ffe9bebd02ae154444c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********4
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-271
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://25a688c3dd50290c24be503e0c3f6e12f0d56b7b5e2af8421a3d933dad439ddc
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-272
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b9f74cdc253a0cd87ba50a7cb18042ac2c62719d80c043997d03f3c7c4c9d935
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ***********85
      podIPs:
      - ip: ***********85
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-273
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c2ace69d2a1b02a8ca141e1b81b1cac41cc343ca3ec5923fe72438837b1ef24b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-274
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://566a82d52873be2d846a4f92ab10e935f312a89f3f82bfb984ffbb5528aef6e5
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ***********50
      podIPs:
      - ip: ***********50
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-275
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f0e6c59e43de36cf1054006e6fa079777b3cc8573420a318c6557a37c1b09b59
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-276
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://74e689e58effae7f6db39e70cbc1212dff86183f66009d52ea03c207d1cb2179
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-277
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ab6428874d0032053f27417b111f8b1cc07f35af29f45a3375cf84cf93ce202d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ***********4
      podIPs:
      - ip: ***********4
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-278
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ec61ff42384cfe186b9728164654ffb9108e91f53af56b2dee6ec14c0f2f09a5
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-279
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://0ad7eb0a6975f901886fee41ba80c065fc1109988a17aea7f978054f553f515d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-280
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://129181d0139c09b8921f09b2be766daf7711cefe535d152d6d8cd9488acb891e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-281
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ea959f68036d4c322b368851cc78d4e9f30d41b4470e735a2a7f50ee36c7be09
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-282
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5ffbf682fe7bdd127a44b08b236b3075316f0c06d04881ddc620f29892e26d24
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-283
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ac742f658cb08cd4cc0c5a9fc4bdb0e0f5179e503f9c5a4b8a0e4b030fa579f3
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-284
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://474f07e36de3deb2aed77f033dbaeb8e7f7e3ceeff7fb6268398ae48d019ffb2
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********3
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-285
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://840b9e9652443f69c588bd1ab19d0470b78748338d4b46779b45c23d07766766
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********20
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-286
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://639a0edef56f4792c0ad8b45f722b4b2059412897de165f22b5f50b0007f2766
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********
      phase: Running
      podIP: ***********05
      podIPs:
      - ip: ***********05
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-287
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://cc9ec0fce768adc2263defac10304aa3de45ae9ea38ae88812b5ab705d06b671
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********4
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-288
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://33b1062ddb99dac41fd9934ae3645eeb8e54a4ae06368cabdf39711b6d95ef9e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********26
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-289
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d612ed9161e00be6c7fc97880d6a020509aaaabf8d99975ed1561714d57dd503
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-290
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://272a3348d9b8ef3a7bc58237c929d37d819aebaf9ed16952ccdb6dd85dfe02c8
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-291
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7ac6ed8df59a05464e84b40597709690fd493726b518efe1aab481d4b695bc08
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-292
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5cc1c6a142aebf245583da7eee24585ca2a117faa5cca6470aff12e62ab0bb73
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********2
      phase: Running
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-293
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5b099cb94ad21073aa17406fbd5f1782680add448fac5418ea4651add760b075
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********4
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-294
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5902f4052d570a12559186eba45b672996fe68630fbd69caba3bddf6816403dc
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-295
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5c1ad0bdf3e6bfd0ab24d1454b832be55fa86598c3b81cfb4c47e8075f77f6a4
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-296
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://423a7e93033e9eaf9cb624342d52f2c9e6f7a6025344551914b90619d0522b36
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:02Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-297
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d91a8987043404900758a7a13efb3eff70ed45eaca3dde2915d64ffd64e79394
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********6
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-298
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://2007c8da95741a61eef9beaa96f19e19f783cc64210b1e144bca8b03504e159d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-299
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://98a1468f67fa8ec250852315987d47a0612c5fd9dac69c67dfc94ab83a1fb434
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: **********2
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-300
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://892a8d6b47f08ec2c1d7bcfe420af57d8abb2b0cf8ddf97c9266ee23d2d582c6
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********0
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-301
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7288b721bc701e603adf05f1eca60d47ec585bd49c5e8808bfcc10914949c152
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:02Z"
      hostIP: **********9
      phase: Running
      podIP: ***********09
      podIPs:
      - ip: ***********09
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-302
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e56c55d519972443c0442420f356620fb8959aa6d01aae7b5895c136bb8df44a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********6
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-303
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d0a81667d87b26a7a423a8a3f0e65d46f2dbbd0ac307d2134bc1fa1774dc84e8
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-304
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e29c6fe459bcd757625d53f5e7ec83867e4aac31fb5020bb2ab4b332fa6c55d9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-305
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:54Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:02Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f6776b029353cbd44948239c8aa2c26dd42368b5e1af85d746af5c4a3b355d6f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:02Z"
      hostIP: **********8
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:54Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-306
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7083edbaac32644e0622919e908769a1e631f00fa918a5730f451274d2e7be21
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-307
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:57Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://11e896becec6296783569591188c25af557a312d5e77058ddcfb7d4dc998b8b0
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:04Z"
      hostIP: **********1
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:57Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-308
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://974cc619b92f87088a01ff7b2b349b11a55177980eab7b088e52ddf87526753f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-309
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://690efdc2e6cc64e5f4ecde6e1963281861bd75fa158dcf922bd3856a53526932
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-310
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7d0340f444227ba6996dcf2da75197f1658b27223bef63d85dd0030402f510b6
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********0
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-311
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6578984b1a506c13d2be8d70e3c81477b5ba40b38a44819e0dc107e0972be5cc
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-312
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://49b02ed21fd98108892d4b7b224236427bfcb8392c444ac56244a13a606a7fad
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-313
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://94f6fa7c75b4323488ee52f34b7e55d2b8d6bc92abb8f696e9f2ca5adbd6548d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********1
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-314
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b07c705cb12ae104c9b8368f8e00b48bd07ab7c4397cd2b94c70150245f401d8
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-315
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://04fe87a6b377de09c3bc9a0b9cf865ac1242532d7baf61e325d963b26fb1338f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-316
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://bdcb7826ae16c086da4f96a32030921dbde1fcb4ede89921075933c8a7093820
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: ************9
      podIPs:
      - ip: ************9
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-317
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ef2cb2b5d4a8a8ab2e8bf93b341bcb5636b6d783ade1eeeb54a559cb51077914
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-318
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6a429f159c2b782c0d9b2fe9f4c416261030a236e9b7d7f47df4cfdb8f648beb
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:03Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-319
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b45031de4ee589a49be1fe3e51bf4419a05238cb2f67ad3dd294f7497af90591
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:01Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-320
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://54e8871299950011301946b2728f7989af33f20225c6d3e136b032bfcc08e3cf
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-321
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://8c083f8dd6ed4b28aa25f425be3c50e8204fa10df1c7097c9e91c3be41250533
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-322
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f372f4f96b00434c9d779b4115ed2c5826e2fc14a298921e08a942a794ba95ad
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: **********
      phase: Running
      podIP: ***********84
      podIPs:
      - ip: ***********84
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-323
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://9a1d4a9fe3bc4cc09e311c463ed7c299737cd828be6af024a82090ed262a4c59
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-324
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://26274965c9aefe19269c1631b0676e835ea625a8c4f1fce2a653bdf56a34300f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: *********8
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-325
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://733d513e8c958095ed7b4160d9f7ff0567e4c7288efe6bb906749e05f199324c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-326
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5247382df22fa33bbad90e4e25d771182048407bd930d5bb05628d6df8765b0e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-327
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:01Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://4eb2f4e3acd106a7e5deef3fa4d3cbcc8e71e1b68d469449330d45e6964273c5
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-328
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:55Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:03Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://95c6633989ea18d1a99e55b8903510782af91d0d7df4501333b61b0db143a9cb
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:03Z"
      hostIP: *********4
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:55Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-329
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://dfc6866b1894f0eed2a2110a2eed2d9aa52ce9fa4619f80a169d063e18c2f2db
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-330
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://644ecb964ca5f6d1fd6b7f7049f10f2309be4e10b6618a05730c528968c9de2f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ************3
      podIPs:
      - ip: ************3
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-331
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://2c59840333505cc823f389c82db702d8a6c7d784d32115a8b9e488f9e4ae591c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-332
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://69e8c2d9e040a77f09d3b672fdbb6a168c97809bf7bd572f9abc051172823391
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-333
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://57b9b71e2551f36051673ad4995a1eeef08838bfa784afd966a7cba80568ffc5
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:38:59Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-334
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a0d1a475cbae39d0eb2c154021d21ee2ca5a6120e8d5c34dc97b8c7c307572ac
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-335
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:53Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:04Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://58f22eae038664e614e0d2be5b319b50619ca51b62a069a27aab4293f8b2042f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:03Z"
      hostIP: ***********
      phase: Running
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:53Z"
  - name: job-nfc9y8fjev-baidu-0711-deepep-worker-336
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T06:38:52Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T06:39:00Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T06:38:48Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://da0504d792a8aae73ea92510cfbd0a87af87dfda96447c19093c2222b0e6108d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T06:39:00Z"
      hostIP: **********
      phase: Running
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T06:38:52Z"
  replicaStatuses:
    launcher:
      active: 1
    worker:
      active: 337
  startTime: "2025-07-15T06:38:46Z"
