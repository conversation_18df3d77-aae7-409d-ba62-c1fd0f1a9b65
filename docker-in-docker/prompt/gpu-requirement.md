# docker in docker main 容器支持安全 gpu 使用

## 已有现状

- 1. 存在 docker in docker 的 deployment yaml 如下:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
  name: aibox-yrq-2
  namespace: aihc-pom
spec:
  progressDeadlineSeconds: 9600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: aibox-yrq-2
  strategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      annotations:
        container.apparmor.security.beta.kubernetes.io/main: unconfined
        scheduling.volcano.sh/queue-name: default
      creationTimestamp: null
      labels:
        aihc.baidubce.com/account: 0e068cb945c74bc790b83d05e6d8b7e9
        aihc.baidubce.com/cpu-node-affinity: "true"
        aihc.baidubce.com/userid: 9c301e4cffeb4bb6928b087ec59bed89
        aihc.baidubce.com/username: aibox-yrq-2
        aihc.baidubce.com/workload-type: dev
        aihclite.baidubce.com/account: 0e068cb945c74bc790b83d05e6d8b7e9
        aihclite.baidubce.com/instance: aibox-yrq-2
        aihclite.baidubce.com/user: 9c301e4cffeb4bb6928b087ec59bed89
        aihclite.baidubce.com/vendor: aihclite-aibox
        app: aibox-yrq-2
        component: aihcpom
        pom.aihc.baidubce.com/app-name: aibox-yrq-2
        pom.aihc.baidubce.com/source: aibox
        pom.aihc.baidubce.com/traffic: "false"
        volcano.sh/preemptable: "false"
    spec:
      containers:
      - command:
        - sleep
        - infinity
        env:
        - name: DOCKER_HOST
          value: tcp://localhost:2375
        image: registry.baidubce.com/cce-plugin-dev/ubuntu-docker-cli:28.2.2
        imagePullPolicy: Always
        name: main
        resources:
          limits:
            cpu: "2"
            memory: 8Gi
            nvidia.com/gpu: 2
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /dev/shm
          name: dshm
        - mountPath: /.rootfs
          name: main-pvc
        - mountPath: /etc/aibox
          name: devmac-car
      - name: containerruntime-proxy
        image: registry.baidubce.com/cce-plugin-dev/dind-proxy:v0.1.1
        command:
        - container-runtime-proxy
        - --debug
        - --docker-protocol
        - unix
        - --docker-target
        - /var/run/docker.sock
        - --listen-addr
        - 0.0.0.0:2375
        - --listen-type
        - tcp
        - --rootfs-prefix
        - /.rootfs-main/fs
        securityContext:
          privileged: true
        volumeMounts:
        - mountPath: /var/run
          name: var-run
      - env:
        - name: DOCKER_HOST
          value: tcp://0.0.0.0:2375
        - name: NVIDIA_VISIBLE_DEVICES
          value: all
        - name: DOCKER_TLS_CERTDIR
        image: registry.baidubce.com/cce-plugin-dev/ubuntu-dind:jammy-systemd-latest
        imagePullPolicy: Always
        name: sidecar
        resources:
          limits:
            cpu: "1"
            memory: 4Gi
        securityContext:
          privileged: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /dev/shm
          name: dshm
        - mountPath: /.rootfs
          name: sidecar-pvc
        - mountPath: /.rootfs-main
          name: main-pvc
        - mountPath: /etc/aibox
          name: devmac-car
        - mountPath: /var/run
          name: var-run
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 0
      volumes:
      - emptyDir:
          medium: Memory
          sizeLimit: 8Gi
        name: dshm
      - name: main-pvc
        persistentVolumeClaim:
          claimName: pvc-aihcpom-aibox-main2
      - name: sidecar-pvc
        persistentVolumeClaim:
          claimName: pvc-aihcpom-aibox-sidecar2
      - emptyDir: {}
        name: devmac-car
      - emptyDir: {}
        name: var-run
```

- 2. 执行 nvidia-smi --query-gpu=index,uuid --format=csv,noheader,nounits 可以查看到 gpu 的 uuid，结果类似如下

```
root@aibox-yrq-2-5b494f879f-9fxhd:/# nvidia-smi --query-gpu=index,uuid --format=csv,noheader,nounits
0, GPU-1494f7f9-9e6a-a183-c291-77e45d5a0dd0
1, GPU-11eeb833-e04b-5947-b433-6c958bac9d01
```

## 需求介绍

- 要求 docker in docker 中的 sidecar 容器对 GPU 的使用视图和权限能和 main 容器保持一致
- 不用管 containerruntime-proxy 容器
- 在 sidecar 创建前就能获取到 main 容器的 gpu 信息，并设置到 sidecar 容器的环境变量中