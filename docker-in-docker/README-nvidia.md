# Ubuntu DinD with NVIDIA Container Toolkit

这个项目基于 `registry.baidubce.com/cce-plugin-dev/ubuntu-dind:jammy-systemd-latest` 镜像，添加了 NVIDIA Container Toolkit 支持，使得在 Docker-in-Docker 环境中可以使用 GPU 加速。

## 构建镜像

### 方法 1：使用构建脚本（推荐）
```bash
cd docker-in-docker
./build-nvidia-image.sh
```

### 方法 2：手动构建
```bash
cd docker-in-docker
docker build -f Dockerfile.nvidia -t ubuntu-dind-nvidia:jammy-systemd-latest .
```

## 使用方法

### 1. 运行容器
```bash
# 需要 --gpus all 参数来启用 GPU 支持
# 需要 --privileged 参数来运行 Docker-in-Docker
docker run --gpus all --privileged -d --name dind-nvidia ubuntu-dind-nvidia:jammy-systemd-latest
```

### 2. 进入容器
```bash
docker exec -it dind-nvidia bash
```

### 3. 在容器内验证 GPU 支持
```bash
# 检查 NVIDIA 驱动
nvidia-smi

# 检查 Docker 是否支持 GPU
docker run --gpus all nvidia/cuda:11.0-base nvidia-smi
```

### 4. 在容器内运行支持 GPU 的应用
```bash
# 示例：运行 TensorFlow GPU 容器
docker run --gpus all -it tensorflow/tensorflow:latest-gpu python -c "import tensorflow as tf; print(tf.config.list_physical_devices('GPU'))"

# 示例：运行 PyTorch GPU 容器
docker run --gpus all -it pytorch/pytorch:latest python -c "import torch; print(torch.cuda.is_available())"
```

## 前提条件

1. **主机要求**：
   - 安装了 NVIDIA GPU 驱动程序
   - 安装了 Docker
   - 安装了 NVIDIA Container Toolkit（在主机上）

2. **主机上安装 NVIDIA Container Toolkit**：
   ```bash
   # Ubuntu/Debian
   distribution=$(. /etc/os-release;echo $ID$VERSION_ID) \
         && curl -fsSL https://nvidia.github.io/libnvidia-container/gpgkey | sudo gpg --dearmor -o /usr/share/keyrings/nvidia-container-toolkit-keyring.gpg \
         && curl -s -L https://nvidia.github.io/libnvidia-container/$distribution/libnvidia-container.list | \
               sed 's#deb https://#deb [signed-by=/usr/share/keyrings/nvidia-container-toolkit-keyring.gpg] https://#g' | \
               sudo tee /etc/apt/sources.list.d/nvidia-container-toolkit.list
   
   sudo apt-get update
   sudo apt-get install -y nvidia-container-toolkit
   sudo nvidia-ctk runtime configure --runtime=docker
   sudo systemctl restart docker
   ```

## 故障排除

### 1. GPU 不可用
- 确保主机安装了 NVIDIA 驱动程序：`nvidia-smi`
- 确保主机安装了 NVIDIA Container Toolkit
- 确保使用 `--gpus all` 参数运行容器

### 2. Docker daemon 无法启动
- 确保使用 `--privileged` 参数
- 检查容器日志：`docker logs <container_id>`

### 3. NVIDIA runtime 配置问题
- 在容器内手动运行配置脚本：`/usr/local/bin/configure-nvidia-runtime.sh`
- 重启容器内的 Docker daemon：`systemctl restart docker`

## 文件说明

- `Dockerfile.nvidia`: 构建包含 NVIDIA Container Toolkit 的镜像
- `build-nvidia-image.sh`: 自动化构建脚本
- `tool.md`: NVIDIA Container Toolkit 安装步骤参考
- `README-nvidia.md`: 本使用说明文档

## 注意事项

1. 这个镜像需要在支持 NVIDIA GPU 的主机上运行
2. 容器需要以特权模式运行（`--privileged`）以支持 Docker-in-Docker
3. 需要使用 `--gpus all` 参数来启用 GPU 支持
4. 确保主机已正确安装 NVIDIA 驱动程序和 NVIDIA Container Toolkit
