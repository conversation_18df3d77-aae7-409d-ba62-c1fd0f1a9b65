apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
  name: test
  namespace: aihc-pom
spec:
  progressDeadlineSeconds: 9600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: test
  strategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      annotations:
        container.apparmor.security.beta.kubernetes.io/main: unconfined
        scheduling.volcano.sh/queue-name: default
      creationTimestamp: null
      labels:
        aihc.baidubce.com/account: 0e068cb945c74bc790b83d05e6d8b7e9
        aihc.baidubce.com/cpu-node-affinity: "true"
        aihc.baidubce.com/userid: 9c301e4cffeb4bb6928b087ec59bed89
        aihc.baidubce.com/username: test
        aihc.baidubce.com/workload-type: dev
        aihclite.baidubce.com/account: 0e068cb945c74bc790b83d05e6d8b7e9
        aihclite.baidubce.com/instance: test
        aihclite.baidubce.com/user: 9c301e4cffeb4bb6928b087ec59bed89
        aihclite.baidubce.com/vendor: aihclite-aibox
        app: test
        component: aihcpom
        pom.aihc.baidubce.com/app-name: test
        pom.aihc.baidubce.com/source: aibox
        pom.aihc.baidubce.com/traffic: "false"
        volcano.sh/preemptable: "false"
    spec:
      containers:
      - command:
        - sleep
        - infinity
        env:
        - name: DOCKER_HOST
          value: tcp://localhost:2375
        image: registry.baidubce.com/cce-plugin-dev/ubuntu-docker-cli:28.2.2
        imagePullPolicy: Always
        name: main
        resources:
          limits:
            cpu: "2"
            memory: 4Gi
            nvidia.com/gpu: 2
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /dev/shm
          name: dshm
        - mountPath: /etc/aibox
          name: devmac-car
      - command:
        - sleep
        - infinity
        env:
        - name: DOCKER_HOST
          value: tcp://localhost:2375
        image: registry.baidubce.com/cce-plugin-dev/ubuntu-docker-cli:28.2.2
        imagePullPolicy: Always
        name: sidecar
        resources:
          limits:
            cpu: "2"
            memory: 4Gi
            nvidia.com/gpu: 2
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /dev/shm
          name: dshm
        - mountPath: /etc/aibox
          name: devmac-car
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 0
      volumes:
      - emptyDir:
          medium: Memory
          sizeLimit: 8Gi
        name: dshm
      - emptyDir: {}
        name: devmac-car
