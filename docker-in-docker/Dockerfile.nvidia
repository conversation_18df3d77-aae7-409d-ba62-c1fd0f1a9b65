# 基于现有的 Ubuntu DinD 镜像
FROM registry.baidubce.com/cce-plugin-dev/ubuntu-dind:jammy-systemd-latest

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive

# 更新包列表并安装必要的依赖
RUN apt-get update && apt-get install -y \
    curl \
    gnupg2 \
    ca-certificates \
    lsb-release \
    && rm -rf /var/lib/apt/lists/*

# 设置 NVIDIA Container Toolkit 包仓库和 GPG 密钥
RUN distribution=$(. /etc/os-release;echo $ID$VERSION_ID) \
    && curl -fsSL https://nvidia.github.io/libnvidia-container/gpgkey | gpg --dearmor -o /usr/share/keyrings/nvidia-container-toolkit-keyring.gpg \
    && curl -s -L https://nvidia.github.io/libnvidia-container/$distribution/libnvidia-container.list | \
        sed 's#deb https://#deb [signed-by=/usr/share/keyrings/nvidia-container-toolkit-keyring.gpg] https://#g' | \
        tee /etc/apt/sources.list.d/nvidia-container-toolkit.list

# 安装 nvidia-container-toolkit 包
RUN apt-get update && apt-get install -y nvidia-container-toolkit \
    && rm -rf /var/lib/apt/lists/*

# 配置 Docker daemon 以识别 NVIDIA Container Runtime
# 注意：在容器内部，我们需要确保 Docker daemon 配置正确
RUN nvidia-ctk runtime configure --runtime=docker || true

# 创建一个脚本来在容器启动时配置 NVIDIA runtime
RUN echo '#!/bin/bash\n\
# 确保 Docker daemon 正在运行\n\
if pgrep dockerd > /dev/null; then\n\
    # 配置 NVIDIA Container Runtime\n\
    nvidia-ctk runtime configure --runtime=docker\n\
    # 重启 Docker daemon 以应用配置\n\
    systemctl restart docker || service docker restart\n\
fi\n\
' > /usr/local/bin/configure-nvidia-runtime.sh \
    && chmod +x /usr/local/bin/configure-nvidia-runtime.sh

# 添加标签以标识这是包含 NVIDIA Container Toolkit 的镜像
LABEL description="Ubuntu DinD with NVIDIA Container Toolkit support"
LABEL version="1.0"
LABEL maintainer="grafana-panel-project"

# 保持原有的入口点和命令
# 如果需要在启动时自动配置 NVIDIA runtime，可以创建一个启动脚本
