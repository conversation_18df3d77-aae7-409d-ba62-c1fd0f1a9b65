apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
  name: aibox-yrq-2
  namespace: aihc-pom
spec:
  progressDeadlineSeconds: 9600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: aibox-yrq-2
  strategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      annotations:
        container.apparmor.security.beta.kubernetes.io/main: unconfined
        scheduling.volcano.sh/queue-name: default
      creationTimestamp: null
      labels:
        aihc.baidubce.com/account: 0e068cb945c74bc790b83d05e6d8b7e9
        aihc.baidubce.com/cpu-node-affinity: "true"
        aihc.baidubce.com/userid: 9c301e4cffeb4bb6928b087ec59bed89
        aihc.baidubce.com/username: aibox-yrq-2
        aihc.baidubce.com/workload-type: dev
        aihclite.baidubce.com/account: 0e068cb945c74bc790b83d05e6d8b7e9
        aihclite.baidubce.com/instance: aibox-yrq-2
        aihclite.baidubce.com/user: 9c301e4cffeb4bb6928b087ec59bed89
        aihclite.baidubce.com/vendor: aihclite-aibox
        app: aibox-yrq-2
        component: aihcpom
        pom.aihc.baidubce.com/app-name: aibox-yrq-2
        pom.aihc.baidubce.com/source: aibox
        pom.aihc.baidubce.com/traffic: "false"
        volcano.sh/preemptable: "false"
    spec:
      containers:
      - command:
        - sleep
        - infinity
        env:
        - name: DOCKER_HOST
          value: tcp://localhost:2375
        image: registry.baidubce.com/cce-plugin-dev/ubuntu-docker-cli:28.2.2
        imagePullPolicy: Always
        name: main
        resources:
          limits:
            cpu: "2"
            memory: 8Gi
            nvidia.com/gpu: 2
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /dev/shm
          name: dshm
        - mountPath: /.rootfs
          name: main-pvc
        - mountPath: /etc/aibox
          name: devmac-car
      - name: containerruntime-proxy
        image: registry.baidubce.com/cce-plugin-dev/dind-proxy:v0.1.1
        command:
        - container-runtime-proxy
        - --debug
        - --docker-protocol
        - unix
        - --docker-target
        - /var/run/docker.sock
        - --listen-addr
        - 0.0.0.0:2375
        - --listen-type
        - tcp
        - --rootfs-prefix
        - /.rootfs-main/fs
        securityContext:
          privileged: true
        volumeMounts:
        - mountPath: /var/run
          name: var-run
      - env:
        - name: DOCKER_HOST
          value: tcp://0.0.0.0:2375
        - name: NVIDIA_VISIBLE_DEVICES
          value: all
        - name: DOCKER_TLS_CERTDIR
        image: ccr-registry.baidubce.com/cce-plugin-dev/ubuntu-dind:jammy-systemd-25080701
        imagePullPolicy: Always
        name: sidecar
        resources:
          limits:
            cpu: "1"
            memory: 4Gi
        securityContext:
          privileged: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /dev/shm
          name: dshm
        - mountPath: /.rootfs
          name: sidecar-pvc
        - mountPath: /.rootfs-main
          name: main-pvc
        - mountPath: /etc/aibox
          name: devmac-car
        - mountPath: /var/run
          name: var-run
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: volcano
      securityContext: {}
      terminationGracePeriodSeconds: 0
      volumes:
      - emptyDir:
          medium: Memory
          sizeLimit: 8Gi
        name: dshm
      - name: main-pvc
        persistentVolumeClaim:
          claimName: pvc-aihcpom-aibox-main2
      - name: sidecar-pvc
        persistentVolumeClaim:
          claimName: pvc-aihcpom-aibox-sidecar2
      - emptyDir: {}
        name: devmac-car
      - emptyDir: {}
        name: var-run
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  annotations:
    volume.beta.kubernetes.io/storage-provisioner: csi-cdsplugin
    volume.kubernetes.io/storage-provisioner: csi-cdsplugin
  labels:
    app: aibox-r6bc44e10c70
  name: pvc-aihcpom-aibox-main2
  namespace: aihc-pom
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi
  storageClassName: sc-aihcpom-aibox-r6bc44e10c70-cds
  volumeMode: Filesystem
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  annotations:
    volume.beta.kubernetes.io/storage-provisioner: csi-cdsplugin
    volume.kubernetes.io/storage-provisioner: csi-cdsplugin
  labels:
    app: aibox-r6bc44e10c70
  name: pvc-aihcpom-aibox-sidecar2
  namespace: aihc-pom
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi
  storageClassName: sc-aihcpom-aibox-r6bc44e10c70-cds
  volumeMode: Filesystem