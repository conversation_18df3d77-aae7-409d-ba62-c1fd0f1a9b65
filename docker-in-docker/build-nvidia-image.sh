#!/bin/bash

# 构建包含 NVIDIA Container Toolkit 的 Docker 镜像
# 基于 registry.baidubce.com/cce-plugin-dev/ubuntu-dind:jammy-systemd-latest

set -e

# 设置镜像名称和标签
IMAGE_NAME="registry.baidubce.com/cce-plugin-dev/ubuntu-dind"
IMAGE_TAG="jammy-systemd-1.1"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"

echo "开始构建包含 NVIDIA Container Toolkit 的镜像..."
echo "基础镜像: registry.baidubce.com/cce-plugin-dev/ubuntu-dind:jammy-systemd-latest"
echo "目标镜像: ${FULL_IMAGE_NAME}"

# 构建镜像
docker build -f Dockerfile.nvidia -t "${FULL_IMAGE_NAME}" .

if [ $? -eq 0 ]; then
    echo "✅ 镜像构建成功！"
    echo "镜像名称: ${FULL_IMAGE_NAME}"
    echo ""
    echo "使用方法："
    echo "1. 运行容器（需要 GPU 支持）："
    echo "   docker run --gpus all --privileged -d ${FULL_IMAGE_NAME}"
    echo ""
    echo "2. 在容器内使用 NVIDIA GPU："
    echo "   docker exec -it <container_id> nvidia-smi"
    echo ""
    echo "3. 在容器内运行支持 GPU 的容器："
    echo "   docker run --gpus all nvidia/cuda:11.0-base nvidia-smi"
    echo ""
    echo "注意：需要在支持 NVIDIA GPU 的主机上运行，并且主机已安装 NVIDIA 驱动程序。"
else
    echo "❌ 镜像构建失败！"
    exit 1
fi
