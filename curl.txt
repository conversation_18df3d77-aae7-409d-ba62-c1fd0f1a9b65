curl --location 'http://localhost:9876/cluster/webhook' \
  --data '{
    "accountId": "7bbbd9d0f2c34be587423c8dcc21f1c3",
    "alarmTags": [
      {"key": "accountID", "value": "eca97e148cb74e9683d7b7240829d1ff"},
      {"key": "clusterID", "value": "cce-19zcjce6"},
      {"key": "region", "value": "gztest"},
      {"key": "step", "value": "EnsureK8SServiceRunning"},
      {"key": "masterType", "value": "managedPro"}
    ],
    "alarmValue": "1",
    "alertingRuleId": "",
    "alertingRuleName": "",
    "endTime": 0,
    "eventId": "",
    "eventTags": [
      {"key": "accountID", "value": "eca97e148cb74e9683d7b7240829d1ff"},
      {"key": "alertgroup", "value": "集群状态持续超过10min"},
      {"key": "alertname", "value": "集群状态持续超过10min"},
      {"key": "cce_cluster", "value": "true"},
      {"key": "hybrid_alerting_rule_id", "value": ""},
      {"key": "hybrid_alerting_rule_source", "value": "prom"},
      {"key": "hybrid_alerting_rule_version", "value": "**********"},
      {"key": "hybrid_cloud_account_id", "value": "7bbbd9d0f2c34be587423c8dcc21f1c3"},
      {"key": "cce_cluster", "value": "true"},
      {"key": "hybrid_cloud_notify_rule_id", "value": "3234_1750773920569"},
      {"key": "hybrid_cloud_severity", "value": "critical"},
      {"key": "clusterID", "value": "cce-19zcjce6"},
      {"key": "region", "value": "gztest"},
      {"key": "step", "value": "EnsureK8SServiceRunning"},
      {"key": "masterType", "value": "managedPro"}
    ],
    "severity": "critical",
    "startTime": 0,
    "status": "ABNORMAL",
    "token": "5d8fc58b234890abe852bfd800683eb002548ea2"
  }'

curl --location 'http://localhost:9876/cluster/webhook' \
--header 'Authorization: 5d8fc58b234890abe852bfd800683eb002548ea2' \
--header 'Content-Type: application/json' \
--data '{
    "alerts": [
        {
            "status": "failed",
            "labels": {
                "cce_cluster": "true",
                "clusterID": "cce-19zcjce6",
                "clusterName": "chehejia_test",
                "accountID": "eca97e148cb74e9683d7b7240829d1ff",
                "region": "gztest",
                "step": "EnsureK8SServiceRunning"
            }
        }
    ]
}'

curl -X POST "https://ku.baidu-int.com/fusion/v1/datasheets/dst5R4h8lG6JDVZPGB/records?viewId=viwCP2yK0H8LG&fieldKey=name"  \
  -H "Authorization: Bearer usklQOmS7DuGowHirEakLvB" \
  -H "Content-Type: application/json" \
  --data '{
  "records": [
    {
      "fields": {
        "ClusterID": "cce-19zcjce6",
        "ClusterName": "chehejia_test",
        "Region": "gztest",
        "AccountID": "eca97e148cb74e9683d7b7240829d1ff",
        "CreateTime": "2025-06-13T08:07:53Z",
        "CreateCertificateAuthority": "0s",
        "CreateClusterEIP": "0s",
        "CreateClusterInfrastructure": "32s",
        "CreateClusterLB": "32s",
        "CreateExternalETCD": "0s",
        "DeployK8SPlugin": "0s",
        "Duration": "0s",
        "EnsureK8SServiceRunning": "451h31m35.181524097s",
        "EnsureSystemInited": "0s",
        "FailedMessage": "timeout waiting for Task [ensure masterPro plugin] err: plugin deployment, unexpected ready replicas, expected: 3, got: 0",
        "FailedStep": "EnsureK8SServiceRunning",
        "FinishedTime": "2025-07-02T03:53:17Z",
        "Success": "false",
        "WaitAPIServerAccess": "451h44m52.181524097s",
        "WaitCCEGatewayToken": "0s",
        "WaitMasterInfrastructure": "0s"
      }
    }
  ],
  "fieldKey": "name"
}'


---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-job-watcher-kubeconfig
  namespace: cce-system
  labels:
    app: ai-job-watcher
data:
  kubeconfig: |
    apiVersion: v1
    clusters:
    - cluster:
        certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURvRENDQW9pZ0F3SUJBZ0lVUW1YeHVDM1FkNjlQWnhBcVNUdHhBTllIWk93d0RRWUpLb1pJaHZjTkFRRUwKQlFBd1p6RUxNQWtHQTFVRUJoTUNRMDR4RURBT0JnTlZCQWdUQjBKbGFVcHBibWN4RURBT0JnTlZCQWNUQjBKbAphVXBwYm1jeERqQU1CZ05WQkFvVEJXcHdZV0Z6TVJRd0VnWURWUVFMRXd0amJHOTFaRzVoZEdsMlpURU9NQXdHCkExVUVBeE1GYW5CaFlYTXdJQmNOTWpVd05ERTJNVEV5TXpBd1doZ1BNakV5TlRBek1qTXhNVEl6TURCYU1HY3gKQ3pBSkJnTlZCQVlUQWtOT01SQXdEZ1lEVlFRSUV3ZENaV2xLYVc1bk1SQXdEZ1lEVlFRSEV3ZENaV2xLYVc1bgpNUTR3REFZRFZRUUtFd1ZxY0dGaGN6RVVNQklHQTFVRUN4TUxZMnh2ZFdSdVlYUnBkbVV4RGpBTUJnTlZCQU1UCkJXcHdZV0Z6TUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF1MUZuRXYvcDcvM00KQXlLak95R0xHSmozV1ZWcEcvT1ZCYWo4Q01heUd0STRFa0ZNNkJTbXFkNkRMUXl1WjV4ZFdPL2JOeE4xam1vdApzZDZpdXFMTm1WZFhkM0lLWVVQM0o0a0F0V3F5akREN0pvK1FCN3VpWmVXNmVsQVpFZTByMExWZzBoYlBldU4rCmQ2VFAyVG1PVmwwTHRaVmJ4TW8vbVY1T0hTRno3UDhQYXQ4c0pOSmt4eC8yTTR6ck1wa3JFNndpdWw5cUdUTEEKOU43dC9NSXRhaW94WVZQaVdJZ2Eya2l2c21WcE5PQWNQMWZXajh5eEpzUFE1amhDcVdMbzNqNGVyTzRsVkVKawp1SGJHaEtnTURxWGdPL0paRWhpRlkzdFAxVTA1cmtONWd5SlhRTHNzTEEyV2xROEk2ZWRxcC9RcXJXMGkxUnYvCjVSL0FKNUZ6RlFJREFRQUJvMEl3UURBT0JnTlZIUThCQWY4RUJBTUNBUVl3RHdZRFZSMFRBUUgvQkFVd0F3RUIKL3pBZEJnTlZIUTRFRmdRVURZdHpaREh0cW9SWEVqVHo1VjhWTkZOaDRUd3dEUVlKS29aSWh2Y05BUUVMQlFBRApnZ0VCQUhDTmxvOUNPMVJpVFU5bmtQN3hVYUJjd1RldEZHcG83VTBLM0hXSzRNOEpNMXNOUGFqSU9aZTNtNE5OCjZDT1RxSEJCUjZ1VjY3ekI2RW0vb043bGViMFdySGViQVIxS0E2MHgxcWtscHQ0YjMvK1RuM01jYlZ1dGNJcnAKWkwwVXFJUDlzaHEwQ2NNeGx2MHprWHZ6bWZ1dStIdUNpM1E4SU93am1UNUhma1hCcFQyejdkZkpQWUZWbkp6VQorVmlHcEg3WTMvUUlHSE9LU096NUtUNytuSG5TWm5YR2lQeFdueUlHTDVmZWhGT3FsWDkwS3RIWWgySGZMUjZZClhaT2R6eDU1NnFEbmFobGtOZ1BaUUVPTFJTRVg4WlhnaXF2Y2x2NzZOR1hlMHBOSWFHdjU5T28rZldkVGZuaHgKWVI5WXoxemNqTG43eitjRkRld2FvZ1AvZjNZPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
        server: https://************:78
      name: kubernetes
    contexts:
    - context:
        cluster: kubernetes
        user: f008db4751894afe9b851e32a2068335
      name: f008db4751894afe9b851e32a2068335@kubernetes
    current-context: f008db4751894afe9b851e32a2068335@kubernetes
    kind: Config
    preferences: {}
    users:
    - name: f008db4751894afe9b851e32a2068335
      user:
        client-certificate-data: 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
        client-key-data: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

kubectl --kubeconfig=cce-gl7rksrz.yaml patch deepspeedjob job-nfc9y8fjev \
  --type=merge \
  --subresource=status \
  -p '{
    "status": {
      "conditions": [
        {
          "lastTransitionTime": "2025-07-16T03:53:11Z",
          "message": "job is pending",
          "reason": "JobPending",
          "status": "True",
          "type": "Pending"
        },
        {
          "lastTransitionTime": "2025-07-16T05:53:11Z",
          "message": "job is running",
          "reason": "JobRunning",
          "status": "True",
          "type": "Running"
        }
      ],
      "startTime": "2025-07-15T05:53:11Z",
      "status": "Running"
    }
  }'


nohup ./cce-ai-job-watcher \
  --cluster-id=cce-3jittcud \
  --region=bj \
  --kubeconfig=./cce-3jittcud.kubeconfig \
  --report-url=http://api.nsc.baidu.com/aipod/2.0/platform/jobs \
  --enable-types=pytorch-job,trainer,deepspeed-job \
  > cce-3jittcud.log 2>&1 &

nohup ./cce-ai-job-watcher \
  --cluster-id=cce-b0z4jnzf\
  --region=bj \
  --kubeconfig=./cce-b0z4jnzf.kubeconfig \
  --report-url=http://api.nsc.baidu.com/aipod/2.0/platform/jobs \
  --enable-types=pytorch-job,trainer,deepspeed-job \
  > cce-b0z4jnzf.log 2>&1 &

nohup ./cce-ai-job-watcher \
  --cluster-id=cce-pc7hcerd\
  --region=bj \
  --kubeconfig=./cce-pc7hcerd.kubeconfig \
  --report-url=http://api.nsc.baidu.com/aipod/2.0/platform/jobs \
  --enable-types=pytorch-job,deepspeed-job \
  > cce-b0z4jnzf.log 2>&1 &  