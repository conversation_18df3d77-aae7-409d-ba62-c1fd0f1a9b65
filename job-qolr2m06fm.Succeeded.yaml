apiVersion: trainer.infra.shiyak.com/v1alpha1
kind: Trainer
metadata:
  annotations:
    infra.shiyak.com/job-name: exit0
    infra.shiyak.com/job-owner: zuti.he
    infra.shiyak.com/job-type: pytorch_ft
    infra.shiyak.com/job-user-config: '{"name":"exit0","owner":"zuti.he","type":"pytorch_ft","docker_image":{"repository_id":"repository-4kv4h49ev3","image_tag":"25.02.rc5"},"request":{"CPU":180,"GPU":8,"Memory":2199023255552,"Rdma":8},"workers":1,"selectors":{"infra.shiyak.com/node-type":"H20Z","infra.shiyak.com/project-id":"llm","infra.shiyak.com/resource-role":"training","nvidia.com/gpu.product":"NVIDIA-H20Z"},"command":"exit
      0","fault_tolerance_spec":{"backoff_limit":3,"check_config":{"logCheck":{"hang":{"enable":true,"period":600,"delay":600},"pattern":{"enable":false,"keywords":[]}},"preCheck":{"gpuComputationPrecision":{"enable":true},"gpuFlops":{"enable":true},"rdma":{"enable":false},"nvlink":{"enable":true},"pcie":{"enable":true}}}}}'
    trainer.infra.shiyak.com/driver-master-command: /driver master --period 10s --batchSize
      1000 --heartBeatCheckInterval 30s --heartBeatCheckTimes 10 --heartBeatCheckTimeout
      120s --port 8080 --jobId job-qolr2m06fm --namespace project-llm --upstreamCheckConfig
      "{\"logCheck\":{\"hang\":{\"enable\":true,\"period\":600,\"delay\":600},\"pattern\":{\"enable\":false,\"keywords\":[]}},\"preCheck\":{\"gpuComputationPrecision\":{\"enable\":true},\"gpuFlops\":{\"enable\":true},\"rdma\":{\"enable\":false},\"nvlink\":{\"enable\":true},\"pcie\":{\"enable\":true}}}"
    trainer.infra.shiyak.com/driver-master-image: ghcr.io/shiyak-infra/ft-driver-master:v0.0.2-2-g111d738
    trainer.infra.shiyak.com/driver-worker-command: /workspace/worker --dcgm-mode=0
      --enable-dcgm=false --v=3
    trainer.infra.shiyak.com/driver-worker-image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
    trainer.infra.shiyak.com/precheck-command: python /mlp-bench/main.py
    trainer.infra.shiyak.com/precheck-image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
  creationTimestamp: "2025-06-28T01:22:18Z"
  generation: 1
  labels:
    infra.shiyak.com/job-id: job-qolr2m06fm
    infra.shiyak.com/owner: zuti.he
    infra.shiyak.com/project-id: llm
  name: job-qolr2m06fm
  namespace: project-llm
  resourceVersion: "166213953"
  uid: cba44593-f742-46f3-8867-93759db1f71b
spec:
  policy:
    failurePolicy:
      maxRestarts: 3
    pytorchJobPolicy:
      nprocPerNode: "8"
    runPolicy:
      cleanAfterFinish: true
  replicaSpecs:
    pytorch-master:
      replicas: 1
      template:
        metadata:
          labels:
            infra.shiyak.com/job-id: job-qolr2m06fm
            infra.shiyak.com/owner: zuti.he
            infra.shiyak.com/project-id: llm
        spec:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: infra.shiyak.com/resource-role
                    operator: In
                    values:
                    - training
                  - key: infra.shiyak.com/project-id
                    operator: In
                    values:
                    - llm
                  - key: nvidia.com/gpu.product
                    operator: In
                    values:
                    - NVIDIA-H20Z
                  - key: infra.shiyak.com/node-type
                    operator: In
                    values:
                    - H20Z
                  - key: blacknode.infra.shiyak.com/blacklist
                    operator: NotIn
                    values:
                    - "true"
          containers:
          - command:
            - /bin/bash
            - -c
            - exit 0
            image: sea-hub.shiyak-office.com/nvcr.io-proxy/nvidia/nemo:25.02.rc5
            name: pytorch
            resources:
              limits:
                cpu: "171"
                memory: 1992294Mi
                nvidia.com/gpu: "8"
              requests:
                cpu: "153"
                memory: 1782579Mi
                nvidia.com/gpu: "8"
            volumeMounts:
            - mountPath: /dev/shm
              name: dshm
          restartPolicy: Never
          volumes:
          - emptyDir:
              medium: Memory
              sizeLimit: 1Ti
            name: dshm
  type: PyTorchJob
status:
  completeTime: "2025-06-28T01:24:57Z"
  conditions:
  - lastTransitionTime: "2025-06-28T01:22:18Z"
    message: job is pending
    reason: JobPending
    status: "True"
    type: Pending
  - lastTransitionTime: "2025-06-28T01:24:52Z"
    message: job is running
    reason: JobRunning
    status: "True"
    type: Running
  - lastTransitionTime: "2025-06-28T01:24:57Z"
    message: job succeeded
    reason: JobSucceeded
    status: "True"
    type: Succeeded
  replicaStatuses:
    pytorch-master:
      podStatuses:
      - name: job-qolr2m06fm-master-0
        status:
          conditions:
          - lastTransitionTime: "2025-06-28T01:22:42Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-06-28T01:24:47Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-06-28T01:24:57Z"
            message: 'containers with unready status: [pytorch]'
            reason: ContainersNotReady
            status: "False"
            type: Ready
          - lastTransitionTime: "2025-06-28T01:24:57Z"
            message: 'containers with unready status: [pytorch]'
            reason: ContainersNotReady
            status: "False"
            type: ContainersReady
          - lastTransitionTime: "2025-06-28T01:22:20Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://f96a5030240b2f4b9fe9f98aa28caeee894c4c1f554647d906556e480cfcc29e
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: false
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://f96a5030240b2f4b9fe9f98aa28caeee894c4c1f554647d906556e480cfcc29e
                exitCode: 0
                finishedAt: "2025-06-28T01:24:56Z"
                reason: Completed
                startedAt: "2025-06-28T01:24:51Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://6b72e2771176ce474c881cd1462f0c594b8be0f793527a71cb2235b9d70c96a0
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-06-28T01:22:41Z"
          - containerID: containerd://034fc802949f1c3431ab24b711ec33488e3456ce6ee1fdf5454ed01ba6824c75
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://034fc802949f1c3431ab24b711ec33488e3456ce6ee1fdf5454ed01ba6824c75
                exitCode: 0
                finishedAt: "2025-06-28T01:24:46Z"
                reason: Completed
                startedAt: "2025-06-28T01:22:43Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-06-28T01:22:23Z"
  restarts: 0
  startTime: "2025-06-28T01:24:52Z"
  status: Succeeded
