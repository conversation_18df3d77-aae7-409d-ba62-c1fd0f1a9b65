apiVersion: trainer.infra.shiyak.com/v1alpha1
kind: Trainer
metadata:
  annotations:
    infra.shiyak.com/job-name: pretrain_moe_1b_8b_baseline_ax2
    infra.shiyak.com/job-owner: llm-user
    infra.shiyak.com/job-type: pytorch_ft
    infra.shiyak.com/job-user-config: '{"name":"pretrain_moe_1b_8b_baseline_ax2","owner":"llm-user","type":"pytorch_ft","docker_image":{"repository_id":"repository-4kv4h49ev3","image_tag":"25.02.rc5"},"request":{"CPU":180,"GPU":8,"Memory":2199023255552,"Rdma":8},"workers":128,"selectors":{"infra.shiyak.com/node-type":"H20Z","infra.shiyak.com/project-id":"llm","infra.shiyak.com/resource-role":"training","nvidia.com/gpu.product":"NVIDIA-H20Z"},"command":"#!/bin/bash\nWORK_DIR=$(pwd
      -P)\nif [[ \"$HOSTNAME\" == *\"notebook\"* ]]; then\n    pkill -9 -f train\n    pkill
      -9 -f wandb\nfi\n\n################ environment ################\nexport HYDRA_FULL_ERROR=1\nexport
      CUDA_DEVICE_MAX_CONNECTIONS=1\nexport CUDNN_LOGERR_DBG=1 \nexport CUDNN_LOGDEST_DBG=stderr\nexport
      enable_profiling_tool=0\nexport ANC_DISABLE_LOGGING=1\nexport ANC_DELAY_LOSS_REDUCE=1\nexport
      ANC_TRAIN_LOADER_WORKER=1\nexport ENABLE_MOE_LOG=1\nexport ANC_SET_VAL_REPEAT=1\nexport
      MEGATRON_LOG_BUCKETS=0\n\n\n################ config ################\nREBUILD=true\nPROJECT_NAME=pretrain_moe_1b_8b_ax2\nCKPT_DIR=/mnt/project/llm/output/wangya/pretraining_model_research\n\nREPO_BRANCH_PAIRS=(\n    \"**************:a7n-global/Megatron-LM.git\"
      \"wangya/deepseek_main_0603\" \"e203994b\"    # \"latest\" for the latest commit\n    \"**************:a7n-global/NeMo.git\"
      \"wangya/deepspeek_debug\" \"78874ce38\"\n    \"**************:a7n-global/Ocean.git\"
      \"wangya/main\" \"2e76598\"\n)\n\nBASE_YAML_CONFIG=(\n    --config_file ./Ocean/scripts/yaml_configs/moe_1b_8b.yaml\n)\n\nMODEL_ARGS=(\n    --num_attention_heads
      32\n    --num_query_groups 16\n    --moe_ffn_hidden_size 896\n    --moe_shared_expert_intermediate_size
      1792\n    --ffn_hidden_size 896\n    --num_moe_experts 72\n)\n\nPARALLEL_ARG=(\n)\n\nTOKENIZER_ARGS=(\n)\n\nDATA_ARGS=(\n)\n\nTRAINING_ARGS=(\n    --global_batch_size
      4096\n)\n\nVALIDATION_ARGS=(\n    --validation_interval 1000\n)\n\nLOGGING_ARGS=(\n    --project_name
      $PROJECT_NAME\n    --model_ckpt_dir $CKPT_DIR\n    --wandb_project $(basename
      $CKPT_DIR)\n)\n\n\n################ pip install \u0026 ssh-key ################\nif
      [ \"$REBUILD\" = true ]; then\n    #pip\n    pip install protobuf==3.20.*\n    python3
      -m pip install leptonai\n    python3 -m pip install toml\n    python3 -m pip
      install --upgrade multi-storage-client==0.21.0\n    python3 -m pip install anc==0.4.0\n    #
      ssh\n    mkdir -p ~/.ssh/\n    cp /mnt/project/llm/users/wangya/public_ssh/id_rsa
      ~/.ssh/id_rsa\n    cp /mnt/project/llm/users/wangya/public_ssh/id_rsa.pub ~/.ssh/id_rsa.pub\nfi\n\nfor
      (( i=0; i\u003c${#REPO_BRANCH_PAIRS[@]}; i+=3 )); do\n    REPO_URL=\"${REPO_BRANCH_PAIRS[i]}\"\n    BRANCH_NAME=\"${REPO_BRANCH_PAIRS[i+1]}\"\n    COMMIT_NAME=\"${REPO_BRANCH_PAIRS[i+2]}\"\n    REPO_NAME=$(basename
      \"$REPO_URL\" .git)\n    NORMALIZED_NEW_PATH=\"${WORK_DIR}/${REPO_NAME}\"\n\n    if
      [ \"$REBUILD\" = true ]; then\n        echo -e \"\\ndownloading: ${REPO_URL}\"\n        echo
      \"  ↳ to: ${REPO_NAME}\"\n        echo \"  ↳ branch: ${BRANCH_NAME}\"\n\n        rm
      -rf \"$NORMALIZED_NEW_PATH\"\n\n        if git clone --branch \"$BRANCH_NAME\"
      --single-branch \"$REPO_URL\" \"$NORMALIZED_NEW_PATH\"; then\n\n            LATEST_COMMIT=$(git
      rev-parse HEAD)\n            echo \"✅ clone succeed: ${REPO_NAME}\"\n\n            if
      [ \"$COMMIT_NAME\" != \"latest\" ]; then\n                cd \"$NORMALIZED_NEW_PATH\"
      || exit\n                echo \"  ↳ resetting to commit: ${COMMIT_NAME}\"\n                if
      git reset --hard \"$COMMIT_NAME\"; then\n                    echo \"✅ reset
      succeed: ${REPO_NAME} @ ${COMMIT_NAME}\"\n                else\n                    echo
      \"❌ reset failed: commit ${COMMIT_NAME} may not exist in branch ${BRANCH_NAME}\"\n                fi\n                cd
      \"$WORK_DIR\"\n            else\n                echo \"  ↳ at latest commit:
      ${LATEST_COMMIT}\"\n            fi\n\n        else\n            echo \"❌ clone
      error ${REPO_NAME}, please check whether repo_name or branch_name is wrong!\"\n        fi\n    fi\n\n    #
      PYTHONPATH handling\n    if [[ \":${PYTHONPATH}:\" != *\"${NORMALIZED_NEW_PATH}:\"*
      ]]; then\n        export PYTHONPATH=\"${NORMALIZED_NEW_PATH}\":$PYTHONPATH\n        echo
      \"  ↳ Added to PYTHONPATH: ''${NORMALIZED_NEW_PATH}''\"\n    else\n        echo
      \"  ↳ Already in PYTHONPATH: ''${NORMALIZED_NEW_PATH}'' (skipped)\"\n    fi\ndone\n\n\n##############
      training env ################\nCOMMA_COUNT=$(echo \"$NVIDIA_VISIBLE_DEVICES\"
      | grep -o '','' | wc -l)\nexport PET_NPROC_PER_NODE=${PET_NPROC_PER_NODE:-$((COMMA_COUNT
      + 1))}\nexport PET_NNODES=${PET_NNODES:-1}\nexport PET_NODE_RANK=${PET_NODE_RANK:-0}\nexport
      PET_MASTER_ADDR=${PET_MASTER_ADDR:-$HOSTNAME}\nexport PET_MASTER_PORT=${PET_MASTER_PORT:-23456}\nexport
      ANC_DATA_IDX_DUMP_DIR=${CKPT_DIR}/${PROJECT_NAME}/dumpped_data\n\n\n################
      run training ################\ntorchrun --nproc_per_node=$PET_NPROC_PER_NODE
      \\\n    --nnodes=$PET_NNODES \\\n    --node_rank=$PET_NODE_RANK \\\n    --master_addr=$PET_MASTER_ADDR
      \\\n    --master_port=$PET_MASTER_PORT \\\n    ${WORK_DIR}/Ocean/scripts/nemo_2_scripts/launch_pretrain_model_training.py
      \\\n    --num_node $PET_NNODES \\\n    --num_gpus_per_node $PET_NPROC_PER_NODE
      \\\n    ${BASE_YAML_CONFIG[@]} \\\n    ${MODEL_ARGS[@]} \\\n    ${PARALLEL_ARG[@]}
      \\\n    ${TOKENIZER_ARGS[@]} \\\n    ${DATA_ARGS[@]} \\\n    ${TRAINING_ARGS[@]}
      \\\n    ${VALIDATION_ARGS[@]} \\\n    ${LOGGING_ARGS[@]}\n","envs":{"NETWORK_PREFIX":"http://poc1-mlp.shiyak-office.com","WANDB_API_KEY":"****************************************"},"fault_tolerance_spec":{"backoff_limit":3,"check_config":{"logCheck":{"hang":{"enable":true,"period":1800,"delay":600},"pattern":{"enable":false,"keywords":[]}},"preCheck":{"gpuComputationPrecision":{"enable":true},"gpuFlops":{"enable":true},"rdma":{"enable":false},"nvlink":{"enable":true},"pcie":{"enable":true}}}}}'
    trainer.infra.shiyak.com/driver-master-command: /driver master --period 10s --batchSize
      5000 --heartBeatCheckInterval 30s --heartBeatCheckTimes 10 --heartBeatCheckTimeout
      120s --port 8080 --jobId job-ku4uzh4unm --namespace project-llm --upstreamCheckConfig
      "{\"logCheck\":{\"hang\":{\"enable\":true,\"period\":1800,\"delay\":600},\"pattern\":{\"enable\":false,\"keywords\":[]}},\"preCheck\":{\"gpuComputationPrecision\":{\"enable\":true},\"gpuFlops\":{\"enable\":true},\"rdma\":{\"enable\":false},\"nvlink\":{\"enable\":true},\"pcie\":{\"enable\":true}}}"
    trainer.infra.shiyak.com/driver-master-image: ghcr.io/shiyak-infra/ft-driver-master:v0.0.2-8-gf767e25
    trainer.infra.shiyak.com/driver-worker-command: /workspace/worker --dcgm-mode=0
      --enable-dcgm=false --v=3
    trainer.infra.shiyak.com/driver-worker-image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
    trainer.infra.shiyak.com/precheck-command: python /mlp-bench/main.py
    trainer.infra.shiyak.com/precheck-image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
  creationTimestamp: "2025-07-03T12:57:39Z"
  generation: 4
  labels:
    infra.shiyak.com/job-id: job-ku4uzh4unm
    infra.shiyak.com/owner: llm-user
    infra.shiyak.com/project-id: llm
  name: job-ku4uzh4unm
  namespace: project-llm
  resourceVersion: "229842867"
  uid: 8e407467-7db4-4ffb-a5c2-48e1ec14dd4d
spec:
  policy:
    failurePolicy:
      maxRestarts: 3
    pytorchJobPolicy:
      nprocPerNode: "8"
    runPolicy:
      cleanAfterFinish: true
      suspend: true
  replicaSpecs:
    pytorch-master:
      replicas: 1
      template:
        metadata:
          annotations:
            k8s.v1.cni.cncf.io/networks: network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca
          labels:
            infra.shiyak.com/job-id: job-ku4uzh4unm
            infra.shiyak.com/owner: llm-user
            infra.shiyak.com/project-id: llm
        spec:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: infra.shiyak.com/resource-role
                    operator: In
                    values:
                    - training
                  - key: infra.shiyak.com/project-id
                    operator: In
                    values:
                    - llm
                  - key: nvidia.com/gpu.product
                    operator: In
                    values:
                    - NVIDIA-H20Z
                  - key: infra.shiyak.com/node-type
                    operator: In
                    values:
                    - H20Z
                  - key: blacknode.infra.shiyak.com/blacklist
                    operator: NotIn
                    values:
                    - "true"
          containers:
          - command:
            - /bin/bash
            - -c
            - "#!/bin/bash\nWORK_DIR=$(pwd -P)\nif [[ \"$HOSTNAME\" == *\"notebook\"*
              ]]; then\n    pkill -9 -f train\n    pkill -9 -f wandb\nfi\n\n################
              environment ################\nexport HYDRA_FULL_ERROR=1\nexport CUDA_DEVICE_MAX_CONNECTIONS=1\nexport
              CUDNN_LOGERR_DBG=1 \nexport CUDNN_LOGDEST_DBG=stderr\nexport enable_profiling_tool=0\nexport
              ANC_DISABLE_LOGGING=1\nexport ANC_DELAY_LOSS_REDUCE=1\nexport ANC_TRAIN_LOADER_WORKER=1\nexport
              ENABLE_MOE_LOG=1\nexport ANC_SET_VAL_REPEAT=1\nexport MEGATRON_LOG_BUCKETS=0\n\n\n################
              config ################\nREBUILD=true\nPROJECT_NAME=pretrain_moe_1b_8b_ax2\nCKPT_DIR=/mnt/project/llm/output/wangya/pretraining_model_research\n\nREPO_BRANCH_PAIRS=(\n
              \   \"**************:a7n-global/Megatron-LM.git\" \"wangya/deepseek_main_0603\"
              \"e203994b\"    # \"latest\" for the latest commit\n    \"**************:a7n-global/NeMo.git\"
              \"wangya/deepspeek_debug\" \"78874ce38\"\n    \"**************:a7n-global/Ocean.git\"
              \"wangya/main\" \"2e76598\"\n)\n\nBASE_YAML_CONFIG=(\n    --config_file
              ./Ocean/scripts/yaml_configs/moe_1b_8b.yaml\n)\n\nMODEL_ARGS=(\n    --num_attention_heads
              32\n    --num_query_groups 16\n    --moe_ffn_hidden_size 896\n    --moe_shared_expert_intermediate_size
              1792\n    --ffn_hidden_size 896\n    --num_moe_experts 72\n)\n\nPARALLEL_ARG=(\n)\n\nTOKENIZER_ARGS=(\n)\n\nDATA_ARGS=(\n)\n\nTRAINING_ARGS=(\n
              \   --global_batch_size 4096\n)\n\nVALIDATION_ARGS=(\n    --validation_interval
              1000\n)\n\nLOGGING_ARGS=(\n    --project_name $PROJECT_NAME\n    --model_ckpt_dir
              $CKPT_DIR\n    --wandb_project $(basename $CKPT_DIR)\n)\n\n\n################
              pip install & ssh-key ################\nif [ \"$REBUILD\" = true ];
              then\n    #pip\n    pip install protobuf==3.20.*\n    python3 -m pip
              install leptonai\n    python3 -m pip install toml\n    python3 -m pip
              install --upgrade multi-storage-client==0.21.0\n    python3 -m pip install
              anc==0.4.0\n    # ssh\n    mkdir -p ~/.ssh/\n    cp /mnt/project/llm/users/wangya/public_ssh/id_rsa
              ~/.ssh/id_rsa\n    cp /mnt/project/llm/users/wangya/public_ssh/id_rsa.pub
              ~/.ssh/id_rsa.pub\nfi\n\nfor (( i=0; i<${#REPO_BRANCH_PAIRS[@]}; i+=3
              )); do\n    REPO_URL=\"${REPO_BRANCH_PAIRS[i]}\"\n    BRANCH_NAME=\"${REPO_BRANCH_PAIRS[i+1]}\"\n
              \   COMMIT_NAME=\"${REPO_BRANCH_PAIRS[i+2]}\"\n    REPO_NAME=$(basename
              \"$REPO_URL\" .git)\n    NORMALIZED_NEW_PATH=\"${WORK_DIR}/${REPO_NAME}\"\n\n
              \   if [ \"$REBUILD\" = true ]; then\n        echo -e \"\\ndownloading:
              ${REPO_URL}\"\n        echo \"  ↳ to: ${REPO_NAME}\"\n        echo \"
              \ ↳ branch: ${BRANCH_NAME}\"\n\n        rm -rf \"$NORMALIZED_NEW_PATH\"\n\n
              \       if git clone --branch \"$BRANCH_NAME\" --single-branch \"$REPO_URL\"
              \"$NORMALIZED_NEW_PATH\"; then\n\n            LATEST_COMMIT=$(git rev-parse
              HEAD)\n            echo \"✅ clone succeed: ${REPO_NAME}\"\n\n            if
              [ \"$COMMIT_NAME\" != \"latest\" ]; then\n                cd \"$NORMALIZED_NEW_PATH\"
              || exit\n                echo \"  ↳ resetting to commit: ${COMMIT_NAME}\"\n
              \               if git reset --hard \"$COMMIT_NAME\"; then\n                    echo
              \"✅ reset succeed: ${REPO_NAME} @ ${COMMIT_NAME}\"\n                else\n
              \                   echo \"❌ reset failed: commit ${COMMIT_NAME} may
              not exist in branch ${BRANCH_NAME}\"\n                fi\n                cd
              \"$WORK_DIR\"\n            else\n                echo \"  ↳ at latest
              commit: ${LATEST_COMMIT}\"\n            fi\n\n        else\n            echo
              \"❌ clone error ${REPO_NAME}, please check whether repo_name or branch_name
              is wrong!\"\n        fi\n    fi\n\n    # PYTHONPATH handling\n    if
              [[ \":${PYTHONPATH}:\" != *\"${NORMALIZED_NEW_PATH}:\"* ]]; then\n        export
              PYTHONPATH=\"${NORMALIZED_NEW_PATH}\":$PYTHONPATH\n        echo \"  ↳
              Added to PYTHONPATH: '${NORMALIZED_NEW_PATH}'\"\n    else\n        echo
              \"  ↳ Already in PYTHONPATH: '${NORMALIZED_NEW_PATH}' (skipped)\"\n
              \   fi\ndone\n\n\n############## training env ################\nCOMMA_COUNT=$(echo
              \"$NVIDIA_VISIBLE_DEVICES\" | grep -o ',' | wc -l)\nexport PET_NPROC_PER_NODE=${PET_NPROC_PER_NODE:-$((COMMA_COUNT
              + 1))}\nexport PET_NNODES=${PET_NNODES:-1}\nexport PET_NODE_RANK=${PET_NODE_RANK:-0}\nexport
              PET_MASTER_ADDR=${PET_MASTER_ADDR:-$HOSTNAME}\nexport PET_MASTER_PORT=${PET_MASTER_PORT:-23456}\nexport
              ANC_DATA_IDX_DUMP_DIR=${CKPT_DIR}/${PROJECT_NAME}/dumpped_data\n\n\n################
              run training ################\ntorchrun --nproc_per_node=$PET_NPROC_PER_NODE
              \\\n    --nnodes=$PET_NNODES \\\n    --node_rank=$PET_NODE_RANK \\\n
              \   --master_addr=$PET_MASTER_ADDR \\\n    --master_port=$PET_MASTER_PORT
              \\\n    ${WORK_DIR}/Ocean/scripts/nemo_2_scripts/launch_pretrain_model_training.py
              \\\n    --num_node $PET_NNODES \\\n    --num_gpus_per_node $PET_NPROC_PER_NODE
              \\\n    ${BASE_YAML_CONFIG[@]} \\\n    ${MODEL_ARGS[@]} \\\n    ${PARALLEL_ARG[@]}
              \\\n    ${TOKENIZER_ARGS[@]} \\\n    ${DATA_ARGS[@]} \\\n    ${TRAINING_ARGS[@]}
              \\\n    ${VALIDATION_ARGS[@]} \\\n    ${LOGGING_ARGS[@]}\n"
            env:
            - name: NETWORK_PREFIX
              value: http://poc1-mlp.shiyak-office.com
            - name: WANDB_API_KEY
              value: ****************************************
            - name: MLP_CLUSTER
              value: poc1
            - name: MLP_PROJECT
              value: llm
            - name: MLP_USER
              value: llm-user
            - name: MLP_URL
              value: http://poc1-mlp.shiyak-office.com/console/dlc_job/job-ku4uzh4unm
            - name: MLP_ID
              value: job-ku4uzh4unm
            - name: MLP_NAME
              value: pretrain_moe_1b_8b_baseline_ax2
            - name: MLP_TYPE
              value: job
            - name: MLP_WORKERS
              value: "128"
            - name: MLP_GPUS
              value: "1024"
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: HOST_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            image: sea-hub.shiyak-office.com/nvcr.io-proxy/nvidia/nemo:25.02.rc5
            name: pytorch
            resources:
              limits:
                cpu: "171"
                memory: 1992294Mi
                nvidia.com/gpu: "8"
                rdma/hca: "8"
              requests:
                cpu: "153"
                memory: 1782579Mi
                nvidia.com/gpu: "8"
                rdma/hca: "8"
            securityContext:
              capabilities:
                add:
                - IPC_LOCK
                - SYS_NICE
            volumeMounts:
            - mountPath: /dev/shm
              name: dshm
          restartPolicy: Never
          volumes:
          - emptyDir:
              medium: Memory
              sizeLimit: 1Ti
            name: dshm
    pytorch-worker:
      replicas: 127
      template:
        metadata:
          annotations:
            k8s.v1.cni.cncf.io/networks: network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca
          labels:
            infra.shiyak.com/job-id: job-ku4uzh4unm
            infra.shiyak.com/owner: llm-user
            infra.shiyak.com/project-id: llm
        spec:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: nvidia.com/gpu.product
                    operator: In
                    values:
                    - NVIDIA-H20Z
                  - key: infra.shiyak.com/node-type
                    operator: In
                    values:
                    - H20Z
                  - key: infra.shiyak.com/resource-role
                    operator: In
                    values:
                    - training
                  - key: infra.shiyak.com/project-id
                    operator: In
                    values:
                    - llm
                  - key: blacknode.infra.shiyak.com/blacklist
                    operator: NotIn
                    values:
                    - "true"
          containers:
          - command:
            - /bin/bash
            - -c
            - "#!/bin/bash\nWORK_DIR=$(pwd -P)\nif [[ \"$HOSTNAME\" == *\"notebook\"*
              ]]; then\n    pkill -9 -f train\n    pkill -9 -f wandb\nfi\n\n################
              environment ################\nexport HYDRA_FULL_ERROR=1\nexport CUDA_DEVICE_MAX_CONNECTIONS=1\nexport
              CUDNN_LOGERR_DBG=1 \nexport CUDNN_LOGDEST_DBG=stderr\nexport enable_profiling_tool=0\nexport
              ANC_DISABLE_LOGGING=1\nexport ANC_DELAY_LOSS_REDUCE=1\nexport ANC_TRAIN_LOADER_WORKER=1\nexport
              ENABLE_MOE_LOG=1\nexport ANC_SET_VAL_REPEAT=1\nexport MEGATRON_LOG_BUCKETS=0\n\n\n################
              config ################\nREBUILD=true\nPROJECT_NAME=pretrain_moe_1b_8b_ax2\nCKPT_DIR=/mnt/project/llm/output/wangya/pretraining_model_research\n\nREPO_BRANCH_PAIRS=(\n
              \   \"**************:a7n-global/Megatron-LM.git\" \"wangya/deepseek_main_0603\"
              \"e203994b\"    # \"latest\" for the latest commit\n    \"**************:a7n-global/NeMo.git\"
              \"wangya/deepspeek_debug\" \"78874ce38\"\n    \"**************:a7n-global/Ocean.git\"
              \"wangya/main\" \"2e76598\"\n)\n\nBASE_YAML_CONFIG=(\n    --config_file
              ./Ocean/scripts/yaml_configs/moe_1b_8b.yaml\n)\n\nMODEL_ARGS=(\n    --num_attention_heads
              32\n    --num_query_groups 16\n    --moe_ffn_hidden_size 896\n    --moe_shared_expert_intermediate_size
              1792\n    --ffn_hidden_size 896\n    --num_moe_experts 72\n)\n\nPARALLEL_ARG=(\n)\n\nTOKENIZER_ARGS=(\n)\n\nDATA_ARGS=(\n)\n\nTRAINING_ARGS=(\n
              \   --global_batch_size 4096\n)\n\nVALIDATION_ARGS=(\n    --validation_interval
              1000\n)\n\nLOGGING_ARGS=(\n    --project_name $PROJECT_NAME\n    --model_ckpt_dir
              $CKPT_DIR\n    --wandb_project $(basename $CKPT_DIR)\n)\n\n\n################
              pip install & ssh-key ################\nif [ \"$REBUILD\" = true ];
              then\n    #pip\n    pip install protobuf==3.20.*\n    python3 -m pip
              install leptonai\n    python3 -m pip install toml\n    python3 -m pip
              install --upgrade multi-storage-client==0.21.0\n    python3 -m pip install
              anc==0.4.0\n    # ssh\n    mkdir -p ~/.ssh/\n    cp /mnt/project/llm/users/wangya/public_ssh/id_rsa
              ~/.ssh/id_rsa\n    cp /mnt/project/llm/users/wangya/public_ssh/id_rsa.pub
              ~/.ssh/id_rsa.pub\nfi\n\nfor (( i=0; i<${#REPO_BRANCH_PAIRS[@]}; i+=3
              )); do\n    REPO_URL=\"${REPO_BRANCH_PAIRS[i]}\"\n    BRANCH_NAME=\"${REPO_BRANCH_PAIRS[i+1]}\"\n
              \   COMMIT_NAME=\"${REPO_BRANCH_PAIRS[i+2]}\"\n    REPO_NAME=$(basename
              \"$REPO_URL\" .git)\n    NORMALIZED_NEW_PATH=\"${WORK_DIR}/${REPO_NAME}\"\n\n
              \   if [ \"$REBUILD\" = true ]; then\n        echo -e \"\\ndownloading:
              ${REPO_URL}\"\n        echo \"  ↳ to: ${REPO_NAME}\"\n        echo \"
              \ ↳ branch: ${BRANCH_NAME}\"\n\n        rm -rf \"$NORMALIZED_NEW_PATH\"\n\n
              \       if git clone --branch \"$BRANCH_NAME\" --single-branch \"$REPO_URL\"
              \"$NORMALIZED_NEW_PATH\"; then\n\n            LATEST_COMMIT=$(git rev-parse
              HEAD)\n            echo \"✅ clone succeed: ${REPO_NAME}\"\n\n            if
              [ \"$COMMIT_NAME\" != \"latest\" ]; then\n                cd \"$NORMALIZED_NEW_PATH\"
              || exit\n                echo \"  ↳ resetting to commit: ${COMMIT_NAME}\"\n
              \               if git reset --hard \"$COMMIT_NAME\"; then\n                    echo
              \"✅ reset succeed: ${REPO_NAME} @ ${COMMIT_NAME}\"\n                else\n
              \                   echo \"❌ reset failed: commit ${COMMIT_NAME} may
              not exist in branch ${BRANCH_NAME}\"\n                fi\n                cd
              \"$WORK_DIR\"\n            else\n                echo \"  ↳ at latest
              commit: ${LATEST_COMMIT}\"\n            fi\n\n        else\n            echo
              \"❌ clone error ${REPO_NAME}, please check whether repo_name or branch_name
              is wrong!\"\n        fi\n    fi\n\n    # PYTHONPATH handling\n    if
              [[ \":${PYTHONPATH}:\" != *\"${NORMALIZED_NEW_PATH}:\"* ]]; then\n        export
              PYTHONPATH=\"${NORMALIZED_NEW_PATH}\":$PYTHONPATH\n        echo \"  ↳
              Added to PYTHONPATH: '${NORMALIZED_NEW_PATH}'\"\n    else\n        echo
              \"  ↳ Already in PYTHONPATH: '${NORMALIZED_NEW_PATH}' (skipped)\"\n
              \   fi\ndone\n\n\n############## training env ################\nCOMMA_COUNT=$(echo
              \"$NVIDIA_VISIBLE_DEVICES\" | grep -o ',' | wc -l)\nexport PET_NPROC_PER_NODE=${PET_NPROC_PER_NODE:-$((COMMA_COUNT
              + 1))}\nexport PET_NNODES=${PET_NNODES:-1}\nexport PET_NODE_RANK=${PET_NODE_RANK:-0}\nexport
              PET_MASTER_ADDR=${PET_MASTER_ADDR:-$HOSTNAME}\nexport PET_MASTER_PORT=${PET_MASTER_PORT:-23456}\nexport
              ANC_DATA_IDX_DUMP_DIR=${CKPT_DIR}/${PROJECT_NAME}/dumpped_data\n\n\n################
              run training ################\ntorchrun --nproc_per_node=$PET_NPROC_PER_NODE
              \\\n    --nnodes=$PET_NNODES \\\n    --node_rank=$PET_NODE_RANK \\\n
              \   --master_addr=$PET_MASTER_ADDR \\\n    --master_port=$PET_MASTER_PORT
              \\\n    ${WORK_DIR}/Ocean/scripts/nemo_2_scripts/launch_pretrain_model_training.py
              \\\n    --num_node $PET_NNODES \\\n    --num_gpus_per_node $PET_NPROC_PER_NODE
              \\\n    ${BASE_YAML_CONFIG[@]} \\\n    ${MODEL_ARGS[@]} \\\n    ${PARALLEL_ARG[@]}
              \\\n    ${TOKENIZER_ARGS[@]} \\\n    ${DATA_ARGS[@]} \\\n    ${TRAINING_ARGS[@]}
              \\\n    ${VALIDATION_ARGS[@]} \\\n    ${LOGGING_ARGS[@]}\n"
            env:
            - name: NETWORK_PREFIX
              value: http://poc1-mlp.shiyak-office.com
            - name: WANDB_API_KEY
              value: ****************************************
            - name: MLP_CLUSTER
              value: poc1
            - name: MLP_PROJECT
              value: llm
            - name: MLP_USER
              value: llm-user
            - name: MLP_URL
              value: http://poc1-mlp.shiyak-office.com/console/dlc_job/job-ku4uzh4unm
            - name: MLP_ID
              value: job-ku4uzh4unm
            - name: MLP_NAME
              value: pretrain_moe_1b_8b_baseline_ax2
            - name: MLP_TYPE
              value: job
            - name: MLP_WORKERS
              value: "128"
            - name: MLP_GPUS
              value: "1024"
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: HOST_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            image: sea-hub.shiyak-office.com/nvcr.io-proxy/nvidia/nemo:25.02.rc5
            name: pytorch
            resources:
              limits:
                cpu: "171"
                memory: 1992294Mi
                nvidia.com/gpu: "8"
                rdma/hca: "8"
              requests:
                cpu: "153"
                memory: 1782579Mi
                nvidia.com/gpu: "8"
                rdma/hca: "8"
            securityContext:
              capabilities:
                add:
                - IPC_LOCK
                - SYS_NICE
            volumeMounts:
            - mountPath: /dev/shm
              name: dshm
          restartPolicy: Never
          volumes:
          - emptyDir:
              medium: Memory
              sizeLimit: 1Ti
            name: dshm
  type: PyTorchJob
status:
  conditions:
  - lastTransitionTime: "2025-07-03T12:57:55Z"
    message: job is pending
    reason: JobPending
    status: "True"
    type: Pending
  - lastTransitionTime: "2025-07-04T04:04:24Z"
    message: job is running
    reason: JobRunning
    status: "True"
    type: Running
  - lastTransitionTime: "2025-07-09T14:36:56Z"
    message: job suspended
    reason: JobSuspended
    status: "True"
    type: Suspended
  replicaStatuses:
    pytorch-master:
      podStatuses:
      - name: job-ku4uzh4unm-master-0
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:44:33Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:44:39Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:44:39Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://965688d9f8b3792e22840c01d42518120ca3e80ae3c40f9e6ace75eb1696885c
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:44:38Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://465eae43b2d1322a009763c9ea4204584b24b7dc399b49e615b8e77fbb283a0a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://e79b2c8aad5d463e79a31b88a1126988653cbad651ad00ad45051dbf9793550e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://e79b2c8aad5d463e79a31b88a1126988653cbad651ad00ad45051dbf9793550e
                exitCode: 0
                finishedAt: "2025-07-09T10:44:33Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
    pytorch-worker:
      podStatuses:
      - name: job-ku4uzh4unm-worker-0
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:01Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://5be776fa2344ff6382c18c4864942e3ca4d41f355e923239914c102816daec26
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:06Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://422c567f6712ab79f6c6da1c3ba3a433552e912ce80fbddcea13b8f3b725c443
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://77f857cf8d20200de721dff5cc4a13ab4ff1d63261471942490d9fde38076829
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://77f857cf8d20200de721dff5cc4a13ab4ff1d63261471942490d9fde38076829
                exitCode: 0
                finishedAt: "2025-07-09T10:44:28Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://a3174198e922613422da417394dfa0fd333252512619fc6492740671d599ae3d
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://a3174198e922613422da417394dfa0fd333252512619fc6492740671d599ae3d
                exitCode: 0
                finishedAt: "2025-07-09T10:45:00Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:30Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-1
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:01Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://7cbad3dbf4cb6537944a02bfd4b88d3a0a4d511e13c74abf2f157b6e00788e85
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:06Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://77ad04c4eb86c195a6a05e411badf7f36ec8454df366634f4b5cb031592ea8e0
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://68d47aeedcfca85d97c1b129319d40ca6766f097235f132615495d6d74046a0a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://68d47aeedcfca85d97c1b129319d40ca6766f097235f132615495d6d74046a0a
                exitCode: 0
                finishedAt: "2025-07-09T10:44:29Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://a4ef8cda16e3c502c5645357e2bfff180d4cecab73a6f928becd215c82489a8b
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://a4ef8cda16e3c502c5645357e2bfff180d4cecab73a6f928becd215c82489a8b
                exitCode: 0
                finishedAt: "2025-07-09T10:45:00Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:30Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-2
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:12Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:12Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://69368ca792dce31d74d02cb1f6f1df458ccf5f71b837e8806749bed684bb9fee
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:12Z"
          hostIP: ***********5
          hostIPs:
          - ip: ***********5
          initContainerStatuses:
          - containerID: containerd://a2e728704f049a0c6417568cd907d388d4db220bd00b4f2594de557ed31462a8
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://5b6bd35208c7bb656580ce77de62cb56d6731fefabe5b27ea2795d3d401efe86
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://5b6bd35208c7bb656580ce77de62cb56d6731fefabe5b27ea2795d3d401efe86
                exitCode: 0
                finishedAt: "2025-07-09T10:44:34Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://b9fc1f9ad708eb37f172e13ffe113543e063be0e034a655cd037b93ee12865f8
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://b9fc1f9ad708eb37f172e13ffe113543e063be0e034a655cd037b93ee12865f8
                exitCode: 0
                finishedAt: "2025-07-09T10:45:05Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:35Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-3
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://f7c3f471cda0b06a1b488ea507fdc5eec8d9c57a92640f06abb4433672a2b4fd
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:07Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://615cc76db12a0b551472e7d1de7875e676c7b125157404c2cea5afb88b47240a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://67c0ae1d6dfa16e398fc860bdf87ccfcf07d110ddc2a0cd67d39a75eafa61311
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://67c0ae1d6dfa16e398fc860bdf87ccfcf07d110ddc2a0cd67d39a75eafa61311
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://27105156f29a99a189e6fa005c1161537b801bd75ed4373c8dcaac88cd9e1df0
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://27105156f29a99a189e6fa005c1161537b801bd75ed4373c8dcaac88cd9e1df0
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-4
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://49c9712be675818d29b590c1bf8aa41c974fdff7bc5afa8c03b7b5527107d93c
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: **********
          hostIPs:
          - ip: **********
          initContainerStatuses:
          - containerID: containerd://00d4335e89365f20d835a8588a7e4ed99e9b8329d0267e3ba046d8ade1b61346
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://0cd808061ff138b3f89ca2c69619830e174c0dc062576eb3bb7a687a51bb5c98
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0cd808061ff138b3f89ca2c69619830e174c0dc062576eb3bb7a687a51bb5c98
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://7c24df8932bef43ff1c7e381d1b374bbd8c4a956df7c9a9143fb34d71f25148a
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://7c24df8932bef43ff1c7e381d1b374bbd8c4a956df7c9a9143fb34d71f25148a
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-5
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://056f41c0f211d5603628532ee8032fca9d76a92ec1d791ecbd45f520ba332357
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:09Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://d8d9d8b13860eb33ae578e7083bbaac89f7206c18a0a365df8c10725399789f0
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://3c87031e59ebd6a90942dde24c1dc7ac0bfde40d7a4caf3f520c27ed28a0952c
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://3c87031e59ebd6a90942dde24c1dc7ac0bfde40d7a4caf3f520c27ed28a0952c
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://8b102d88a69dd0ac2323f2aa6bcd5b58612adab1d04821844f8fdb2868272427
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://8b102d88a69dd0ac2323f2aa6bcd5b58612adab1d04821844f8fdb2868272427
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-6
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:27Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:01Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://ec4d158f1c6433f0b5b3d0fcabae9699c87e7b87f063f7ae96a992a0cfd7595c
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:06Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://21f35bc2be2e06581d02e6541c307f9403bc6cc86d45deff09ed07fefab9255f
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:27Z"
          - containerID: containerd://55daa0137621421d05608bb97f0aeea55d2de0230bb8b7b37a8c81f24e3a4833
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://55daa0137621421d05608bb97f0aeea55d2de0230bb8b7b37a8c81f24e3a4833
                exitCode: 0
                finishedAt: "2025-07-09T10:44:39Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:29Z"
          - containerID: containerd://a7cadacf97ee7cac379ad07c74cc9b8b81b17df7035f77b99e69b744f7e94184
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://a7cadacf97ee7cac379ad07c74cc9b8b81b17df7035f77b99e69b744f7e94184
                exitCode: 0
                finishedAt: "2025-07-09T10:45:00Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:40Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:25Z"
      - name: job-ku4uzh4unm-worker-7
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://6260de94df1760945a8ff29a865a98fef13476c73482ec5aafb722084dda9007
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ***********6
          hostIPs:
          - ip: ***********6
          initContainerStatuses:
          - containerID: containerd://5128dfd681f3ace85de2b7bbd1103dad2083a3c098557c5e0df4fc738241efbc
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:22Z"
          - containerID: containerd://aef5b10c61a49bd27f418b7dd88abc3ece267a46f6666d3d42612b64717298f0
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://aef5b10c61a49bd27f418b7dd88abc3ece267a46f6666d3d42612b64717298f0
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://b9ff4ae5c106e4b811cb8aa76c24a49b0dc8f48a4d9c716929e900325214b6c3
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://b9ff4ae5c106e4b811cb8aa76c24a49b0dc8f48a4d9c716929e900325214b6c3
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-8
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:05Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://54d5243e8437d2cc5101a44c02f1c1fa27859f1d62c79b797e404f4d4cec0411
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:10Z"
          hostIP: ***********9
          hostIPs:
          - ip: ***********9
          initContainerStatuses:
          - containerID: containerd://50f445b20c70dd2c171974aa69b17eb69b400646f0b46cd3ab51d6fcf3a874ad
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://867a010fcf564e7b0d6d27179250d1edeebf67ec48015de26c1243dee8775999
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://867a010fcf564e7b0d6d27179250d1edeebf67ec48015de26c1243dee8775999
                exitCode: 0
                finishedAt: "2025-07-09T10:44:33Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://88775dc68e895a36937d26972d209b76d19469449f7e5603b9318c6cc643dc8b
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://88775dc68e895a36937d26972d209b76d19469449f7e5603b9318c6cc643dc8b
                exitCode: 0
                finishedAt: "2025-07-09T10:45:04Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:34Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-9
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://fee2586217e5b1b2addab0acf47413011f05640bef13fdcd82900641b84c998c
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:07Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://979425514883ebaf1317e87115554dbef3fa58a116aa6743a66bb28c487dd924
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://d950c9a6dcd58a619dc3f98cb0b227089fd5c2d265b25a0ed37612e89d670ea4
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://d950c9a6dcd58a619dc3f98cb0b227089fd5c2d265b25a0ed37612e89d670ea4
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://7251166834aa71249237cb5fb7b3a219b928d62af0cd8a940e475f0641c1333e
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://7251166834aa71249237cb5fb7b3a219b928d62af0cd8a940e475f0641c1333e
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: ************
          podIPs:
          - ip: ************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-10
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:12Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:12Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://0d7ff3b4342a23de9afc2c6258db2c2b87bf547755b02814df4e185fa7f28f3e
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:12Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://2a071c0acccf1697559f3565f3fb2518c6ae5430f2113d92bbb01876774dc59a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://d01f520a14c634000bec4b2a26337df4dc9f24b5efd75ad20f9e03fc384a3644
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://d01f520a14c634000bec4b2a26337df4dc9f24b5efd75ad20f9e03fc384a3644
                exitCode: 0
                finishedAt: "2025-07-09T10:44:35Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://a6a99f7dce6526eeb1949d42e820ade164cf94a860ae8a6698dcfb1da29fcfef
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://a6a99f7dce6526eeb1949d42e820ade164cf94a860ae8a6698dcfb1da29fcfef
                exitCode: 0
                finishedAt: "2025-07-09T10:45:05Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:35Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-11
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:15Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:15Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://cb58f99fd04f6da614fc7229ad89a23b71cc98cc0cc239d4eb3486bdc9868d07
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:14Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://5135ea992a4904cedd6a90ac31bce5d83ee08a6d57880d02a4c32c062672ba8e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://d41fe90a0d808f4415ac53c93dfb92e0546042e8fb250d288c59e74aa55380ba
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://d41fe90a0d808f4415ac53c93dfb92e0546042e8fb250d288c59e74aa55380ba
                exitCode: 0
                finishedAt: "2025-07-09T10:44:35Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://95f93d83773638de89e8e68dd8fff970ad00c07145364340b6460d869f7a9ecf
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://95f93d83773638de89e8e68dd8fff970ad00c07145364340b6460d869f7a9ecf
                exitCode: 0
                finishedAt: "2025-07-09T10:45:05Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:35Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-12
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://86a2680d55deb1f8511554e672870c2364e9319d1ef408083850d2f16326ecbc
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:09Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://105ff9bbcd9010408237770452ab47dbb1c6ab63e43a4bb6680c28fd7da5b94a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://57f11d4d190c0897c7f4f1fba3233dc336d7cba3de0b8430ef7c23ffab330a4e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://57f11d4d190c0897c7f4f1fba3233dc336d7cba3de0b8430ef7c23ffab330a4e
                exitCode: 0
                finishedAt: "2025-07-09T10:44:33Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://85a028fd38c9e1f0daefa7637974a6718c91b849a538923301ae64b3218691fc
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://85a028fd38c9e1f0daefa7637974a6718c91b849a538923301ae64b3218691fc
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-13
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://126332facd9b6fb215125ead734d4a8a13582cc344cdad2d2b251a2e9658608e
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://60a925aba806b3fc0d0fb28230e6396fae9ee7a1f6e1d9a723db5691de180155
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://03cb329e3eef3f9ca42326e157ced1368e77894cab7386f022945e8a063026a9
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://03cb329e3eef3f9ca42326e157ced1368e77894cab7386f022945e8a063026a9
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://422eef732a96251eb0c78c536c843a00fa4567b6296ccebda85ce429553347e2
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://422eef732a96251eb0c78c536c843a00fa4567b6296ccebda85ce429553347e2
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-14
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://34eb3b787d14810a4acadceb1c7f6c219852a6c42c42e3c80508331cd34fb0cd
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ***********4
          hostIPs:
          - ip: ***********4
          initContainerStatuses:
          - containerID: containerd://5e4af56c4341396494d8a968b3761d5eb8953d2a1d2dbc1ea87e011143e5f327
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://0ecc2c38756ec7bb7e59b154337ad8cfcbdd8c479fd62a7d305e82259187611e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0ecc2c38756ec7bb7e59b154337ad8cfcbdd8c479fd62a7d305e82259187611e
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://fd5d4d63b2ea3c6e9c90134499e7bfb2f0f47cb067f928429226a0f3c81f05d4
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://fd5d4d63b2ea3c6e9c90134499e7bfb2f0f47cb067f928429226a0f3c81f05d4
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-15
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://660d4509f5b9c5b9e6ad1ce2fd2b0aec660a6de180e057ba270df6ac98c87c6d
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:07Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://4cb37010c9387b62f002fcd172021342eb28d6d4c7ec8d6ce59b1a1588b27246
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://b2aa093b10723d746828787f0f8e1aff76494505248f2bc43fd9306418724acf
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://b2aa093b10723d746828787f0f8e1aff76494505248f2bc43fd9306418724acf
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://08700b87f3d137c0569c8f5bdce54f491e1177b15b9a330eadb52a7200f4fc19
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://08700b87f3d137c0569c8f5bdce54f491e1177b15b9a330eadb52a7200f4fc19
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-16
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://d7dc1c0a2da7d682331b44eafd4d617b76f889fdb744a6d5ef231135a8e5a269
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:07Z"
          hostIP: **********7
          hostIPs:
          - ip: **********7
          initContainerStatuses:
          - containerID: containerd://458a488a96bfbe0d075a4b961fe22edadee52718df1ceaad0065fbd9ea5f7019
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://ca78611806529d3c9bfc7620d95e30e22eccfa0f2d9580b0e03ba4e6110158f1
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://ca78611806529d3c9bfc7620d95e30e22eccfa0f2d9580b0e03ba4e6110158f1
                exitCode: 0
                finishedAt: "2025-07-09T10:44:29Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://73278438d426e2a31435bc4a65efa6220b276746ad01e6adebb8b7e61ca3e420
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://73278438d426e2a31435bc4a65efa6220b276746ad01e6adebb8b7e61ca3e420
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-17
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://d264032bedfdd834b80f925aa0edca30222511bbef034c94ad4656ec76030a84
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:10Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://60d9359597180ed929bbdab6ab4c340aa5991ab97553218bf43b5d467b98524a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://8c6952ae337c71a8b8a8feac749ada4a81541236bf60a29c149375c029e228e9
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://8c6952ae337c71a8b8a8feac749ada4a81541236bf60a29c149375c029e228e9
                exitCode: 0
                finishedAt: "2025-07-09T10:44:33Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://c869b4fc421b8fb404ac5037e55890db490f4e5afebb3ab8c95910b7ff59c2da
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://c869b4fc421b8fb404ac5037e55890db490f4e5afebb3ab8c95910b7ff59c2da
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-18
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://636a161c57147b4d7289293171312874b0d0eed5e3e26e50705587ad3c8ec923
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://830b7fd3aaa2d2da390eec40d52eeaf99340319d02d4eea3e573bacc9c40b35e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://b13f30f1b7afbb733acd809d022bb4b5276de697257de15aca13810e571b58cf
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://b13f30f1b7afbb733acd809d022bb4b5276de697257de15aca13810e571b58cf
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://1f275337e27b7c026bfe78060aa71cccbe114dcb39da9eb995225c1fe2304738
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://1f275337e27b7c026bfe78060aa71cccbe114dcb39da9eb995225c1fe2304738
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-19
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://aa407560e9c955b436d6abb02f53cb7bb7694bf13b533520846bd18d4bd46c04
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:07Z"
          hostIP: **********0
          hostIPs:
          - ip: **********0
          initContainerStatuses:
          - containerID: containerd://075973586cff4f2643e1e2dbba9deb2cb9a4f088ff51c4d585428c2c3df8e8b1
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://5b6fd390f93caea9099c4b1144c454ca26eaa94c6ea462d6bcde3d68ae673b9b
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://5b6fd390f93caea9099c4b1144c454ca26eaa94c6ea462d6bcde3d68ae673b9b
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://bf01040779e71f35079ffd1dc986a5312e3600ae30876e625249a307a1fc99d8
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://bf01040779e71f35079ffd1dc986a5312e3600ae30876e625249a307a1fc99d8
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-20
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:44:59Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://ae14f245aee92a8a9ebcd1e93fd29ccd96480cdb60933318d60b0fda85d240d1
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:04Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://be0139b3324b93a3182d320104089242629e642b1f70bd0a8b3945ba488e3e0d
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://7042f08a055d54b0aca73f19deb72fa066b458355401dc50f90240595f51a26f
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://7042f08a055d54b0aca73f19deb72fa066b458355401dc50f90240595f51a26f
                exitCode: 0
                finishedAt: "2025-07-09T10:44:28Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://b1be0a387f6afd2f1a7da6cd1e4c87039c6e56e5d17636079b6311361f8982cc
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://b1be0a387f6afd2f1a7da6cd1e4c87039c6e56e5d17636079b6311361f8982cc
                exitCode: 0
                finishedAt: "2025-07-09T10:44:58Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:28Z"
          phase: Running
          podIP: ************
          podIPs:
          - ip: ************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-21
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:26Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:44:59Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://0c891e00ef3edf08fac76a250cb3a1bbca8ca3d8604b8a83f7b0baa23774a5a4
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:04Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://bb4a9e3fa071afb43c80f5183fe4fe79b6ed53206decb8a725440bdb40081769
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://2f849027d6d6cc486ee3eda07e1b1db94969e68ca7d9621a70c306166fb53e34
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://2f849027d6d6cc486ee3eda07e1b1db94969e68ca7d9621a70c306166fb53e34
                exitCode: 0
                finishedAt: "2025-07-09T10:44:37Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:27Z"
          - containerID: containerd://aa590363951a38ac31cde53ecf04ab53a686fe20af9d8ad7aa9b359869c887ab
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://aa590363951a38ac31cde53ecf04ab53a686fe20af9d8ad7aa9b359869c887ab
                exitCode: 0
                finishedAt: "2025-07-09T10:44:59Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:39Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:23Z"
      - name: job-ku4uzh4unm-worker-22
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:14Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:14Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://f78260aaf01a19a0807e08a059a894c7f20f35cb33fce81dffaf0498d1c9a26b
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:13Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://906f3530954af080b76706d139bae0d3bd2f940b290b2d4a013da9de18d84fe0
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://325b7a112faa64eba147200d07a30f04b9fd33cf34a6cfcdc83ac294a3918644
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://325b7a112faa64eba147200d07a30f04b9fd33cf34a6cfcdc83ac294a3918644
                exitCode: 0
                finishedAt: "2025-07-09T10:44:36Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://0723bc87e40f2bfcc2d772b48697d1f93a598addef17efe0daa1632b28d8c61c
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0723bc87e40f2bfcc2d772b48697d1f93a598addef17efe0daa1632b28d8c61c
                exitCode: 0
                finishedAt: "2025-07-09T10:45:07Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:37Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-23
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://751de11e837c723a72e17087feb49b111a007d4ca529434df89ee68c0b7ac81f
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:15Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://bb6425d8510e026974face0ab5a5883ef1039f54a086844b6e830cc52b9c793d
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://5e5abd8da7a40dcc820b23bbdb161f50f27ab1ff7c55a16b0f3f54a67d55f318
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://5e5abd8da7a40dcc820b23bbdb161f50f27ab1ff7c55a16b0f3f54a67d55f318
                exitCode: 0
                finishedAt: "2025-07-09T10:44:35Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://633503ac4c5888c77a459984f658251db46790c5eab3305d0e5d0a0cab27afcd
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://633503ac4c5888c77a459984f658251db46790c5eab3305d0e5d0a0cab27afcd
                exitCode: 0
                finishedAt: "2025-07-09T10:45:06Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:36Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-24
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:14Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:14Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://41345cbd1d9c4d14770eb2538fca12a72b2d5d1725f26bf4f4ab65d9c8becf5d
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:13Z"
          hostIP: **********0
          hostIPs:
          - ip: **********0
          initContainerStatuses:
          - containerID: containerd://ae310870795074bd5d89fd4dd9d2c2c0fc19caf7e05ee40249dc7abf82baa358
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://cef489bc3c83aa8974bfa847b32046ae42fe199032023d7e3a9eac4dff703e3e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://cef489bc3c83aa8974bfa847b32046ae42fe199032023d7e3a9eac4dff703e3e
                exitCode: 0
                finishedAt: "2025-07-09T10:44:35Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://af0fe08b38841cabe2bc3afdb95576a26af54dba4d5a879d99c792f55fd01913
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://af0fe08b38841cabe2bc3afdb95576a26af54dba4d5a879d99c792f55fd01913
                exitCode: 0
                finishedAt: "2025-07-09T10:45:06Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:36Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-25
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://3e29ef12d488eef9e712f9be3acb53f11dcf334b4e99465d203f9a0ff8c5f88b
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:07Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://a37779aa6c161f11be50bba3ee80314dc0fdda9c400a1d16823bbbf9d6c8d7c5
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://2f3535ed2c16fc234946d41021c4bc149721e50328247305ce8c713017d59546
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://2f3535ed2c16fc234946d41021c4bc149721e50328247305ce8c713017d59546
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://ab72d09c9153b26fc565749598831a5d1725d805f0f5b1bff9b7190e6f9f27b2
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://ab72d09c9153b26fc565749598831a5d1725d805f0f5b1bff9b7190e6f9f27b2
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-26
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:12Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:12Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://483efe9533e7ba1321547457133742c28ace4ba05f27cb0b9187cd08062cc513
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:12Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://78aa12c0d1b425961e7c39fc1ab3ebc1320a30769276933179bfd633f83da037
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://78bbfb26abd48de8aa1ed91bf6050679c74a7252d99836d319f9e751714d1614
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://78bbfb26abd48de8aa1ed91bf6050679c74a7252d99836d319f9e751714d1614
                exitCode: 0
                finishedAt: "2025-07-09T10:44:26Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://753702ef6a39e2536a2ed8af2e11485968586a4a298016eff9485be96004733e
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://753702ef6a39e2536a2ed8af2e11485968586a4a298016eff9485be96004733e
                exitCode: 0
                finishedAt: "2025-07-09T10:45:07Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:27Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-27
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://e44f886ab4ea702c2805777d336009b72ef812734aaab3677283f27a43a063de
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:07Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://f8d8ae834d86654ed4af3c59933c8c03ff82b43fbca20f73ff3d57405f856986
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://27b508a468749222e8728320c1e159358ff6769c0c823865cb650f8e227c7171
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://27b508a468749222e8728320c1e159358ff6769c0c823865cb650f8e227c7171
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://016ad6e8cb6e4248b6c7642ebbd8d617037eba43ab355dc3e195325c290f79a3
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://016ad6e8cb6e4248b6c7642ebbd8d617037eba43ab355dc3e195325c290f79a3
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-28
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://388859a46ebe5d045962992a9967b5da5a8808c46e74dd3fb3e99afd65803823
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:09Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://36858425458512b4cd63add29080ca519ed94866f855c716d4ce52f781ed2640
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://6125016eed4469a9dd700dc3a54a1f63ff79c31aa3bf38f5aa18c2b7c35c80cc
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://6125016eed4469a9dd700dc3a54a1f63ff79c31aa3bf38f5aa18c2b7c35c80cc
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://84d9920a0f207d33dad78dd26720bf4c3df550c1f1a33d33544c94f60b650d3f
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://84d9920a0f207d33dad78dd26720bf4c3df550c1f1a33d33544c94f60b650d3f
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-29
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:25Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://3dfe4133eb76233167ecdce266377a41a2604fa40ac1e62a85d5fa31996052ab
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:09Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://0404ec1939a72af3a1026160e2bf8545861081a15b57ee683afff1576364bf69
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://d17e173d4c7162a8dd0ab535c6bec4832b0f5843f9407fedf50044ce5adc4fde
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://d17e173d4c7162a8dd0ab535c6bec4832b0f5843f9407fedf50044ce5adc4fde
                exitCode: 0
                finishedAt: "2025-07-09T10:44:33Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://dce9736afa686738143d61effb932884c23c83464f5d2a2061feb740eb3e59d5
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://dce9736afa686738143d61effb932884c23c83464f5d2a2061feb740eb3e59d5
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-30
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:00Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://9b29f0881b965512592c8a2137c9581654a5e9a7667953a80cf934380d67c482
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:06Z"
          hostIP: ***********3
          hostIPs:
          - ip: ***********3
          initContainerStatuses:
          - containerID: containerd://c89237649a5cc742e362d560b8d27de7d47d790fb30eeb9f4a50ac02b4cf0de0
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://d236a9f61b45f6d0e7ce0a21650f9a0ec1a5b7a26d6933343c1978f6e41a7a86
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://d236a9f61b45f6d0e7ce0a21650f9a0ec1a5b7a26d6933343c1978f6e41a7a86
                exitCode: 0
                finishedAt: "2025-07-09T10:44:29Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://a3633d328d2e3c33dd0574d50c3c21bf7f3f37a6bc08767b9a2d42a3c006739b
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://a3633d328d2e3c33dd0574d50c3c21bf7f3f37a6bc08767b9a2d42a3c006739b
                exitCode: 0
                finishedAt: "2025-07-09T10:45:00Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:30Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-31
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:25Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://1ac3f6b4e1757b266826861f876380da47fbf74ed07e4bf27f52a5dd7dd870cc
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://1146a75a9432ca1860e3950790c2152bf22ae083089611c4a99594a034de3406
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://89c45f18bbf1f85e9560e1787f7bb82c8c6a87a50e215be400503d8fc98c210a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://89c45f18bbf1f85e9560e1787f7bb82c8c6a87a50e215be400503d8fc98c210a
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://feaa4b1770a2a7c9bb9727d05d667191cfba97e9db7db45bcbc0f0a06acbcf4b
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://feaa4b1770a2a7c9bb9727d05d667191cfba97e9db7db45bcbc0f0a06acbcf4b
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: ************
          podIPs:
          - ip: ************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:22Z"
      - name: job-ku4uzh4unm-worker-32
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://ee5f0204590b30dc158831e4b5a5d10daff6f65d0bf4f8d62b761e1dec64da13
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://d5b64ed4149d6414143482ddaef8549eb89baf6adfd70568e36e9343a84ac60f
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://30474613358474d379752c5ccaf518e589e38bc0f85c867085f710aa09f08927
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://30474613358474d379752c5ccaf518e589e38bc0f85c867085f710aa09f08927
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://f1585fd897d223752467875111ccb02f0374274b71c5518c8814c6e1cb198754
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://f1585fd897d223752467875111ccb02f0374274b71c5518c8814c6e1cb198754
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-33
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:01Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://1e6945e0ab9cb2e760d1e5a7adfb4a4178b7f54aa50654c4098107800b4661c2
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:06Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://494b69c7ef671a87daf7291d14569652574d5b66922152d7282191e24485fa4b
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://fcd46a674d2866d281c10ea07a9ec5848ca0e911df38ec03eeabc8652014c8cb
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://fcd46a674d2866d281c10ea07a9ec5848ca0e911df38ec03eeabc8652014c8cb
                exitCode: 0
                finishedAt: "2025-07-09T10:44:29Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://02dded88b15c940143043ac0b6384eb43cb666f74115fb0c4ef182f2ccce78b6
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://02dded88b15c940143043ac0b6384eb43cb666f74115fb0c4ef182f2ccce78b6
                exitCode: 0
                finishedAt: "2025-07-09T10:45:00Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:30Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-34
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://be2b6a4efa2ec077c5c0b3135f4a229f5ad08336bf52729fb0310a75fe8f486a
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:15Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://2374d0e24499af60a93be595eb071728c9ef6e3114323226316bff4a760005d8
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://8a53d33aacafc36fdfb85c08fd5321029e5360db4d4d3e2fcc5f4a7a6154290c
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://8a53d33aacafc36fdfb85c08fd5321029e5360db4d4d3e2fcc5f4a7a6154290c
                exitCode: 0
                finishedAt: "2025-07-09T10:44:29Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://d889e44e98dad891a8240e750abd86a77d851e18c0f9517e1c23f707f2c123c6
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://d889e44e98dad891a8240e750abd86a77d851e18c0f9517e1c23f707f2c123c6
                exitCode: 0
                finishedAt: "2025-07-09T10:45:10Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:30Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-35
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://d3809f768f772420b91807caebdd26dbf52954c418ab916a178dfc15e83c4658
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:15Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://5189b1624c8b99ec84411e410c927ea72356205273eee930d5ae0b698855ae40
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://01b811bc2449d3fdfcee30cb510c377909adaaaa0300d6bb4d58fb2022cab276
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://01b811bc2449d3fdfcee30cb510c377909adaaaa0300d6bb4d58fb2022cab276
                exitCode: 0
                finishedAt: "2025-07-09T10:44:28Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://46eca1fd73f3f7e8cb4b65765eeb5cf6e1c40844de7e6ea874ee7dbf95d4c980
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://46eca1fd73f3f7e8cb4b65765eeb5cf6e1c40844de7e6ea874ee7dbf95d4c980
                exitCode: 0
                finishedAt: "2025-07-09T10:45:09Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:29Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-36
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:05Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://dd11c8afb9215876d3572e53eee377773800ae68d23cd25a9b1cfed8ef03e006
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:10Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://6264ed4e67a8e131d96a5b2a7eb13f9bf786de2515e0c32436e8416caad76c2a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://fa3a2b1524ea74813ef53f0dd91a4b5474195a4204e68931f60ee9f25bef5ae6
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://fa3a2b1524ea74813ef53f0dd91a4b5474195a4204e68931f60ee9f25bef5ae6
                exitCode: 0
                finishedAt: "2025-07-09T10:44:34Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://22aac19d5961cba9b4d9577aa1ff32e738671f2ddad9f53850fa3626da7b7d2a
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://22aac19d5961cba9b4d9577aa1ff32e738671f2ddad9f53850fa3626da7b7d2a
                exitCode: 0
                finishedAt: "2025-07-09T10:45:05Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:35Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:22Z"
      - name: job-ku4uzh4unm-worker-37
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:25Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:05Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://72f14a7635f258c5129fa61c08f38227c6033cb2fbce88585cf2f6b34af64691
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:10Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://71c5ebb2eda1080a6cef22f67eaa8a32d50f8ea80e05b0c8d6d0b39a5fa54ff9
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://7b902619b8bd7951497d15d2e871bffcb22d5d4c676278d636a5a3ed3639ebfb
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://7b902619b8bd7951497d15d2e871bffcb22d5d4c676278d636a5a3ed3639ebfb
                exitCode: 0
                finishedAt: "2025-07-09T10:44:33Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://59a728c30881a1c64845cb197b668fa277250ca3e2aa9b212418db736fcbff53
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://59a728c30881a1c64845cb197b668fa277250ca3e2aa9b212418db736fcbff53
                exitCode: 0
                finishedAt: "2025-07-09T10:45:04Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:34Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-38
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:44:59Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:05Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:05Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://f35d39274abfab4e377cacbdf427a667b09a0e86f2d787464ee5bd45ff7bc612
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:05Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://74e67c535741b007555f78df85b28bbddb6620ec8a0e8ce1253cfd45f39f3fbf
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://e084ee4fe545fccef25232ba7380cd898f44efe94aafd261a949308df06936ac
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://e084ee4fe545fccef25232ba7380cd898f44efe94aafd261a949308df06936ac
                exitCode: 0
                finishedAt: "2025-07-09T10:44:28Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://5a330b2775a04a8c8010b6135ed035a32f9dcc9ff4e4325887357598d3b1fbc8
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://5a330b2775a04a8c8010b6135ed035a32f9dcc9ff4e4325887357598d3b1fbc8
                exitCode: 0
                finishedAt: "2025-07-09T10:44:59Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:29Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-39
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:05Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://c576d9add101648f28056ceafdc1165d9e4c1bb704b30bb23ce38bb52ff2ea17
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:10Z"
          hostIP: **********3
          hostIPs:
          - ip: **********3
          initContainerStatuses:
          - containerID: containerd://133a8eba834df51b641cbd82c1de4e23942aa0350f73e1bc8dc2ef66b9522f5e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://85044f590e683f1d87978b8610586f25995d384d237ef1760d97254b4793e0b2
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://85044f590e683f1d87978b8610586f25995d384d237ef1760d97254b4793e0b2
                exitCode: 0
                finishedAt: "2025-07-09T10:44:33Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://217c4c5152ce713729492ea373dc67045df1314bb78bdf4cd57a49bfcc6572c4
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://217c4c5152ce713729492ea373dc67045df1314bb78bdf4cd57a49bfcc6572c4
                exitCode: 0
                finishedAt: "2025-07-09T10:45:04Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:34Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-40
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:25Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://a9b90d6fcd6b0b2abadbece17d67f00c24e95adc128a1f5481503f7aa1d46288
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:09Z"
          hostIP: **********
          hostIPs:
          - ip: **********
          initContainerStatuses:
          - containerID: containerd://39e1410e7984f9017381ec15f72a3cfa2d0759dfa8eecfaec72de00c3e2f4008
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://ffb0c4ed79d2a7b5b40ad0e8ae09555108d73164d110c1048bfc2399206c1195
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://ffb0c4ed79d2a7b5b40ad0e8ae09555108d73164d110c1048bfc2399206c1195
                exitCode: 0
                finishedAt: "2025-07-09T10:44:33Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://83cff9918a89c229bc5b67d9fcaa69ee9c82b81a7d4f682481c7d8e8af5cc6d9
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://83cff9918a89c229bc5b67d9fcaa69ee9c82b81a7d4f682481c7d8e8af5cc6d9
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-41
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://950e1fd67c59660c7d250703d22f67e58e078c4a32197c23a9bfb5384e2563b5
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://e97dcd40774928dc886f1146c708ba3b44225b14e65b0cc243d6645ff7f9f700
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://22c71048a8d44486e32c2679eaaffbe8f26d07568deabef077c94509df95c6ac
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://22c71048a8d44486e32c2679eaaffbe8f26d07568deabef077c94509df95c6ac
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://44d11cdb186754a3b3912d7caa43c4ec29367524e613c20c507f83a7e9c58774
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://44d11cdb186754a3b3912d7caa43c4ec29367524e613c20c507f83a7e9c58774
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-42
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://075cfd4f0bac951685bd32f1153bf3449feff20b58a425ff6ae9781195f4e0ef
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:09Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://404a3918f78786c86a01c43ff7017ace1332831b7f2bf3ff2dad69dbe6e4da80
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://d08bbfa11ef439f7fe69134bb128a0c30f83dc0ebbf50aca989db60ed9242272
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://d08bbfa11ef439f7fe69134bb128a0c30f83dc0ebbf50aca989db60ed9242272
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://df6ce7908131fc1d6f815cc488fca30bcb2ab62f431f2193fcc9289f6d9fab1f
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://df6ce7908131fc1d6f815cc488fca30bcb2ab62f431f2193fcc9289f6d9fab1f
                exitCode: 0
                finishedAt: "2025-07-09T10:45:04Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:34Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-43
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:05Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://dc89dab5786b39ffbbd1c7fb31703c0ec95ef0d364c50007b25ef5c66370adb0
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:10Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://afb483655b177b30c15251c1e398238a98a0f8660c110287e23866b2f5c54bb1
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://27c19cf8dd9b98570155a397f88c01307a93bd45a9b35389bd3f408456d63a9b
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://27c19cf8dd9b98570155a397f88c01307a93bd45a9b35389bd3f408456d63a9b
                exitCode: 0
                finishedAt: "2025-07-09T10:44:34Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://225d929387771765c0bfdfb6d1bc02abb588070df186c64a9b06fe1323eb873c
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://225d929387771765c0bfdfb6d1bc02abb588070df186c64a9b06fe1323eb873c
                exitCode: 0
                finishedAt: "2025-07-09T10:45:04Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:34Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-44
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:15Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:15Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://8e8ea21af53981b811b6c61af7cb48f4ef2ba0fb978291d707f206d54dd77a33
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:15Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://266ce2743e248d60dfbd5874554fa6faa0c002b8b2a46620b3f200897cd577d4
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://9867225e31d3609f026cac9d09d5a232063d3c22bc869175c49017148acc47fa
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://9867225e31d3609f026cac9d09d5a232063d3c22bc869175c49017148acc47fa
                exitCode: 0
                finishedAt: "2025-07-09T10:44:29Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://e0c363df65a63c049c9796601199d47fe788415ecabe46f90b33b0509f9e8383
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://e0c363df65a63c049c9796601199d47fe788415ecabe46f90b33b0509f9e8383
                exitCode: 0
                finishedAt: "2025-07-09T10:45:09Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:29Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-45
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://0f87489e877fc0a5e6be9f49db2263703caf3ac860554b9a4524ad814b0de1e7
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://3a012fe2802c5073d84ea23c87d42b6a5812e0f5e0c7760650d7b62985c747bb
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://686c1cc0946208bd34dec1a4e264b06515061e7c0fc9709e332178feb2909f80
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://686c1cc0946208bd34dec1a4e264b06515061e7c0fc9709e332178feb2909f80
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://337c87f0c17971c17dbff707706d69ee0e8f9e87d4c1d375df499a156e7aa912
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://337c87f0c17971c17dbff707706d69ee0e8f9e87d4c1d375df499a156e7aa912
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-46
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://7db1da2850fdaddf73ffde99752ac360f85dab00bbc6fb823f673fead134c13d
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:10Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://26b276fdee99e095b19e4ed19b26a18518e0ad4b41649c6fb865fc3bdfd3fc5f
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://937ebec0ccba96d8b9dcac0ac760790941279e04244944776f0c90eb8553e0d9
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://937ebec0ccba96d8b9dcac0ac760790941279e04244944776f0c90eb8553e0d9
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://de274c214bb44590bbb07ca5b5b96f28eb0fe1974f066adfe969883d4f01b383
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://de274c214bb44590bbb07ca5b5b96f28eb0fe1974f066adfe969883d4f01b383
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-47
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://ee91015305b1b7b584fa8246be91b7a8645b62a32038cf4004415959017d97e3
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://bdfabf0c08949a2ef558b862e4db1a3944aaadbf65a495525366d57058e8175f
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://7a815811406c791e0ffb56ee760ee8d91d01f1acce7db6e8495a74dfa2ec0117
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://7a815811406c791e0ffb56ee760ee8d91d01f1acce7db6e8495a74dfa2ec0117
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://754d8078ba4b6d3c35d0b96793213e153356198dcaf7196e8078074e91ca5e89
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://754d8078ba4b6d3c35d0b96793213e153356198dcaf7196e8078074e91ca5e89
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-48
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:01Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://4a37f721c9d39d7c23b8e8ae1b5d8450aa62de2d08c193421a5070a16696b1d7
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:06Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://865ce7b9cbcc832478215386cc61ad6beb55ab0cb05f3e8b28e5e8cf8303b5cd
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://bafda18a241e5f761354102e302f145d912533a01055028e19bffdc5508a1926
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://bafda18a241e5f761354102e302f145d912533a01055028e19bffdc5508a1926
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://9da62a554052fd03b8b49b0eef3adb3e18b4980bad934df31ea92da1dc6b7278
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://9da62a554052fd03b8b49b0eef3adb3e18b4980bad934df31ea92da1dc6b7278
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-49
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:05Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:12Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:12Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://612c97d59364678402423e199d4482cba65e236ccd4df9f3f164962fe4d8874a
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:12Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://0e0a3c706cd6172b9f63af0a57f93e91d92e311742f44e483c868e0216d84e5e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://ba468735e46d9952161da0ec363e2cdd4d14da7b4fddf35a786a017e3e8d3d41
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://ba468735e46d9952161da0ec363e2cdd4d14da7b4fddf35a786a017e3e8d3d41
                exitCode: 0
                finishedAt: "2025-07-09T10:44:34Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://fcd13654663b2c9a24dab225725b134f16da71c5e2a5f4971c50ec2f9ce18aae
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://fcd13654663b2c9a24dab225725b134f16da71c5e2a5f4971c50ec2f9ce18aae
                exitCode: 0
                finishedAt: "2025-07-09T10:45:05Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:35Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-50
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:25Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:12Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:12Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://2cc9f1af0228ac570ea0e5ea6f9d2bfde8b3b0078297f8623426ed4c0b97f8b9
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:12Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://8c466c2c1f57d603e495a5358bd1eb94a9fb6b4076dbc8da0b22d0c20b26e19b
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://3653f2655b74417864bae99f1a03ae6286a13f38d8985bfa80006bde0118be82
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://3653f2655b74417864bae99f1a03ae6286a13f38d8985bfa80006bde0118be82
                exitCode: 0
                finishedAt: "2025-07-09T10:44:35Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://be15b0b3137d071dfe861a1ec608f26936e9e4e307672043dc7dba8c093cdd41
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://be15b0b3137d071dfe861a1ec608f26936e9e4e307672043dc7dba8c093cdd41
                exitCode: 0
                finishedAt: "2025-07-09T10:45:06Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:36Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:22Z"
      - name: job-ku4uzh4unm-worker-51
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:05Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://102bd9caee26a42591655a2aa4e1f9cad1dd125b0c81699f8931588b28c5f17d
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:10Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://b4ef7bf0f5ce7250432c6feb5cf96fd7cd6f723d36edd7af1a34425a8cb48551
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://5e5bb46e393f4621de65e0b5307c689365f22fe215c776f7c2e2ec48c6676e89
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://5e5bb46e393f4621de65e0b5307c689365f22fe215c776f7c2e2ec48c6676e89
                exitCode: 0
                finishedAt: "2025-07-09T10:44:33Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://53251dc78913a148b12fb2a6ab8804053082eca0b69b8e516c96a8783292be3e
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://53251dc78913a148b12fb2a6ab8804053082eca0b69b8e516c96a8783292be3e
                exitCode: 0
                finishedAt: "2025-07-09T10:45:04Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:34Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-52
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://8012979263479058b55293344f32906688cdf6d0202c3f0b04333b61e2458652
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:07Z"
          hostIP: ***********7
          hostIPs:
          - ip: ***********7
          initContainerStatuses:
          - containerID: containerd://98917cacf3b2a0bbe0a39573ec71def23bb94cbb5b097ce5fd6a4c908a57bfcd
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://80831ce049ff62ff52ef4fb2142a665a673c2952f14aad32c803f8922ce85045
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://80831ce049ff62ff52ef4fb2142a665a673c2952f14aad32c803f8922ce85045
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://9c3b3e0dc3b76f8ae184f0cee52acbce2ff64ecf60cd09ee7da3bb9c813c48a4
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://9c3b3e0dc3b76f8ae184f0cee52acbce2ff64ecf60cd09ee7da3bb9c813c48a4
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-53
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:12Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:12Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://23cdfbe123757eeeb4475447772aefafff0a91d076a91261ef3cf320413e77bf
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:12Z"
          hostIP: **********
          hostIPs:
          - ip: **********
          initContainerStatuses:
          - containerID: containerd://61100154cd2239b873805b43aa4549ba7a9de3073220e517a1ca331f47bda423
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://6e2a83e79084a6f6917d90bb6d02bc09a69cc2be24a01c8c7ff30e4c006c4610
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://6e2a83e79084a6f6917d90bb6d02bc09a69cc2be24a01c8c7ff30e4c006c4610
                exitCode: 0
                finishedAt: "2025-07-09T10:44:34Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://b0015cc455573d2c905c3f4cd98e0cbaa55b31b7416d223ba92df6d1d00ceb70
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://b0015cc455573d2c905c3f4cd98e0cbaa55b31b7416d223ba92df6d1d00ceb70
                exitCode: 0
                finishedAt: "2025-07-09T10:45:05Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:35Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-54
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://b8819e1fde30d1c395dfbe79192a2ae3a374d55fb38cc3495cbfe3c43a73f45c
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://1a6d64e8da7d2bc0e24537918fa5e91c7cce8f532fb848f81b920161400f06a5
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://8a92a1c08dbb0b54ee6a863bfbd5355bb3ada0c3cbcd08fd4bead6188a578895
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://8a92a1c08dbb0b54ee6a863bfbd5355bb3ada0c3cbcd08fd4bead6188a578895
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://097e8a1668d9cdcf43ed2abda6909712c3c27a59213e029d28114b3417be4f9b
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://097e8a1668d9cdcf43ed2abda6909712c3c27a59213e029d28114b3417be4f9b
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-55
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:00Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://f7f99a92c920cf2aee5b66710ece4508682c063826fe3e25e1497c0a3b3aa8ee
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:06Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://fd37c698be2a1bba28fcb7486f6a6d49125775133a10d2c36d9aa2b8feaac9c5
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://6bd5a01ce686ef22283987b667d5ab34dae334e65e1bf21a80c45a03c8714509
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://6bd5a01ce686ef22283987b667d5ab34dae334e65e1bf21a80c45a03c8714509
                exitCode: 0
                finishedAt: "2025-07-09T10:44:29Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://f80e98e6b6d9a98e6c3006ae766d6365603cc00caaafaf3f5a2499fc7077da80
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://f80e98e6b6d9a98e6c3006ae766d6365603cc00caaafaf3f5a2499fc7077da80
                exitCode: 0
                finishedAt: "2025-07-09T10:45:00Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:30Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-56
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://d6864c3eaec2e0c475936a7e21887a25caa9394ec480f82067a8b92fc40d4624
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:10Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://80e324d6188bb92998ffbc0cacdb2cac6ca7b6ea823bc9a01f65e4b4f3d1a0bc
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://ffd91e3e2872566d4dcf9ed5a28fb2e99993bfa84fe498bb9b5185bb85f4c6b3
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://ffd91e3e2872566d4dcf9ed5a28fb2e99993bfa84fe498bb9b5185bb85f4c6b3
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://81f68abe50376179e243334de7a3db33f9359a932eb515f21ac7a3f0436c5fea
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://81f68abe50376179e243334de7a3db33f9359a932eb515f21ac7a3f0436c5fea
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-57
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:01Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://49adb074872a61a1a2538f3e7e7eaae2e231e043c46b8f830d04a9373c5ac5c5
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:06Z"
          hostIP: **********
          hostIPs:
          - ip: **********
          initContainerStatuses:
          - containerID: containerd://d5690c1c009657372cfbfab5f9b9b05f52b2e0bb00e8cc35715cf3071caf3eed
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://e89de4df8cdfde57cdb91f12282c5b14df6536b724d152ac0edc0870fc3da19d
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://e89de4df8cdfde57cdb91f12282c5b14df6536b724d152ac0edc0870fc3da19d
                exitCode: 0
                finishedAt: "2025-07-09T10:44:29Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://8b2bf259fd4729b52d9346c32ed50022afc76aa154c01c56efe0fad816d2c344
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://8b2bf259fd4729b52d9346c32ed50022afc76aa154c01c56efe0fad816d2c344
                exitCode: 0
                finishedAt: "2025-07-09T10:45:00Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:30Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-58
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://a0a0fd638631aaaf05ae596705394db3958b42ab519aa2f994e8633df7ea4cf6
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:07Z"
          hostIP: **********
          hostIPs:
          - ip: **********
          initContainerStatuses:
          - containerID: containerd://45c8ca0291dfefdc87a95f4c30ffb12b8041977cc6efc5167b9e87810f111fd6
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://7e9663a900db2929ac880f60a012ee8ac2e02f7c29cf118973474ff6093aa1f5
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://7e9663a900db2929ac880f60a012ee8ac2e02f7c29cf118973474ff6093aa1f5
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://7a13caaa06c6595c31484d61e3d20e5a8d8a2a6cea1224c4197860e1cd7a47e4
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://7a13caaa06c6595c31484d61e3d20e5a8d8a2a6cea1224c4197860e1cd7a47e4
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-59
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://3ad136a579f55b535cda1587684f3f49069151c855523fdf28983fdff240d5ef
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://bcdf0edc8b0f8b50a2af1d3c1cc1742b9a566bdaeeac423642accdee597f4ee3
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://5294104240d5a14701f113f77f78c8882a89a629b51f45cec180d99ef2bd5817
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://5294104240d5a14701f113f77f78c8882a89a629b51f45cec180d99ef2bd5817
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://9ead3b3358eb65be15d3f66d7dda1ac4a6c6fe3b9fd2084f1a835a5693250206
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://9ead3b3358eb65be15d3f66d7dda1ac4a6c6fe3b9fd2084f1a835a5693250206
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-60
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:25Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:12Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:12Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://e2711a84872c4049255cbc259727f342aa5bbbeab408075ba258c3ecf5b803b8
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:11Z"
          hostIP: **********6
          hostIPs:
          - ip: **********6
          initContainerStatuses:
          - containerID: containerd://6480b5af5e02cda9b8add0738a79c4a52f65f21ed1a3847598a0fe06d890b32d
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://af563a518c29d0a17427dd549154742ab19a8072e1211f663f6cf1340b6c1fdb
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://af563a518c29d0a17427dd549154742ab19a8072e1211f663f6cf1340b6c1fdb
                exitCode: 0
                finishedAt: "2025-07-09T10:44:34Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://84e33f66d86ab24e7608be89f03f19e9c5760cc9cf12cbe3fa750cace8d60d40
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://84e33f66d86ab24e7608be89f03f19e9c5760cc9cf12cbe3fa750cace8d60d40
                exitCode: 0
                finishedAt: "2025-07-09T10:45:05Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:35Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-61
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:28Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:44:59Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://0b2e1669e46fa2cf00d213d6bdc80575125e3c746610fa2f62983c66335055af
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:05Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://4a4137515c10f1baecdd76f95986e8800c94db2313f19806979f1065153a735a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:28Z"
          - containerID: containerd://f07a3a689f056f46aa2186b4a0608ea26264aeb2d6390cbb2b0a63343896f93a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://f07a3a689f056f46aa2186b4a0608ea26264aeb2d6390cbb2b0a63343896f93a
                exitCode: 0
                finishedAt: "2025-07-09T10:44:38Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:30Z"
          - containerID: containerd://3a735c03aeb49f7d987fa78ca1ee48fc5d93200c9ca2699d2d760bc0eddf24b1
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://3a735c03aeb49f7d987fa78ca1ee48fc5d93200c9ca2699d2d760bc0eddf24b1
                exitCode: 0
                finishedAt: "2025-07-09T10:44:59Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:39Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:25Z"
      - name: job-ku4uzh4unm-worker-62
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:25Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://3aa753f7e290d10ef34b5cbbef4a636d85372856b20fc9a654fa920ff75d2c84
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://02589326a15c8ec1fd5381806b38f1c0c2548c2c3e94077e9fa0e2255d5cfe87
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://353fe71e498c471742330cd4b91339f620b7fca529ecbfadfaa10f62699346f8
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://353fe71e498c471742330cd4b91339f620b7fca529ecbfadfaa10f62699346f8
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://b0c5c327a2e64b36fec68c50f18c849cad16352bab77b261d1db178994fdc5ae
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://b0c5c327a2e64b36fec68c50f18c849cad16352bab77b261d1db178994fdc5ae
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-63
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:05Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://95c032373f13fd34255d582d97d27303021f7ba41974d79cb2e2e0f1bc79c807
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:10Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://a2ebb86dfdd6958f5c727fa08359874033fb247466eed0e982ec19eb195161b0
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://d36a25d905b9716fbe3b8dbe0ff688eacef5749cd4e5fe86005cfa8362a62d45
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://d36a25d905b9716fbe3b8dbe0ff688eacef5749cd4e5fe86005cfa8362a62d45
                exitCode: 0
                finishedAt: "2025-07-09T10:44:33Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://c706b2822797b9b8e9879ba6a078ca3ed0f667f297eb18a703b07a1aaab5980c
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://c706b2822797b9b8e9879ba6a078ca3ed0f667f297eb18a703b07a1aaab5980c
                exitCode: 0
                finishedAt: "2025-07-09T10:45:04Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:34Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-64
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://c2b9524b63e5a553fbcc12431b88f3f93b3c75a2fdffc76a450ead0d09f73378
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ***********6
          hostIPs:
          - ip: ***********6
          initContainerStatuses:
          - containerID: containerd://bd789dc45af9c273279cc4704761c92388dc2c7779d31972d814185343c17fcb
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://7b3c9dabfad7835d78516a096d6ba6b1e1b3cc708589141913557d571eb1171f
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://7b3c9dabfad7835d78516a096d6ba6b1e1b3cc708589141913557d571eb1171f
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://d6a171dc498cb9abbf8b3376498eb8e9468c297b57b1bd9c983483f790f51068
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://d6a171dc498cb9abbf8b3376498eb8e9468c297b57b1bd9c983483f790f51068
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-65
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:00Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:05Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:05Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://d62d4250bbd5fedbff9bd2bc87ed7e9a60422ed06aec61966223dcc196775a9b
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:05Z"
          hostIP: ***********5
          hostIPs:
          - ip: ***********5
          initContainerStatuses:
          - containerID: containerd://b5a7ef28f71c41ec50550b0d1c2a5c97f94c6186f5a0c9e786e0817192a9b421
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://9b3c0f55d841051e823af67bef280d8f71046459241d4d7b0f83e4bdc7bae728
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://9b3c0f55d841051e823af67bef280d8f71046459241d4d7b0f83e4bdc7bae728
                exitCode: 0
                finishedAt: "2025-07-09T10:44:28Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://6584e179304bc4ce6b7c5c68c46e3df5bfcfdec0e842c426ecc45abf961d819b
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://6584e179304bc4ce6b7c5c68c46e3df5bfcfdec0e842c426ecc45abf961d819b
                exitCode: 0
                finishedAt: "2025-07-09T10:45:00Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:30Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-66
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:00Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://4bca5c76b685985a95dbcbf7874729cfb3534359e9fccc6fc654b23b6084c671
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:05Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://135062135923f3630374d583f7e8e9b0834d17dbe469134feef35879dc7f7398
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://2dc14ffbda9f958a51d1ffdd447a6e2ee14d9b470f24ff2ea114c8d4d2c55c4f
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://2dc14ffbda9f958a51d1ffdd447a6e2ee14d9b470f24ff2ea114c8d4d2c55c4f
                exitCode: 0
                finishedAt: "2025-07-09T10:44:29Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://40ed33718be264b6bede2c7c428aa6cb5206f9d4f491294c45899c4c6d906b25
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://40ed33718be264b6bede2c7c428aa6cb5206f9d4f491294c45899c4c6d906b25
                exitCode: 0
                finishedAt: "2025-07-09T10:44:59Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:29Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-67
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://094bfe439eba4b4992f35bd4247ee0e43e0772fce81cf9136a63ba14b57103e8
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:09Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://3e091c9bbc810e862261727218f466a5e7bba97d6208e15a4f1c4d4b26c7c2ee
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://2cab05c5405ffde8b5cae593b5c0e6dfb3f1f518d1ac4af88766001693dd48d7
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://2cab05c5405ffde8b5cae593b5c0e6dfb3f1f518d1ac4af88766001693dd48d7
                exitCode: 0
                finishedAt: "2025-07-09T10:44:33Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://61095eca3fe5783f72c1aebf1ec381bb4696541e0fe85e5e793b12414eca10ab
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://61095eca3fe5783f72c1aebf1ec381bb4696541e0fe85e5e793b12414eca10ab
                exitCode: 0
                finishedAt: "2025-07-09T10:45:04Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:34Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-68
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://5a345a335905f330684f27a573328ef3917178d110147735af588daf7da4228e
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://45c24d29ecbcee44604bbc1fa65d291efcdd5fcb5d2f1121cf6e4bcaea904b6c
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://96ab36e904ab8c1ddc946fdd78acd2890573b65aa76d09d79549c2d5e1d74fb5
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://96ab36e904ab8c1ddc946fdd78acd2890573b65aa76d09d79549c2d5e1d74fb5
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://c70e00f28faeee9d6e76be4eaa43ea34d7a7e227c01f718ba27c02db5bb806e4
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://c70e00f28faeee9d6e76be4eaa43ea34d7a7e227c01f718ba27c02db5bb806e4
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-69
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:01Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://5524b4cd38f0aa2e06907d7538bd972d7ddd24291b3af97ef5f056203c622f7d
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:06Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://10b4e5375ddf6429432ef1a0e745e81fc4a8fa563d65b801dee4de163b87e35b
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://db0f71fcc898c92f3661ff5200691fde8a76cf913b921c33d98807582b3fb6f5
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://db0f71fcc898c92f3661ff5200691fde8a76cf913b921c33d98807582b3fb6f5
                exitCode: 0
                finishedAt: "2025-07-09T10:44:29Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://884828a9757cde8225c805ede7ddcade24aa6421b00d4852c069f11a88a785d9
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://884828a9757cde8225c805ede7ddcade24aa6421b00d4852c069f11a88a785d9
                exitCode: 0
                finishedAt: "2025-07-09T10:45:00Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:30Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-70
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://34da8d7ef65c4fb59e6923b67cf32a1e6d8dc81c19b50be1626c33d5aabf12b5
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:09Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://72afc79945a92e14792bdddb2d65f5d5e53164e82f81667b69374c58d66482c7
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://38ac01608ce278a17fa402ea509f59d7d9f163b36f09165b5ac4ae43d4820629
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://38ac01608ce278a17fa402ea509f59d7d9f163b36f09165b5ac4ae43d4820629
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://4b4438d06585e1a5cb1cb63b87e2e64c66ff054bfa795dd7bae64e267dc860fb
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://4b4438d06585e1a5cb1cb63b87e2e64c66ff054bfa795dd7bae64e267dc860fb
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-71
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:25Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:12Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:12Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://4f2211ab00b6be9b667df8b2088f41886572aa93f8a9e0f7b6dc25ecd7d6c3aa
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:11Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://01d9d76f235cc9fcdbbd57861ec9395c9cbd518db3fa6c7756cfb7e1401cf558
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://2fb9d505911889538488a9ef761f34df0f2bdaada79d4d4f7c0cbaf74ce91564
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://2fb9d505911889538488a9ef761f34df0f2bdaada79d4d4f7c0cbaf74ce91564
                exitCode: 0
                finishedAt: "2025-07-09T10:44:34Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://a16f41f948b4d5dddb8ad9588950587da5dc623e23bd6e166509edebf3213b71
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://a16f41f948b4d5dddb8ad9588950587da5dc623e23bd6e166509edebf3213b71
                exitCode: 0
                finishedAt: "2025-07-09T10:45:05Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:35Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-72
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:14Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:14Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://881cb8ba2de4c5ac5fe7c54fdf22253bd1c69814c7b5da2779412c4e16f71b55
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:13Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://9b95af09cfe95021a8447d3a0a1848179b0ca8d3ce5db427757d61c83975eced
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://433b8f513e29129a0f216b53437ffe4bd5fbab007a35598ee9cd6c59808c25c9
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://433b8f513e29129a0f216b53437ffe4bd5fbab007a35598ee9cd6c59808c25c9
                exitCode: 0
                finishedAt: "2025-07-09T10:44:35Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://a01a820a7771f8812fdbf68ae2d30055443bed9881115c8fed4c478e9886ed88
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://a01a820a7771f8812fdbf68ae2d30055443bed9881115c8fed4c478e9886ed88
                exitCode: 0
                finishedAt: "2025-07-09T10:45:05Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:35Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-73
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:01Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://c84b8ed01cc31ddfd92060febc535ff25e46bd722d0e1392da367e14c65d17b7
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:07Z"
          hostIP: **********1
          hostIPs:
          - ip: **********1
          initContainerStatuses:
          - containerID: containerd://05815f5928ffc0913cd57736d6b2f515b50610852aa79744e000891f22b69470
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://c0dccf0f6a4294534e99e1894ada94ebb2dec587fb634312999ed3bb8693186d
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://c0dccf0f6a4294534e99e1894ada94ebb2dec587fb634312999ed3bb8693186d
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://5f6d30d89dc7f424502cebc14e17713e0b653f9119c7532e5c5d9dbd8cdb4ede
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://5f6d30d89dc7f424502cebc14e17713e0b653f9119c7532e5c5d9dbd8cdb4ede
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-74
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://9276b3672343f30f8737d413ce399ce382ba6a4b8721680a507bf4d9288ba14e
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://d308d7b34a9fddc419d33bfbc8b95f3b0d6a601d815f05488b11b132c4944793
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://a95e179e9790b0d7be4527e199072863f66b78d089f26a9607e64093c4b881ec
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://a95e179e9790b0d7be4527e199072863f66b78d089f26a9607e64093c4b881ec
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://437afb3c39f856d8abbe4b28d436290d9fa0796a2a09f3bb320d37ee46c0bcae
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://437afb3c39f856d8abbe4b28d436290d9fa0796a2a09f3bb320d37ee46c0bcae
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-75
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:12Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:12Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://f48135d37d6e155618cb8d976f41bd4be5445c97cdfc99373253359b9e2ea9fc
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:11Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://a502b0a8fad596d8bebf3d5489042513aa8ae1ce9e5c04290d907e54ab8f2f3a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://8ead15635f4647ef6a7ddd65c97272821925d3230e901fa55618bb5d2c04359d
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://8ead15635f4647ef6a7ddd65c97272821925d3230e901fa55618bb5d2c04359d
                exitCode: 0
                finishedAt: "2025-07-09T10:44:34Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://ba8ee386b985ace42a99370475f7b11c647b91fe1ed81ad6325eadb6a53502b8
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://ba8ee386b985ace42a99370475f7b11c647b91fe1ed81ad6325eadb6a53502b8
                exitCode: 0
                finishedAt: "2025-07-09T10:45:05Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:35Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-76
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://321a8159c1d0723d59f59b06bd26849fc7f9ecfba052c4978f43471734ecc460
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:07Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://557097e3eacd8fd2b8cf0096445a5a1e74fa8c095c8a979f4bdb8e43cb6b90ef
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://f5ffbe532e334124e7afe60bf279c266693d869d8efa879b84386a4ae234254a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://f5ffbe532e334124e7afe60bf279c266693d869d8efa879b84386a4ae234254a
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://23c9131376daf64e8dde52a773e7df9e90e788bf11abe441995c1ba082f603cf
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://23c9131376daf64e8dde52a773e7df9e90e788bf11abe441995c1ba082f603cf
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-77
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://f1450894e7fc506ae75d7f8203adabef13412d48d18bf0ff807d11c691817820
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:09Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://6ad59deb483e4ba2442d6d5fdf9b1d6ee7458aa38844ac31e24dc3265825f7a6
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://2c9f920776ab3e0e2ac6e8baddb44d64fb91fd216c6aab3fb3dc19da6ccd6d17
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://2c9f920776ab3e0e2ac6e8baddb44d64fb91fd216c6aab3fb3dc19da6ccd6d17
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://3f4ee8587181ce3c58f1d26b3a206a957e5b46c9d31c730c4c8f67498c07c13e
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://3f4ee8587181ce3c58f1d26b3a206a957e5b46c9d31c730c4c8f67498c07c13e
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-78
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://b39a822d859fe628031e148f10a63349086da45c934ad0daf99ac83949edbc95
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:09Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://2cf7e8fb3a042c7d88dfe3a1308ea97e73918aafaeb81075fabffaf47c1464da
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://533775bb19a89b0df0ec5dd7488e8eecfccd51b0ef9fc662491e10b56223cb59
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://533775bb19a89b0df0ec5dd7488e8eecfccd51b0ef9fc662491e10b56223cb59
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://f01d95bdbb2d1ef38a2dd4e90640a88a514e0a450c34ef5fb8d0ad7e5ce50b06
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://f01d95bdbb2d1ef38a2dd4e90640a88a514e0a450c34ef5fb8d0ad7e5ce50b06
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-79
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:44:58Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://48001179753bade726b225ea69e41881111e97f40f64ea920ec8e0307d9a33fb
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:03Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://81cf90848f76ff779ad55c6e672cfcbf7dc8bbca949fb6090c08f01c8c0f4e21
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://ff0c5817f75808d45f4e96043d9ac908c9f90f7510bdae93fb327bafe4d4df11
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://ff0c5817f75808d45f4e96043d9ac908c9f90f7510bdae93fb327bafe4d4df11
                exitCode: 0
                finishedAt: "2025-07-09T10:44:36Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://0efc0f0ee2bf320aa530c46a4c956dc8999e416431957e6d0d33dd291fce1e06
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0efc0f0ee2bf320aa530c46a4c956dc8999e416431957e6d0d33dd291fce1e06
                exitCode: 0
                finishedAt: "2025-07-09T10:44:57Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:37Z"
          phase: Running
          podIP: ************
          podIPs:
          - ip: ************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-80
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:05Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://8e159fb673f83e790398f03183890ac8329e9be5cb5e6b348011863ff859759e
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:11Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://06062a2e070282ab8151b9739436f47490cf3963f8177911c4ae7933e253ceb0
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://96cb8ad0652915eae61f6b4067a5d6bc67a6834ac296c77d39d15c5a3e209d2b
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://96cb8ad0652915eae61f6b4067a5d6bc67a6834ac296c77d39d15c5a3e209d2b
                exitCode: 0
                finishedAt: "2025-07-09T10:44:34Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://661414d28efc2b03dbbe0b4b58ee23659795faadbd07555ac5be9a97e3450dd3
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://661414d28efc2b03dbbe0b4b58ee23659795faadbd07555ac5be9a97e3450dd3
                exitCode: 0
                finishedAt: "2025-07-09T10:45:04Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:34Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-81
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://852539eb3c1eaa3eeadb39302f3679c5c91d17150cc5b6a48737ce7707028044
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:07Z"
          hostIP: **********8
          hostIPs:
          - ip: **********8
          initContainerStatuses:
          - containerID: containerd://c8be29eafce1258f0d350a9abd03bbc17c7b3430292eae9dae0750658d310907
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://1005be4ccf7669df272f51672483029eb5e181f938b4d226070751079d63d7a5
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://1005be4ccf7669df272f51672483029eb5e181f938b4d226070751079d63d7a5
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://0804b1f32e3ea570670b244992c3fad31d295dce329e12fc934cd2ccb4c2a805
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0804b1f32e3ea570670b244992c3fad31d295dce329e12fc934cd2ccb4c2a805
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-82
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://80d31959f98339166f7e61b267a0a9bd382044b798a9a11da15905a6232181ba
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:07Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://e81af0c033d23e4cd816f33814909912923ce2078278a4e47600019371b1bfc2
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://1a4fd415838d552a91841a5242b78e888973b4eb2c71a343951c02739ce63b2f
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://1a4fd415838d552a91841a5242b78e888973b4eb2c71a343951c02739ce63b2f
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://50cf5a1a02ea7992f612b66e505f58587a64e234f9576b7dc0c4a649779accbe
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://50cf5a1a02ea7992f612b66e505f58587a64e234f9576b7dc0c4a649779accbe
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-83
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:25Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:16Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:16Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://8f7f93bae01cdb32bb191e01b4000062bd3d47da1d5649c6f3d80f299a1a7ef6
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:15Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://ea2256dd2d61db888fc301a7a2caec24e4b82dd99e7e1ac36bb51e0dd76a224d
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://ea27d441a74a9f11ffb4c8e7704ce333f4fd1567055429d717d30c56c0e1641e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://ea27d441a74a9f11ffb4c8e7704ce333f4fd1567055429d717d30c56c0e1641e
                exitCode: 0
                finishedAt: "2025-07-09T10:44:35Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://132f0a0f12e1db227ddba3d5a78202b633bcfe540b1f6b74a5bf881a49633a23
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://132f0a0f12e1db227ddba3d5a78202b633bcfe540b1f6b74a5bf881a49633a23
                exitCode: 0
                finishedAt: "2025-07-09T10:45:06Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:36Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:22Z"
      - name: job-ku4uzh4unm-worker-84
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:01Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://2f78675037035d4247c99329d930502affa0801e1d65c4fac2394c6f763333da
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:06Z"
          hostIP: **********6
          hostIPs:
          - ip: **********6
          initContainerStatuses:
          - containerID: containerd://901354605a978214c81df51a0f5cc090c9664884f2fda93d3fa17608cabda96d
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://af8e1712145d8c972e136746b1ef19f8d4c80500cef29e42b852348e9a21abf1
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://af8e1712145d8c972e136746b1ef19f8d4c80500cef29e42b852348e9a21abf1
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://2e308580b178647b7a07d13258c50d4916471c21c911967b5d27ffb104ef962a
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://2e308580b178647b7a07d13258c50d4916471c21c911967b5d27ffb104ef962a
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-85
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://cc4c98636d70deb96d6c578b84072dfee42609d83b9498667503427528776094
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:07Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://636dc4f24614af4d4da4b3a800dadac31fd512135f59383223fd4715878a5c63
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://acb345d62bb0b1165e94f0d97093683d8cfce085885c18d0ed6f8e704312bf1a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://acb345d62bb0b1165e94f0d97093683d8cfce085885c18d0ed6f8e704312bf1a
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://7279d2ffb86b0a1128143d872c94d6e0986f31e27487668834e40dca9e67f6cc
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://7279d2ffb86b0a1128143d872c94d6e0986f31e27487668834e40dca9e67f6cc
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-86
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://63a8dbfcebbefe4760fef63b16592e2207114456bb0d23dc2da383b18a24fc54
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://57938eb5f9c0492b1de063ab00c8e032ddbce1a89e4639d2fa42fdc80e23a8df
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://a88559647aa56a9221baf8a09084c95ced177b8befc92153a867f51b7ae71dd5
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://a88559647aa56a9221baf8a09084c95ced177b8befc92153a867f51b7ae71dd5
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://873233ea07a51151a3eb22d10c85127a1aad00e016e1b5a9cfb7b860d6baa2cb
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://873233ea07a51151a3eb22d10c85127a1aad00e016e1b5a9cfb7b860d6baa2cb
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-87
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://292f8b1ab7bef23ac75dbcc091c9cf8fbd51d8024e6a4bf94b057eae2051f076
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ***********0
          hostIPs:
          - ip: ***********0
          initContainerStatuses:
          - containerID: containerd://d1d07c941d8df5d93747a5d6108ad63770c7300031b55917be0c1536c5928918
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://321200ea2c00da09e59ddceb4c20c7235b3987cdc453f3400036af1b29aae028
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://321200ea2c00da09e59ddceb4c20c7235b3987cdc453f3400036af1b29aae028
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://b93d5e63c754c47d989abd253f63b6317d2ff20d87f3b4d49b1d546955435cfc
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://b93d5e63c754c47d989abd253f63b6317d2ff20d87f3b4d49b1d546955435cfc
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-88
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://2967ba10cbfd3f8ec3d2b9b6a906b8a25e91027582ddc94772dd6f5dab4b6511
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://2bd190f502955bf8872cb4aa29e805693a2ed5f5aeb7a58465162d80b2acbef0
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://e1921ebb667d15fe3932f360bca1643df885154f918e23fd968fd37411e002b9
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://e1921ebb667d15fe3932f360bca1643df885154f918e23fd968fd37411e002b9
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://c26911948340f2d372055dc77ccb7f3f037b48b12de2bdb23b24955489be6450
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://c26911948340f2d372055dc77ccb7f3f037b48b12de2bdb23b24955489be6450
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-89
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://a27526ff06353b04026c5bf9f3edcb6b9ead05ce6609657de34eb7eb5b8dd8c8
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://76348a00f4ef68e568b6f942199017dd74edfe717a62388eec492e3a8a21b91a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://55d3c5a2ced4da8c1e5e4b4cacb57029d9e2b3c6a28a6e60835091707837bbed
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://55d3c5a2ced4da8c1e5e4b4cacb57029d9e2b3c6a28a6e60835091707837bbed
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://d9bff0512d960d11957f6fa050002d6b06aa49f4e96ec80c91a28265cee0b812
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://d9bff0512d960d11957f6fa050002d6b06aa49f4e96ec80c91a28265cee0b812
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-90
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://2fce42447bc9724eb784ed95974e9e0a644ab36c319acda35b417d84139a4c58
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:07Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://5785d45db65f86e126c1fc7c73a83bd522e1b7a1c9f28e75bcd3dfc85f56b7e3
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://7ebe4f2cc5e4ab1770b2525d54530e07aed57b3831019e97744d9c851fa2c751
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://7ebe4f2cc5e4ab1770b2525d54530e07aed57b3831019e97744d9c851fa2c751
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://e71d52bca56ebcbdd4dfbd3536ab17fce4eebaf790bbe21737211b99b2ebd02f
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://e71d52bca56ebcbdd4dfbd3536ab17fce4eebaf790bbe21737211b99b2ebd02f
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-91
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:01Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://c5ee514b1e1d5fe037dde5f90d409d1633f11b4747d7272afd019b7d68260c8d
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:06Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://801e8b436e95b6eeb51841e1efc497019f43014523a7f3ca88151a42e0329d2c
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://87c5682ff02138e5df0cc01f057392cfe4c77b62db0d52ccec3a2f1233319105
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://87c5682ff02138e5df0cc01f057392cfe4c77b62db0d52ccec3a2f1233319105
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://afad824f852cc6f8983b8f228c1456ca522651941b5718efabed58c3c26f6bdb
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://afad824f852cc6f8983b8f228c1456ca522651941b5718efabed58c3c26f6bdb
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-92
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://0ce343b8784c9dbca4ddae123a0f50d44c81f57d1e6bde5786a63fd4ccc50a5d
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:09Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://7115bd0f9e6f65e40be0df56c03cec5a72599d9a03961640f9eb5266d17f43a5
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://ff8c2c02e57ef6ab18947c834e508b965104492a7845ec6fdc0921bb46e6bc5d
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://ff8c2c02e57ef6ab18947c834e508b965104492a7845ec6fdc0921bb46e6bc5d
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://d1a1d48bd13416828a356beb20bbf102ff5828d979661fe18937627e348adbef
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://d1a1d48bd13416828a356beb20bbf102ff5828d979661fe18937627e348adbef
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-93
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://2abcbc7c7871282e4aeb7517d6b9ac52c4ab00fd54a8e3cd875519d2463fb966
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://77379aee5a09fc203e7314622396fea6e671e6e10f1d3e4c3187c35923413343
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://609c0085ad7a55a1566a27ad3d1530e850040d98c46e5051d6124d2b8416f628
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://609c0085ad7a55a1566a27ad3d1530e850040d98c46e5051d6124d2b8416f628
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://84b5c8864fe45ed260ce5d21e71bdfc8dd2b08ea316d86e07a58fdb3471afc6f
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://84b5c8864fe45ed260ce5d21e71bdfc8dd2b08ea316d86e07a58fdb3471afc6f
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-94
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://d7b3e73253afc528d4045815b93c42845df90d586f233c9b8cff9b71acd9b6cc
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:09Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://7806844acb3d64e9fee88d67d4aa2ce0176bbd2ec8dd3b66fb5046ca33db5000
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://0b8dd516c40a7e571ece2631f9146e584a78472d06350da5d48f8430af6e6415
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0b8dd516c40a7e571ece2631f9146e584a78472d06350da5d48f8430af6e6415
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://dbae158895f1f9642048c74bc15008aca8b85c76177922e30cf5c33e79b4b3cc
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://dbae158895f1f9642048c74bc15008aca8b85c76177922e30cf5c33e79b4b3cc
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-95
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:01Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://f0bdc4a7066cdaf377c12dda5fd9dee1449252abacfeb2b12b57f20b2dd588b9
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://40cc5a24b9807dd9849b661e9cabd6166e8949a50f6768ce149881c749834b6e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://fec140d1f30ba6f06224e64353ad98e72586dfb81582903ee602f763897d14af
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://fec140d1f30ba6f06224e64353ad98e72586dfb81582903ee602f763897d14af
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://7dfac433dfc77f90f78c00a62bf826a9398aa164baf33414b457fd3f15b16c58
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://7dfac433dfc77f90f78c00a62bf826a9398aa164baf33414b457fd3f15b16c58
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-96
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:05Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://5622149f7dc6ab0c9d6d3826293aab87e5d0f0a18596d05fdd7c98a18649ae86
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:11Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://e6bf6eb0d024a2afe78f393e9b5551ee7dccb41976449648eb4f9cbd4b98e23b
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://c7f0d4b1f85a6a4200150a1b3a9b591536cff61e700365802092c604016d4c02
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://c7f0d4b1f85a6a4200150a1b3a9b591536cff61e700365802092c604016d4c02
                exitCode: 0
                finishedAt: "2025-07-09T10:44:33Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://1e8eaba6cd948c3bc2f730ef49a1c47dd68773ffba4d7549a26f523b350013de
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://1e8eaba6cd948c3bc2f730ef49a1c47dd68773ffba4d7549a26f523b350013de
                exitCode: 0
                finishedAt: "2025-07-09T10:45:05Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:35Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-97
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:13Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:13Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://90ce6101a450bc615e580f6a52858095a79a76766e42aefe16a85856353b705a
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:12Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://1e3120dbf8db51243e87e48d6794f3d81d08382a41d472788075d7db5df92efc
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://635d4703c9b5b19b09f95f69ad022fc3cbf4a1fe0737c81b8dc2cfa486e3adc2
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://635d4703c9b5b19b09f95f69ad022fc3cbf4a1fe0737c81b8dc2cfa486e3adc2
                exitCode: 0
                finishedAt: "2025-07-09T10:44:35Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://01e576e82f962b39516d5746af9d1ca84f90f31171e6d417c5028b482e35c5b7
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://01e576e82f962b39516d5746af9d1ca84f90f31171e6d417c5028b482e35c5b7
                exitCode: 0
                finishedAt: "2025-07-09T10:45:06Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:36Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-98
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:25Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:01Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://03b19e552fefb2a81193b7ff792f79adb151ae45976e94c546efa55afc8ab45c
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:07Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://23a08aa41d4505a440ed702e02f2af129e5542b0c4ba27a6d1ca5930ece6d0a2
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://0e301fdf7d89d0a95024ba126728b515b0a7cbb3b9a249490ef0f92aa8391e68
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0e301fdf7d89d0a95024ba126728b515b0a7cbb3b9a249490ef0f92aa8391e68
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://b7b6e724cec50f38f23472708ec13eea166a5e5ffe44a12cab5743c8a824c64f
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://b7b6e724cec50f38f23472708ec13eea166a5e5ffe44a12cab5743c8a824c64f
                exitCode: 0
                finishedAt: "2025-07-09T10:45:00Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:30Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-99
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://2d677920a0df97c17b42950bfeed07e15995f95c7ba02e8c3463f67e69cae031
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:09Z"
          hostIP: **********7
          hostIPs:
          - ip: **********7
          initContainerStatuses:
          - containerID: containerd://e55e50081a0626ec6c4e89e0a07a99a16bbaf06abf0a0ecc62a97aa3c5963877
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://372d7a9c11b3949a693b0d36cc554eeccef6eeaa7fa692a3669b7968bbdfc888
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://372d7a9c11b3949a693b0d36cc554eeccef6eeaa7fa692a3669b7968bbdfc888
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://005d34c8cac9f96c1cac54a31364d9d3ded1219a1e86e676d18e1407ba88dc7d
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://005d34c8cac9f96c1cac54a31364d9d3ded1219a1e86e676d18e1407ba88dc7d
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-100
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://d475ee0fe0bd2d1b4732d21b127a8ca780b49240619bf5a530a66429e4377ea0
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:09Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://3f5dc87324d03dd8d6d8ea292ffd7a54a2293e500cc7b3c87da6f9242d22b990
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://69879cf39f1c93237710a293122dde2b2fab9837697eb4c8a0fb545b7a44c4e2
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://69879cf39f1c93237710a293122dde2b2fab9837697eb4c8a0fb545b7a44c4e2
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://62e031934e13c6b854b9c09ab783f9f02ae7ee8ba18482d8188aa27984371584
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://62e031934e13c6b854b9c09ab783f9f02ae7ee8ba18482d8188aa27984371584
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-101
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://7ec146fb0189f65ac495623ee737c2c234cfc7fb864ad9c4204d4c357112ce13
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:09Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://a2ed64edc6233e03411ed90528d46b1e030438e096070b272f1afe3c0e12641c
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://ed7494dedc60423709571ee50b55719ff043b8efba7d0365b506a4c1edf0cb42
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://ed7494dedc60423709571ee50b55719ff043b8efba7d0365b506a4c1edf0cb42
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://6ddf5fd836343295fec53b05ec13c6a6631dc8dd49a552004afa41b88759dc78
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://6ddf5fd836343295fec53b05ec13c6a6631dc8dd49a552004afa41b88759dc78
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-102
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://9e21ac4f44813076cafeb1ce5625df0e64afd455c3259b8dcd83ac1a7633648d
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:09Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://ff69c1509dd3a55254ebad589703397f6581bd354cc5b3b0af5233203eb6997e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://cc368d0493f16adeb2dc1e6d646f8aa1e37b70772610a717e5524a8844f7bd36
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://cc368d0493f16adeb2dc1e6d646f8aa1e37b70772610a717e5524a8844f7bd36
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://5a9fd3c904c46b333759052b99719182f6e71fb60322c2dd7301641aaa49b390
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://5a9fd3c904c46b333759052b99719182f6e71fb60322c2dd7301641aaa49b390
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-103
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://4a50ecc29231aeace5ddaad9f24e37f265a07e464b12cc317bc8e1a6dfc065f5
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://5a9ff0d1780ebd3e4f9342c92b7a5d85727ebb60f2c85c749d26b1ba72c4042e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://3c5b5276d2665f6de9f80b94607b2260013b60615c6c76e55c30aedf8e977275
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://3c5b5276d2665f6de9f80b94607b2260013b60615c6c76e55c30aedf8e977275
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://8a43aae8185bb03eee062e50ff66ac2dbe5a54444a1d7011b793c14037c01bb0
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://8a43aae8185bb03eee062e50ff66ac2dbe5a54444a1d7011b793c14037c01bb0
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-104
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:44:59Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:05Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:05Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://d43a1a5f685e01a4efb119641c008e1c06cf5cac02c69947fadd179b4d164d00
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:04Z"
          hostIP: **********
          hostIPs:
          - ip: **********
          initContainerStatuses:
          - containerID: containerd://f5a04e3ae68ef81c23724fc1e7ce8c1bec52a7f2cbfa924f331e4bc4f3ddf628
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://0d1902cf9dd86fd3e2d5149ea7c8bc0f819e225e80eef557645241e1609a559e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0d1902cf9dd86fd3e2d5149ea7c8bc0f819e225e80eef557645241e1609a559e
                exitCode: 0
                finishedAt: "2025-07-09T10:44:28Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://9a7ad91fbf25a0afb9d2f692f35de2dfe0a0684b1b832995831badc4eae3b317
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://9a7ad91fbf25a0afb9d2f692f35de2dfe0a0684b1b832995831badc4eae3b317
                exitCode: 0
                finishedAt: "2025-07-09T10:44:58Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:28Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-105
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:05Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://68615be2ac4ed410d0fedec828174286d1bc56b44aa9f746184c7d0c988598b4
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:10Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://45e82b12c4608abc77209c887e635191623318381ac01343249bed434575ffdc
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://e9bc754cefd881fc74190831e8f674194dcafaad43e61e034ed0e1db6e60b4fb
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://e9bc754cefd881fc74190831e8f674194dcafaad43e61e034ed0e1db6e60b4fb
                exitCode: 0
                finishedAt: "2025-07-09T10:44:33Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://be2b32ccb426117936af6da85ed8b3e2c40d635f756f6b3d0af8edd7134f5f56
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://be2b32ccb426117936af6da85ed8b3e2c40d635f756f6b3d0af8edd7134f5f56
                exitCode: 0
                finishedAt: "2025-07-09T10:45:04Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:34Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-106
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://29352830efbe95ff460e7d40d913307f334241f1f7bf2bc976fb0e26b14ab66f
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:07Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://fd5390d4569ba10ae33633955030a192a2b1d427323ec2ef81571e0abf01d5af
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://1caae278ff56a144ecdfbf70eae80e06165c3c5b6a51d680ff09fb2634d6811d
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://1caae278ff56a144ecdfbf70eae80e06165c3c5b6a51d680ff09fb2634d6811d
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://7f4def1ba911cb55fdb863e6d3f525c64cbd802a888a25344f9447716d820321
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://7f4def1ba911cb55fdb863e6d3f525c64cbd802a888a25344f9447716d820321
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-107
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://fa0a0eae551b448e38c253a8a4ce890cb225b982fd2a75639471e615c71b3968
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:11Z"
          hostIP: ***********2
          hostIPs:
          - ip: ***********2
          initContainerStatuses:
          - containerID: containerd://26ea997c08d0670fbc4b12e1c4b944ef187f7e846a953b90a94785c6d53a20d3
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://964440045b8be719936e77443a6bf25394123bbcf031ae14c29f213216b8e7b8
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://964440045b8be719936e77443a6bf25394123bbcf031ae14c29f213216b8e7b8
                exitCode: 0
                finishedAt: "2025-07-09T10:44:34Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://4c4a14fbdbf0cc770be3aa955cda63e6aa9b84ff518701417f13243878156c38
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://4c4a14fbdbf0cc770be3aa955cda63e6aa9b84ff518701417f13243878156c38
                exitCode: 0
                finishedAt: "2025-07-09T10:45:06Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:36Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-108
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://61f6ee952f675f988e80cdcb73f03bf054416fc318289e81a7b79cf4bcb6691d
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: **********3
          hostIPs:
          - ip: **********3
          initContainerStatuses:
          - containerID: containerd://bd726cf3451e5eb449645ed7b6bf772c50f1a689e0fa2d4791212b180c8b6b72
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://9803ee0f2c93b07adbd96f18955c11e0935e91cf2354af9adbeee8380017abc4
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://9803ee0f2c93b07adbd96f18955c11e0935e91cf2354af9adbeee8380017abc4
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://f86063452dbd88a262514f7f5236b76cf250ac246f65b4db94dcd88cb0cb3f79
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://f86063452dbd88a262514f7f5236b76cf250ac246f65b4db94dcd88cb0cb3f79
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-109
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:25Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://4e08caae83b1a1d3607c20e57539d24024f0ee3647e164e19cca3129fd183403
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:07Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://1e40199c7bd97f24cd3c100472217f0252e61db72b72be95413b6b8e6449f6d8
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://4d597b7e8237702b83b51ad5d9fb7abadea76d7a7afa5ed318eae1ed0d4e31b7
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://4d597b7e8237702b83b51ad5d9fb7abadea76d7a7afa5ed318eae1ed0d4e31b7
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://77f89fb6b8ae4e075d3459b3489140668debd2dcc8c5f5d724dd4f75fd2c926b
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://77f89fb6b8ae4e075d3459b3489140668debd2dcc8c5f5d724dd4f75fd2c926b
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: ************
          podIPs:
          - ip: ************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-110
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:05Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:11Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://af694f344fe5da9d4490ace336495544cdcc599b6d1048f42abdb842493bc598
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:10Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://f74a05e36e74afb4271ea0430b0733f31a7f4e82e2d3f6c413d8941eac1d92ac
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://54821ce3e4e5b5ef45e558abbbfbf2d5bebfe69b772e3eab35c727f1b5e9bd6b
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://54821ce3e4e5b5ef45e558abbbfbf2d5bebfe69b772e3eab35c727f1b5e9bd6b
                exitCode: 0
                finishedAt: "2025-07-09T10:44:34Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://11376e9e43e437dc472da5451f67bdef6ee805437ce86b6b9bcd46a2082f8113
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://11376e9e43e437dc472da5451f67bdef6ee805437ce86b6b9bcd46a2082f8113
                exitCode: 0
                finishedAt: "2025-07-09T10:45:04Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:34Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-111
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:01Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://e52a73921f74a3adafbefce2d4a4fb3b195bfd0871b4f8d0facbdfb30c7779d9
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:06Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://1067bb023fc615ed491c85553f5872efd138aaa79202723b733144eeab59e72c
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://24e755ab77b2760e5d12db7c18c62f1061da1f6519ad7c97721081ca699d2357
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://24e755ab77b2760e5d12db7c18c62f1061da1f6519ad7c97721081ca699d2357
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://50c37708392722100455da56b63512b093d2f33254de12ec6413a5a814e60b76
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://50c37708392722100455da56b63512b093d2f33254de12ec6413a5a814e60b76
                exitCode: 0
                finishedAt: "2025-07-09T10:45:00Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:30Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-112
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:13Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:13Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://b2048665eda7c8b2030f5609949893ce6f7efcac1bd1f56e42e3ccadd2c33e53
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:12Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://f691052f23499caf59223b159bb807abb86cf316f6618fb2cfca525fd618bd05
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://a6aed24a735c7387e9e59a6cca28f83d25863050418282593f4e0bc2eb90094e
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://a6aed24a735c7387e9e59a6cca28f83d25863050418282593f4e0bc2eb90094e
                exitCode: 0
                finishedAt: "2025-07-09T10:44:34Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://a0d355dd48156face9bf9405b5b6daa724e530163c5df6ca77a10fdb98948ad5
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://a0d355dd48156face9bf9405b5b6daa724e530163c5df6ca77a10fdb98948ad5
                exitCode: 0
                finishedAt: "2025-07-09T10:45:06Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:36Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-113
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://d9368d9916807ca47d60ca32e9fd3278dc36e68c1519d8b9f80f468d40dc2124
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ***********3
          hostIPs:
          - ip: ***********3
          initContainerStatuses:
          - containerID: containerd://6cbf86e96ceb79275134726d3431111e9455c2353024b46115908107e19f0ffa
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://f97bacdd71949063512b4bd1390c91318a522d414d19f779c74c0221b131ba10
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://f97bacdd71949063512b4bd1390c91318a522d414d19f779c74c0221b131ba10
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://6e728ee83402c98cc01482753070a017f032b41536b70bc4519ddd203c57428f
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://6e728ee83402c98cc01482753070a017f032b41536b70bc4519ddd203c57428f
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-114
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://965c2819213b218936a5e171f69fa176bd0292b4e959ad058b761821e18a345b
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:09Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://b11a37344f6ba03e943a8949edc7de34368813829349ec01672b00c0112a146b
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://58b1aeace0da4cff2deccbab7bc5a4ccd5a351c7f310be4d6f5fbc6047012a4d
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://58b1aeace0da4cff2deccbab7bc5a4ccd5a351c7f310be4d6f5fbc6047012a4d
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://a3ee2c68cad4feb56b3aebdf69c5cb3374f019d60e2bff7b1c99d891d3c8e71d
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://a3ee2c68cad4feb56b3aebdf69c5cb3374f019d60e2bff7b1c99d891d3c8e71d
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-115
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:01Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://bdcaf85eee2a7903bc6e2ab1824cdf6c967ee5496b22ad5eee6e1158c250b1cc
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:06Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://b89f32effd4503357a644d0f3033f141265ae463984588bb691ab2b630e05d8d
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://dc83585a3aaebb439f979aa5b26c4af74c46e17d10dd186039b7ff56f4b85c10
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://dc83585a3aaebb439f979aa5b26c4af74c46e17d10dd186039b7ff56f4b85c10
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://32fbb3509d6776123919ddc67be094e9eeae28479edd49fba3c6205fa3d513bb
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://32fbb3509d6776123919ddc67be094e9eeae28479edd49fba3c6205fa3d513bb
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-116
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://784e985cc83a158046cc50a4acb6336d0a1d13c176f4778eb8216438fb492e83
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://1f3771e34f75797cab5d9adb19c786942e272e507453b55f399c46aeb2564520
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://0fed5e89c6ae620e0b570fe9221012269231b725a775ccbd806bf9db71a8be21
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0fed5e89c6ae620e0b570fe9221012269231b725a775ccbd806bf9db71a8be21
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://0609f745a343c6f77166065321abea78fad2c782a9a772276e832e868d277a6a
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0609f745a343c6f77166065321abea78fad2c782a9a772276e832e868d277a6a
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-117
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:25Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://a23ddcf25e4b11c1b3cb2ef47858f2f96de15439cc6966cb52cc546c9dc7a407
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://dcd18e7325a5ca6d044985cdf34aef15c3154764c536c337a23f2d858b7b2def
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://3e4f2eb9af29db66e6683ef4677e88a206acb6cc20b0cbfcbfeffc207c9119b6
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://3e4f2eb9af29db66e6683ef4677e88a206acb6cc20b0cbfcbfeffc207c9119b6
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://c73f44ee19d760b1c4e3ce5089dceb520efc6df3f1d69b306d552f705850779e
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://c73f44ee19d760b1c4e3ce5089dceb520efc6df3f1d69b306d552f705850779e
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-118
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:01Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://d6532f54f7e7ace694d6f8d28f38803ac5696affcac49d9ad6a8b31be3f33d15
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:07Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://06d19bc5f76890df0379da3386795b58b876e07accb828457f738a8f2fcc5bc7
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://3455cd93d2ca7a2c2813890048b6c59f693ccb113fc8192dfcf26a4290a3158d
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://3455cd93d2ca7a2c2813890048b6c59f693ccb113fc8192dfcf26a4290a3158d
                exitCode: 0
                finishedAt: "2025-07-09T10:44:29Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://85b0d904e99ffe0c910a3d46d40e045c1d07a0158193340537a4e09bd9bca6f3
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://85b0d904e99ffe0c910a3d46d40e045c1d07a0158193340537a4e09bd9bca6f3
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-119
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://8f97a52f687211a3aad08cdab9cfb7916851b33aeb01dca181098f35f77dc5d3
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:10Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://f0bc42af1db6a42bb4ca80ad92d289bdf2a866e266bf374a2df45efa2ac03957
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://c0c9f92cb659f2fd66e6bebe9813b817b6b0d503b59a99d673a1bb494e59f6c7
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://c0c9f92cb659f2fd66e6bebe9813b817b6b0d503b59a99d673a1bb494e59f6c7
                exitCode: 0
                finishedAt: "2025-07-09T10:44:33Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://71dfeac80af3d8ac4fa277296c158797336d3199751090fe4e2badc5d8be7440
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://71dfeac80af3d8ac4fa277296c158797336d3199751090fe4e2badc5d8be7440
                exitCode: 0
                finishedAt: "2025-07-09T10:45:04Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:34Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-120
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://e37fc15094822727f42cd4b430c5aaa1d275038906e76ba9490f5ca904e7dd43
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:07Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://5a6cd3855cbf5cac3e7f1126babce3054921e2300d2e193707a511c6e20bb227
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://83e8fab5ceb3a4fd9c61a220c25b4030546b6a166d9277cdba7b20a88bbb1af7
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://83e8fab5ceb3a4fd9c61a220c25b4030546b6a166d9277cdba7b20a88bbb1af7
                exitCode: 0
                finishedAt: "2025-07-09T10:44:31Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://9ce48e32ed43205badb6801e1f40809ccdc06fe78447533d2ea6996878510686
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://9ce48e32ed43205badb6801e1f40809ccdc06fe78447533d2ea6996878510686
                exitCode: 0
                finishedAt: "2025-07-09T10:45:02Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:32Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-121
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:02Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:08Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://9058630506ac4f68a9df7a2f90317a11ab5afd93726024980bcc95ff46f06722
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:08Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://a81e6dbec0f5efbf9082667a078451c901a91a8639814375c2921779de0fb8b3
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://98f4251a3df02d6ec75b81138fac9b0ca8cec63d1d46ce5be711bb4544be40c3
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://98f4251a3df02d6ec75b81138fac9b0ca8cec63d1d46ce5be711bb4544be40c3
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://518c3a9ceaea5d771d72c4cd50b132b5de4bdbe9311988101f297697ba1a3a89
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://518c3a9ceaea5d771d72c4cd50b132b5de4bdbe9311988101f297697ba1a3a89
                exitCode: 0
                finishedAt: "2025-07-09T10:45:01Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:31Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-122
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:26Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:00Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://7363515c76fc7201f6a61682e4a90263f96e3f7f2a0e467a0de881dfba7c75e2
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:05Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://7ad585d2ceb1059e162260a3bbd5873b9c0a1b26e8a030cc9488dedbd4fd1421
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://c0e287ce9245d90990f0a4c6b70e9a6a489651f258a3e63ae4d63516189816d6
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://c0e287ce9245d90990f0a4c6b70e9a6a489651f258a3e63ae4d63516189816d6
                exitCode: 0
                finishedAt: "2025-07-09T10:44:38Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:27Z"
          - containerID: containerd://ac82ceb6cbb3cfa03c00b24b28ac6b1023bc664b3b38690e36309fd5cef8292d
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://ac82ceb6cbb3cfa03c00b24b28ac6b1023bc664b3b38690e36309fd5cef8292d
                exitCode: 0
                finishedAt: "2025-07-09T10:44:59Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:39Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:23Z"
      - name: job-ku4uzh4unm-worker-123
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:03Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:09Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://61119031a9e06bef16a961a6417a68cfefaa1384d41b990899648f69c1a6d0d2
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:09Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://0d7e8ad8a62b953f67413b8f3f22e85c4ebdda4704a1c63c560c9e7945ca567b
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://9ef7d6bd3de234f1423b499843c5acd9f9d8e19ba23c4dc97470a2d01affac3a
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://9ef7d6bd3de234f1423b499843c5acd9f9d8e19ba23c4dc97470a2d01affac3a
                exitCode: 0
                finishedAt: "2025-07-09T10:44:32Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:26Z"
          - containerID: containerd://a3552be50a9941bf22e8a8baaa79666bf85c090b132b9f6c99a167baa5892fd7
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://a3552be50a9941bf22e8a8baaa79666bf85c090b132b9f6c99a167baa5892fd7
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:21Z"
      - name: job-ku4uzh4unm-worker-124
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:01Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:06Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://c22bc8aab36c5b27c642dd8cb415c1fee95672f504fcffd9c0abddb474f574f4
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:06Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://e781288fdfc5f29496c441ef1dd326e7f29964e6941ae1df426dc6ffe576d241
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://0753c6efab1ea9e140292cd0091795ff3d914c7bc9ee3dcd9c05d8ab5e40d29c
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://0753c6efab1ea9e140292cd0091795ff3d914c7bc9ee3dcd9c05d8ab5e40d29c
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://7de2037aee656495a3f433d9ddc98b6149a95938b03955af9a71a3cb863f2e54
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://7de2037aee656495a3f433d9ddc98b6149a95938b03955af9a71a3cb863f2e54
                exitCode: 0
                finishedAt: "2025-07-09T10:45:00Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:30Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-125
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:24Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:04Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:10Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://9dd9c6e4ff00b63f0cdd42442a54ce2346537aca4380fa216909543fb56696d7
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:10Z"
          hostIP: ***********
          hostIPs:
          - ip: ***********
          initContainerStatuses:
          - containerID: containerd://899faa9974b24048835e06a1e3dee4a6f935cc90005e1373410ac47deaf445f0
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://e928d2ae3036b83d4c618c26dff85857808f5f3ac40a7ba57a702f42ded8c1bb
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://e928d2ae3036b83d4c618c26dff85857808f5f3ac40a7ba57a702f42ded8c1bb
                exitCode: 0
                finishedAt: "2025-07-09T10:44:33Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:25Z"
          - containerID: containerd://67ba270ec37937174c700914200039594b01f65d367d0becf171f85b414699b5
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://67ba270ec37937174c700914200039594b01f65d367d0becf171f85b414699b5
                exitCode: 0
                finishedAt: "2025-07-09T10:45:03Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:33Z"
          phase: Running
          podIP: *************
          podIPs:
          - ip: *************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
      - name: job-ku4uzh4unm-worker-126
        status:
          conditions:
          - lastTransitionTime: "2025-07-09T10:42:23Z"
            status: "True"
            type: PodReadyToStartContainers
          - lastTransitionTime: "2025-07-09T10:45:01Z"
            status: "True"
            type: Initialized
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: Ready
          - lastTransitionTime: "2025-07-09T10:45:07Z"
            status: "True"
            type: ContainersReady
          - lastTransitionTime: "2025-07-09T10:42:16Z"
            status: "True"
            type: PodScheduled
          containerStatuses:
          - containerID: containerd://153c06d318e1d133e56fe4776504e4c294b5f1cb25cb7869f2b9e22022638b6d
            image: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo:25.02.rc5
            imageID: ccr-2svebe2y-pub.cnc.bj.baidubce.com/nvidia/nemo@sha256:a08506b4ff8d77718f640a20c7a71df701269434e85519ef58fd34b6ae603dd1
            lastState: {}
            name: pytorch
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:45:06Z"
          hostIP: ************
          hostIPs:
          - ip: ************
          initContainerStatuses:
          - containerID: containerd://ed2b683d675fea139a959fa02840bcaf491775e653ea6824fdd0d51c86ca2615
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker:0.0.5
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/ft-driver-worker@sha256:82537b906c4d64e6c30975b1942f8467a886d3d148d6822fcf0873f2245dfbb3
            lastState: {}
            name: ft-driver-worker
            ready: true
            restartCount: 0
            started: true
            state:
              running:
                startedAt: "2025-07-09T10:42:23Z"
          - containerID: containerd://82b5198361cb8a251ee0350a341b83022cf10b14ad01eac1c5a2861bd92fe4a0
            image: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench:0.0.4
            imageID: ccr-2svebe2y-vpc.cnc.bj.baidubce.com/infra-share/mlp-bench@sha256:f7ee8ccb2493bd95f31defe3aeddcfb5232655b8ae70f32c1496fd8a39389616
            lastState: {}
            name: precheck
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://82b5198361cb8a251ee0350a341b83022cf10b14ad01eac1c5a2861bd92fe4a0
                exitCode: 0
                finishedAt: "2025-07-09T10:44:30Z"
                reason: Completed
                startedAt: "2025-07-09T10:42:24Z"
          - containerID: containerd://fa8e76ec4460ba16074ea1f67d644500d0e104cdd3b8895fb7ed522e0302bc87
            image: docker.io/library/alpine:3.10
            imageID: docker.io/library/alpine@sha256:451eee8bedcb2f029756dc3e9d73bab0e7943c1ac55cff3a4861c52a0fdd3e98
            lastState: {}
            name: init-pytorch
            ready: true
            restartCount: 0
            started: false
            state:
              terminated:
                containerID: containerd://fa8e76ec4460ba16074ea1f67d644500d0e104cdd3b8895fb7ed522e0302bc87
                exitCode: 0
                finishedAt: "2025-07-09T10:45:00Z"
                reason: Completed
                startedAt: "2025-07-09T10:44:30Z"
          phase: Running
          podIP: **************
          podIPs:
          - ip: **************
          qosClass: Burstable
          startTime: "2025-07-09T10:42:20Z"
  restarts: 0
  startTime: "2025-07-04T04:04:24Z"
  status: Suspended
  stopTime: "2025-07-09T14:36:56Z"
