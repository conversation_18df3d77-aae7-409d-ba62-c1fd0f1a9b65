apiVersion: kubeflow.org/v1
kind: DeepSpeedJob
metadata:
  annotations:
    infra.shiyak.com/job-name: test_clone4
    infra.shiyak.com/job-owner: zuti.he
    infra.shiyak.com/job-type: deepspeed
    kubectl.kubernetes.io/last-applied-configuration: |
      {"apiVersion":"kubeflow.org/v1","kind":"DeepSpeedJob","metadata":{"annotations":{"infra.shiyak.com/job-name":"test_clone4","infra.shiyak.com/job-owner":"zuti.he","infra.shiyak.com/job-type":"deepspeed"},"labels":{"infra.shiyak.com/job-id":"job-nfc9y8fjev","infra.shiyak.com/owner":"zuti.he","infra.shiyak.com/project-id":"infra"},"name":"job-nfc9y8fjev-baidu-0711","namespace":"project-infra"},"spec":{"replicaSpecs":{"launcher":{"replicas":1,"restartPolicy":"Never","template":{"metadata":{"labels":{"infra.shiyak.com/job-id":"job-nfc9y8fjev","infra.shiyak.com/owner":"zuti.he","infra.shiyak.com/project-id":"infra"}},"spec":{"affinity":{"nodeAffinity":{"requiredDuringSchedulingIgnoredDuringExecution":{"nodeSelectorTerms":[{"matchExpressions":[{"key":"nvidia.com/gpu.product","operator":"In","values":["NVIDIA-H20Z"]}]}]}}},"containers":[{"command":["/bin/bash","-c","echo Bootstrap launcher ...\nif [ ! -x /usr/bin/pdsh ]; then\n    echo /usr/bin/pdsh not found, installing ...\n    DEBIAN_FRONTEND=noninteractive apt-get -y -q update \u003e /dev/null\n    DEBIAN_FRONTEND=noninteractive apt-get -y install openssh-client pdsh \u003e /dev/null\n    chown root /usr/lib/\nfi\necho Execute user command ...\nsleep inf"],"env":[{"name":"NVIDIA_VISIBLE_DEVICES","value":"void"}],"image":"registry.baidubce.com/ai-native-dev/aixl:latest","imagePullPolicy":"Always","name":"deepspeed","resources":{"limits":{"cpu":"2","memory":"4Gi"}}}],"tolerations":[{"effect":"NoSchedule","key":"node.kubernetes.io/unschedulable","operator":"Exists"}]}}},"worker":{"replicas":337,"restartPolicy":"Never","template":{"metadata":{"annotations":{"k8s.v1.cni.cncf.io/networks":"network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca"},"labels":{"infra.shiyak.com/job-id":"job-nfc9y8fjev","infra.shiyak.com/owner":"zuti.he","infra.shiyak.com/project-id":"infra"}},"spec":{"affinity":{"nodeAffinity":{"requiredDuringSchedulingIgnoredDuringExecution":{"nodeSelectorTerms":[{"matchExpressions":[{"key":"nvidia.com/gpu.product","operator":"In","values":["NVIDIA-H20Z"]}]}]}}},"containers":[{"command":["/bin/bash","-c","echo Bootstrap worker ...\nif [ ! -x /usr/sbin/sshd ]; then\n    echo /usr/sbin/sshd not found, installing ...\n    DEBIAN_FRONTEND=noninteractive apt-get -y update \u003e /dev/null\n    DEBIAN_FRONTEND=noninteractive apt-get -y install openssh-server \u003e /dev/null\n    mkdir -p /run/sshd\nfi\necho Launch sshd ...\nexec -a /usr/sbin/sshd /usr/sbin/sshd -D -e\n"],"image":"registry.baidubce.com/ai-native-dev/aixl:latest","imagePullPolicy":"Always","name":"deepspeed","resources":{"limits":{"nvidia.com/gpu":"8","rdma/hca":"8"},"requests":{"nvidia.com/gpu":"8","rdma/hca":"8"}},"securityContext":{"capabilities":{"add":["IPC_LOCK","SYS_NICE"]}},"volumeMounts":[{"mountPath":"/dev/shm","name":"dshm"}]}],"tolerations":[{"effect":"NoSchedule","key":"node.kubernetes.io/unschedulable","operator":"Exists"}],"volumes":[{"emptyDir":{"medium":"Memory","sizeLimit":"1Ti"},"name":"dshm"}]}}}},"runPolicy":{"cleanPodPolicy":"All","schedulingPolicy":{},"suspend":false},"slotsPerWorker":8}}
  creationTimestamp: "2025-07-15T05:09:18Z"
  generation: 1
  labels:
    infra.shiyak.com/job-id: job-nfc9y8fjev
    infra.shiyak.com/owner: zuti.he
    infra.shiyak.com/project-id: infra
  name: job-nfc9y8fjev-baidu-0711
  namespace: project-infra
  resourceVersion: "3681280"
  uid: 3eb18511-9dde-47ea-ab2a-fc26a5557117
spec:
  replicaSpecs:
    launcher:
      replicas: 1
      restartPolicy: Never
      template:
        metadata:
          labels:
            infra.shiyak.com/job-id: job-nfc9y8fjev
            infra.shiyak.com/owner: zuti.he
            infra.shiyak.com/project-id: infra
        spec:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: nvidia.com/gpu.product
                    operator: In
                    values:
                    - NVIDIA-H20Z
          containers:
          - command:
            - /bin/bash
            - -c
            - |-
              echo Bootstrap launcher ...
              if [ ! -x /usr/bin/pdsh ]; then
                  echo /usr/bin/pdsh not found, installing ...
                  DEBIAN_FRONTEND=noninteractive apt-get -y -q update > /dev/null
                  DEBIAN_FRONTEND=noninteractive apt-get -y install openssh-client pdsh > /dev/null
                  chown root /usr/lib/
              fi
              echo Execute user command ...
              sleep inf
            env:
            - name: NVIDIA_VISIBLE_DEVICES
              value: void
            image: registry.baidubce.com/ai-native-dev/aixl:latest
            imagePullPolicy: Always
            name: deepspeed
            resources:
              limits:
                cpu: "2"
                memory: 4Gi
          tolerations:
          - effect: NoSchedule
            key: node.kubernetes.io/unschedulable
            operator: Exists
    worker:
      replicas: 337
      restartPolicy: Never
      template:
        metadata:
          annotations:
            k8s.v1.cni.cncf.io/networks: network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca,network-operator/hca
          labels:
            infra.shiyak.com/job-id: job-nfc9y8fjev
            infra.shiyak.com/owner: zuti.he
            infra.shiyak.com/project-id: infra
        spec:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: nvidia.com/gpu.product
                    operator: In
                    values:
                    - NVIDIA-H20Z
          containers:
          - command:
            - /bin/bash
            - -c
            - |
              echo Bootstrap worker ...
              if [ ! -x /usr/sbin/sshd ]; then
                  echo /usr/sbin/sshd not found, installing ...
                  DEBIAN_FRONTEND=noninteractive apt-get -y update > /dev/null
                  DEBIAN_FRONTEND=noninteractive apt-get -y install openssh-server > /dev/null
                  mkdir -p /run/sshd
              fi
              echo Launch sshd ...
              exec -a /usr/sbin/sshd /usr/sbin/sshd -D -e
            image: registry.baidubce.com/ai-native-dev/aixl:latest
            imagePullPolicy: Always
            name: deepspeed
            resources:
              limits:
                nvidia.com/gpu: "8"
                rdma/hca: "8"
              requests:
                nvidia.com/gpu: "8"
                rdma/hca: "8"
            securityContext:
              capabilities:
                add:
                - IPC_LOCK
                - SYS_NICE
            volumeMounts:
            - mountPath: /dev/shm
              name: dshm
          tolerations:
          - effect: NoSchedule
            key: node.kubernetes.io/unschedulable
            operator: Exists
          volumes:
          - emptyDir:
              medium: Memory
              sizeLimit: 1Ti
            name: dshm
  runPolicy:
    cleanPodPolicy: All
    schedulingPolicy: {}
    suspend: false
  slotsPerWorker: 8
status:
  completionTime: "2025-07-15T06:01:31Z"
  conditions:
  - lastTransitionTime: "2025-07-15T05:09:18Z"
    lastUpdateTime: "2025-07-15T05:09:18Z"
    message: DeepSpeedJob job-nfc9y8fjev-baidu-0711 is created.
    reason: DeepSpeedJobCreated
    status: "True"
    type: Created
  - lastTransitionTime: "2025-07-15T06:01:31Z"
    lastUpdateTime: "2025-07-15T06:01:31Z"
    message: DeepSpeedJob job-nfc9y8fjev-baidu-0711 is failed because 1 launcher replica(s)
      failed.
    reason: DeepSpeedJobDeepSpeedJobFailed
    status: "True"
    type: Failed
  lastReconcileTime: "2025-07-15T05:10:11Z"
  pods:
  - name: job-nfc9y8fjev-baidu-0711-launcher-0
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T06:01:31Z"
        status: "False"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        message: 'containers with incomplete status: [init-hostfile]'
        reason: ContainersNotInitialized
        status: "False"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        reason: PodFailed
        status: "False"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        reason: PodFailed
        status: "False"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: ""
        lastState: {}
        name: deepspeed
        ready: false
        restartCount: 0
        started: false
        state:
          waiting:
            reason: PodInitializing
      hostIP: ***********
      initContainerStatuses:
      - containerID: containerd://e767ca6bff8c0cbea31c06b06e3caa00d6b1516cc69e8173307a97096147295b
        image: ghcr.io/shiyak-infra/deepspeed-init:v0.0.1
        imageID: ghcr.io/shiyak-infra/deepspeed-init@sha256:e17faad87f610e82216dde59b9422c9412b362cf37ed6e775c13d9e145074de7
        lastState: {}
        name: init-hostfile
        ready: false
        restartCount: 0
        started: false
        state:
          terminated:
            containerID: containerd://e767ca6bff8c0cbea31c06b06e3caa00d6b1516cc69e8173307a97096147295b
            exitCode: 255
            finishedAt: "2025-07-15T06:01:28Z"
            reason: Error
            startedAt: "2025-07-15T05:10:04Z"
      phase: Failed
      qosClass: Burstable
      startTime: "2025-07-15T05:09:55Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-0
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://78f9d677dc0eafb362456f31807cc0decb3670aa5cd84aff533387bb56bbe4ac
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********09
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-1
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://0a3dd56216241a676cf50dacff6e8baf162718e7b6f131252fdf1905611a4546
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: *********04
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-2
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://99c6dc4729467d6fdc08d7018b54b7455f4433c141703d3b0a153c6dacaa60b8
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-3
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7c2ad1659d5516df24526dac195411d22707a16483611ab8588b5cda64eceadc
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********3
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-4
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7b3d61557449b53a74caf4f716cfb78f765de3067b944133e11a61689f19b030
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-5
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://fb9043a2b869739a54b2ed6bf9f7f3322bd8c54fadeb924c2327b49abe81047c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-6
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6720827944a37bf4a958b7026f3146c9f444a03d6d70b4ae7cc0cd639e740429
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-7
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://474c3e6a0e699c65af9d7f54962149c5943d8afee2e1dcddff5e454c199cf9d6
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-8
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:01Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://66f76c99b2139e45127d2582981a5d4801879f368b54f018e818fdc9828b4725
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:09Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************8
      podIPs:
      - ip: ************8
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:01Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-9
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://3704666f9a429c4843391801e74d527d4e543bc58a0b593b6526f289e59a4e7b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-10
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://737d8dfc3a29571e557ca347dfd4a8abb8892719ea05f9321392c904d4a59fa1
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-11
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://091d8110dff69be6c4cee463a6b17980599dce8c0ea3eeb7f828f691b5135f70
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:08Z"
      hostIP: *********0
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-12
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://cb90024cd872305d82b96c4606d77f6222ed2eeda73028e2b7621da1c89195e8
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-13
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://afa545fb75fbc52bde0fc8ffe213796bb7f4c0efeadbbba52d2d65693157c7f2
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********1
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-14
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7e6b7650c6985585a88bd40b9f38d2793f4c8f335f349b60c22cb2492a70eec0
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-15
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e09a9a2f41a6d74e52f9ca924a67f995dee8f3403d6ebc102a9dc410b31d4d0c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************5
      podIPs:
      - ip: ************5
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-16
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c487026bb7f882463526172ca45243fe3f8d263d1a716937d6f068a575530973
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: *********3
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-17
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c45be226bda2ea57598689311d8b00465db281d4c47008784755108dd7af3dc9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-18
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://0c26b06ce2b9647c6abac3b9108ec6a66d30eb1666b4fc39f30c031f58d55401
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-19
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://98646825a669807ed07e2af5186ad39216346421208e9033cb993b8a4ff10e6d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: *********2
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-20
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6283b78f5286db0e94aa756c3dd710b13dedc8b8ca1384945012af23b3641bb6
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ***********62
      podIPs:
      - ip: ***********62
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-21
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://4560d0df7aea8a46ed54f5b845988acbbaebddab147a23ab2c617acecd88cb67
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: *********17
      phase: MasterExitKilled
      podIP: ***********04
      podIPs:
      - ip: ***********04
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-22
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d3e666fb7a4be704147078b4c5a36c97f66309551b81abc2e885964279f2c4ad
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-23
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://8f3940b380ffd78d82e0fd8e6c6ecae87e8af01e1171f9eb9310ba5331e57f7a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-24
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a74c719a3ecb7a5542a033714d8dd1931d74fdf895dd79aececa5f36acd899a8
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-25
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://026fb7f04016db9ce2f3458e33a1d9726c561e7073679278ec7a42091e74ea28
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-26
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://492d48c03a6ec34aafc4add717558e823f9d90e158a12a0d7af8aa8dad7e3af0
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-27
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://683b0756daba8a1591e3bdf948d76d765eb6fdb07beac0456ef502553b2cf07a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-28
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5da22d03fdd550c9b688f2874abfdc1f2b9260990c5f679be6df59a85c2c7ec9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-29
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ba6641b63e00a5f59269dddc9233defa73836c447e722bfb2d74df0b44eb2f21
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-30
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://bbe5cc82d7d5322c1520abfc295ed3b6b6c84b7a6182e8892fe620d3005954c7
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-31
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://429347496a70827a7f7ad06e94cddd67ad8177f8f558172341b4ad7b49a0495f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-32
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://4b826470991ad9b6a1344711a385f653f75ca80fde06b12c0206d8f598b9d68d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-33
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://dcf7af7788a49f0a707807ef24b2c62d4d3bad58c31b4d422fcb5c79f7af8822
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-34
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://2243d04db487cfa448693142631f83705c6932a4ef577878cd63f2db5a2c4e18
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-35
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f88a9e2a3adca8d04268dbb39b04de5dd56941ac2d7b45d4979a899b4d3f06b3
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-36
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5fef9a3e870448d63e233600e564d45ce546fb1846fcc95a6c804ef028b27ebd
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-37
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://8917224732703837ec22780a1c13d47685c416332cdcbc4f283130ad028b72d5
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-38
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://209ccceda14fb38d4efa93b65d4e6262e4deea56781b3f4adab08f409ee28bce
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-39
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a7a358fce5bd080bcd729c2c88e00d3f46635d311ab4b7b0583c7d984e6352cc
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-40
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6c9816296728fd741b3fed55cb82ed52892ba1777383074880daf0d5246a2b72
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:09Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-41
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://38642bddc042ff9dc1a8d0328de156ae4271a350f0cef5ad893cdbf1566e14a2
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-42
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://852d8bd717d94e741e9d13ec38870e7ee1a1b627a2dddebb21a1f4baf6619ce8
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-43
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://219955c0638c7f154e483711c0b58f5ad2322a411c234c97e9baebdc5e6cbb16
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-44
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a310f0b4431ac12bad1ea999bdd7981dece7e979fbbed0abb59287ddd905e5aa
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-45
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:04Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d6ea85c2ea6afe8f1d1f10be8555deb9386fbf452acfdd7c584087756a19335a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:10Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:04Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-46
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://3a4b55e89e55d849c5d2e6dfb59649375379276d013ec1a65ee90b9bf2d6d5fd
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-47
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://450fa249b02724980bd5d98f38a13567d5905b7a732a6d34babfa8e27243afb8
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-48
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d36907320885c51d51f7bfcc5aa4f5db94c7a47d0c5c9d8547194b77dde45c44
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-49
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://aa8dd005b981b6c0c3fde45496983bfc3e9a206d26b2207b1b0acebb42fb0e1c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-50
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://636ccfa9b5eac45e669934f52685a1eb3ce0a98c7ee0c91433bf200cc5ccd40a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-51
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d0b20a3d19e4354d893864498fdbaecfde91ac381a12d831b19f4a3030e2c200
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-52
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1cef55ed68967ea5ee372d61a08defad8b666c1e8bc647a9f8e19a99d90f0371
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ***********0
      podIPs:
      - ip: ***********0
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-53
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://2a1a6a75cf5bb19c039c31e2b55c0c462fee52539ea01d5235cd00b68ea3db4c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-54
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1d7868a41f06baa79f37dcd48a88608f7c77e92889b2596083cf980dafda950a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-55
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b776d0664954562da5e2c0c663bc299bf2f0033c779b279faed2241c5826baee
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-56
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c78a5dd0001c67b385bf09f08e37363ee93e0bceaa9a5e6c867fcacbb6192e11
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-57
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d911c249c19c5b6363757c1db292c843101604fa8c17f064fc08f0dbac56f303
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-58
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://06f63b650a256d0971b018fe1b393f2ac3469144727359bfdf6741141bf2f341
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********20
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-59
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://33cf3cc0076791f286fd3a6af2b38c11cfd108b6e421021825f032fe0334b079
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-60
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6f76c9f2bb7aeb6a5066d1f90533b5ed700203ffc57c7b574cbf3f4685928cd1
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********9
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-61
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://bef481ad6bfae6fad990ab9238cbde7f4f39e12e89e9d7bee734cdd77c6080d0
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:09Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-62
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://05a43f425cac048972fbb4c41fee751f863f69b295834fc8ec98bc06b57bd065
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-63
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://acad01c6ab23366e49696be82ad089d4ad084760499058952229719858c890f6
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:08Z"
      hostIP: *********5
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-64
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://feae27c259dbb195ca736f83d44271ea65cf057d3fb3afb75d6283ceb51770af
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********1
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-65
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://4df25d332f22553172e6581264754ad2b1398f47cf605a14505998921af25c8f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:10Z"
      hostIP: *********13
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-66
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://962aab968c141d7fbe382442ffeca8fe9609cf7b3854ce25c536fe05fdc6d5d7
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-67
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://13e48726838afb52c66175340df350a98bae690469517d9409f10f3518307a24
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-68
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://538ed2a6fa5bbf19b4cf3ed2e5b3728229454ff3fa010447fffb3c6c9766e338
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********8
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-69
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5a510ce72f82f282612f95d38467d2b6411cdcb75b715fa637a754dd2d020c6f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: *********4
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-70
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e061abd0059f83d26f57643beaf0e0e56651faf197e345ea5ca6404d78856cca
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-71
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://133b257ba08798a6f9d65293eeec18f3f6655c0d474dbedf4f438cfe16ca4ea9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-72
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e76e06fd2563e9b46b0130a324c77578df184edda7e71406ec6bdbc41b56446d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-73
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f6f71d4f4fdaee5c869ccb9d1fa8fd601e7564ee272731dfab3076b6853257a0
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************5
      podIPs:
      - ip: ************5
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-74
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6a4da9ff55d455b0c8adddf66598ff6921e62dd5cc5557ab413c0a15342cf557
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-75
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://44b9a7bff87d5167e9b223ba9190974d87db0e739e7f7c2f041d466adba034fa
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-76
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://32686c179270f7f9ddfbf7b0622ef001e5106afc2afd57af37e03cee4e607c62
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-77
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://86e190ce218e47ff60f14f772cf28f408a1d21d2c8d9933b4579ed267125bd05
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-78
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f50ef7bb2172b255753d21987003dcac277dccb07db043e84e488d6d1d92e538
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-79
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e2026356708e29ca50ef317c3c891d4c568e4ee93b7f3cebdfa6d690e7d1b7df
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-80
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7003622b5ca1e640724c3e7c7ae56906d384fdae73a457fccc56998f13fc5da7
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-81
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://81a2941e3b9c8d98b5806bf15a0e263220bf78207c8fcfcf74c394013756df9b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-82
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://2072a57f9f83330c3056e712a8f90ac3e71d94bf7c8854f0044187400757f5a9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:11Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-83
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://99f5f0f9b94651a1a444806a6bf23fee5c59d5a0b0d63fcb088c5689f7e14593
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: *********9
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-84
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a5b00611eda8b4bdd2a8033acc1eed0d15968e259ad2802612112562e4777d74
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************7
      podIPs:
      - ip: ************7
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-85
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7456621953806a43ff3652fc66329611e003949646cf0b86f97e5311b083b0c6
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-86
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a1fff13fe242bda8e9a0975ee0bd9e36dc479c64e595be99042a48e0e907984f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********9
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-87
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://3cd1888b0e418deef21d44918e79f7843746608f1392ab7651d313b3736b6400
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-88
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b4969e5d4f134a3955639218aabe7bbaaab80a49d118e817faf092d6ca7f21fe
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-89
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://aff506a65a41e4f8200f8f8f06d090c70e25a2bbdfad5779e3f197bf3aafbe4c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************9
      podIPs:
      - ip: ************9
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-90
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:02Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a79c48ec571e581493be6838e8d68c27d3d404b8f4cdc37f7a0b56ba6a5aa560
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:09Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:02Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-91
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f4c3f5e76728bd4f2afdec3ac08ce6624d4947351dab30df200badc6132fe647
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-92
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://819c9d131923d90801baaa7e35168d9419263d1f3a0821347aa37bfe7437f1da
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********1
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-93
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5714635fb33545454ef6f3a2f65048a32414810ace889f8d9eb37e5f9134dd68
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************8
      podIPs:
      - ip: ************8
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-94
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://255ce2c282394039398597ac17946d3d72fb1e478de0912cca339a9780da0192
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********18
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-95
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://91dc461316671badfb6346e366fb5b60ef44b1b38d518002b6d2213675fe5e53
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-96
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://2d3ebb239142b602fff86b5793f89e088ed14328e92f8f4494c3b30d70c79664
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-97
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b43141534b714bee2821942df67be869c376349c34fe954e9feaecf158c6dcaa
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********2
      phase: MasterExitKilled
      podIP: ***********28
      podIPs:
      - ip: ***********28
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-98
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://92756016cdedb39ad782f72e29ebab940fb64d5f007031352983af74fee09fe0
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********4
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-99
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a624401edf0d235e17320a921d053802d86098277836cff617eee6d24132da63
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-100
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://52b02bfb649a184e044c7e7779f2d57baca920419b61aaaa384f508a785a7207
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********0
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-101
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://0a7411d8c9864df173b7f45ce88ef71e05171df341ff6fd21ad4bc77a9b3b8af
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-102
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:03Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5a3e50e2e352e18980736880830340367a3f72d8cdeef47fa254e4aeacc4b195
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:10Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:03Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-103
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://cb6657ec9a2e60e16926ac719c2ede7d4f5c15dac14203bd5f387ce4db280d3e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-104
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1a0a56523cd9205741c62fa9a49322927112e9040965d19b75def02fe8715b30
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************2
      podIPs:
      - ip: ************2
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-105
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://bccd423163fa5809946b5f96483e17ee04b7ec4cd4b724b800aaebf87dd81f0a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-106
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://42223b845e196c86de6c1a5159dd8eb4fbbc6aab326f897ac7ada2534536399a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:10Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-107
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://10ce35902abf6b2112937c79850822ddbd8d7eae8ff005f76fff110fc2b9ab96
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-108
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://55b3233000136a0dbbdf07e798b948e01f6da4625e53c6d49fcdeeb96c9e4e36
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-109
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://9f45b0b127d136123100f7258aa950de562005337c5924c02e9f6d8b5291f151
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-110
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c978aa48cf838214797f8e00e404a2c211933baf8383cb26147d9e06c74fe75e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-111
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:01Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b3d37dbb60729a854e7ff2e35a6742278e19001bd76f73439146b7f006aa48a2
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:08Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:01Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-112
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:01Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://021d2672c8e440f592aabbd687cef46a228135bd6c5fb7b67247c3bb7a4e4c14
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:09Z"
      hostIP: *********05
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:01Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-113
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6afe7a552aa23f5f1e075ce4c417a13de67e9810c16c37c7fc7e3e566fd74930
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ***********7
      podIPs:
      - ip: ***********7
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-114
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d6c4a39e25ff812a6480c41237e596a3a7221c8f9dea90f4446e37f77ecddf8c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********8
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-115
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ce5e1fd0d55832c6ab9047c4762e7add81d1ce9578360de8c64a09a54508659f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-116
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://abbc4de7e155a089b8ec7a5b0b1dff3fb87328d1462a38a356201dac43622ce6
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-117
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d036125cd764f685d267b4300d70ae48c5bb3c710d17a17103530727b4d7edc3
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: *********6
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-118
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c08a8a812625aef227564c5c6004b79a8a241fc18f448b5ad6ec4f973f9acb4c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:08Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-119
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b15e52b26345868a2ecf7a6a4899fcebd5357a4caeee87849440db0b213f3471
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: *********08
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-120
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7578e7858ea75230a8cf76987698b28b378e81dc7b1689ca600ce4a624885422
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: *********5
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-121
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://49af18ef039cbd1eab329e7040aa53b67b00287fe1e3db745d6931b84f4352ab
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********7
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-122
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://83ea23a18acee1d73e761744ebb21b24f85dc768bd4c5f0a55498cb2a09ad53d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-123
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://42a2a19ff5c2be305e506bca10fd6b4287246cbc301c66ad69b4c7d48ff4abbe
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-124
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a9b05cdebd183321ec78649f9748cfb3b505872cfebdf620d78e24720d67c0e3
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-125
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:02Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5af5adf9aed2cfb5f4c3880dbe1e87ce97e3357c56a20e8644fad323886f0b0b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:10Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:02Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-126
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://4fddf138aeab3ce404a31471b3bccd35d3535fd79902c1ef42603f00b5a3da3c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-127
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://847ace5c552ee992f4701d0506bb4b4c931d9907a79b5562ccf35ed1590163db
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-128
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://515d5883497ce553cb05cab8c1354d44ab971b21ddcda5902a372a3bb0d20977
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-129
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://32456aa794fddf76d2bbca2d74f07cfc84be5d5d3ad659604bf8b73b052914ce
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: *********25
      phase: MasterExitKilled
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-130
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://675e5ec41370dd12ab116895457def15f9ad7f3456733b987fdcf2d0d875daaa
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-131
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://4e0f50574fe8b251e0b104a010440332b829cfdb0fd3a95e84b2fe2d8467fc6d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********15
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-132
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6f22c0e074d5aac1fbcb02e2c92027f471af80c61f9f2d0142bf7b17447e7478
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********26
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-133
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://8eabc044095c5b2bef94abbabd1ef7cf6fc3c9997d3cbb2865ef749e279b71f3
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-134
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://700965d2022695e2e58881fc94ff814cc8dd46901a6cfae329c6cd08e22845d0
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-135
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5819dafa84f5cfdb220078572164d652bab719eb4d6322273b29d7c063128a1a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-136
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://22ed4fcf9ca94c4b94945c2e6503c7a13912dabaaff17a086c53b18665825ac2
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-137
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:02Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1da8cd98f5ce3d4e4c0a9ca4ccb0c35abb9f752b14d867f9a2c6a03c6aa9c18f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:09Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:02Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-138
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c7c497f8a3fca7e65736a757a8f310b67e9c77ac328203f047a13f012b12f0cf
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-139
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6063f6b399fe9cb11b67dd19d547f246eb47ceb36b862de360520fbed77c415a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-140
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://2c30fbc46bd442a4c45a5e6e3748ae178d3b7b55f86abe258f632281e957ee35
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-141
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a8da2b1dff646ecd291d37b0dfe1b5c27b720462f556d9fe5e0cd80a97f1a3f8
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-142
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://427180352bf3c3d916e75ddc9c8cea567a15fb142bd2d1c551447ccf8d81774b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********34
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-143
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7c2d0e749a7d03bc8ccebac3f5757df7d75cf1ee77f78194dccad05daeadbcca
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-144
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://716118deb126a80823711ea1a2f470cee85e0af287adacda25be6092f90888b5
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-145
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:02Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://725228c312ff0e277938333eb7c969823a7b7c9ccf65047b6c2551c9e76a2b90
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:10Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:02Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-146
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://0200073bb4663a15f975296ae2f948cd8cc15cc48cdd34237de55fd91bff0dee
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********31
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-147
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://00d10c0d24694f44afa673935127d9e1a5e31d4a36596b61f1e41df5b93bc4f0
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-148
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f348085c77f91aebdd79fb33c83220529146553332446abbdb92f259fe066729
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-149
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c87b81198d681c015e6e6b86dca6a288affc69da29fbb49a2fa05915d06ea59e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-150
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://2914f32832e0993703018043d14e4dfb451e4bd63199529a5776926125ec0b87
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-151
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://3d17f658417d1259c0f1fe7c5fce4609349021ccc8de4e55ede1b87f361adb9a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-152
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://3740914cae0f25eafc3b80a060a0753ee77d70aaa29b596ee5d97946d6fa3b1a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********9
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-153
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://db1ecca1d8516952075875b61c6bc7b6d6ef50de090651f4920135e23cb70ec4
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********3
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-154
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://43c2cffe1b2acf27c6f80577796f92c58a20d90d5d76a61494f84c9a7bc9e283
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************0
      podIPs:
      - ip: ************0
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-155
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://65da4b6172dcf02427446628b12515a706a91e977c45b7ad79d30f394f1586e6
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ***********8
      podIPs:
      - ip: ***********8
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-156
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:02Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://cdfe86dcd73215ba36fbeca0aba5203c5c6e759b42e6bb109a7303c427b65fd0
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:10Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:02Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-157
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a23d277dc90d8527008e49e4e669af4bded8d7dc84723229802f4e1330b451f1
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-158
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://0aeb619df748de9da272ac6d743712f8e44c9c82f58f0fc41d3511c10b400fa7
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-159
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://cd3351e261baa7696aa5398586cd8bfd9d8187a18aa95857597ebf75e02afae1
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:09Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-160
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://4171a529a846c671d84e21833f51435458e3cf219de7aa1e6b0276f289e1c821
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: *********19
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-161
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://81436dd3a68acb73f9cecadf7f6a773440f4945783058dbb4671711bb3372332
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********00
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-162
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f26a30a221f5d66645cf4b09935faa40030d64d15a7dff9b5cd6a902ab3e44b1
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-163
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5281b21cc64ea87ed926178beb000eef9d2fd8531beb051d5507e68a5e8078e8
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-164
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://8f8e95b50286d9cca7059d7699e9e501feec5362bc21f6f761032bde4db0bd64
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-165
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d16a090921411ce8e6dca3af066d1b85aa635c78899152be29caa5d82b4a5563
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-166
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://0c1b9ce3175d748af9e999854ed463e87cb28e3019ba901bc5036a42b1e68ba1
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-167
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e36d33030aa626959b069db884b4e232d5733250a7de7d52f9ef95286405d83e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-168
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1c7f8e37e16fc7cb918c086f7da5331956a270a4db0774b62a6cbc68b18a1061
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-169
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1bd2642024373a1645f7d6df46ffdccfbff029a2d46cfc7597b83b79fb1b9bcd
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-170
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6d9a9a702473296657d533aa413007763f16f085833db623a996fbb8e3cc7793
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********3
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-171
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://309836e73e0c468c4c3a1a432467110f4388dbb5c8e91d6b171977a0adb9c199
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-172
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://12bd23e0110a01b7fd615fefdca1141f4caa864fd181d2705aa62496e042597a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-173
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6ba2c952ed8f32eb45ee03218f7182d3108d3eeaf3d12253fdbf62097c8fb87d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-174
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://bdc9ecddc4d9b428297578ce67ecff02ea8da487c83039c854bc34a61fa4fb67
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-175
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://cba1baa963743ac5b07916f4ba022e9cc172db86621b5b7de18cd3ffa4c2c633
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-176
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d2c8752aa468dda9af14c59fecbd2e9d17111b52f8f26da4f70adc3e4da22c1c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-177
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://9983270e530154803e5393a688bd47024c466fc20a4430de9e870c3610b0f60e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************4
      podIPs:
      - ip: ************4
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-178
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ff1fa4b95a7763d12c7ba05fd17685e633a7db4be01b4bdcf2db58715dd787bc
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********4
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-179
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://73449da1793e587ecc13fa6ee614c0a9b60aff2dac31d2bc579ef8a82b5e661d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: *********5
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-180
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e994b6cb9c06bb6eb769ea8f1c5640a0959ea9b4b2e26793bf6dd6efdd6c090b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-181
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c415b2802011788400f948b42b84f72914e9c50e6c58288d31c8042df8d490bb
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-182
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ba9fe1f828ef6f209236aa7d5b9646bc26fe078c0d418ab3438832def22c2b1b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:08Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-183
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a9627c57d6242799bfeedbdf75c5c7207f48f9e0c1826a777f3ad332fcf21c29
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********24
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-184
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://12eee506f66675fdcc6dc1f41901f30996d4331edab4c9bb99a05660942c3d72
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-185
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://13d8cafbc5985432fcd128e62c10f5a1ffe72974e0248dfdead29da2775f0b21
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-186
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:03Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://8c55173e4ef42923a00f1006c643b3f06201d02a67f0b46603d26b9f42f666c9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:10Z"
      hostIP: *********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:03Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-187
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f12b29e586df5f45064043689808484e1563e71e39e6d3b303b746060269a84f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-188
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b4f7d2ffcebbf35bf172978b28f91d1bfb20ed6128305f9fda424549e01b3057
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: *********28
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-189
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://65a52c9d3188bf025689aba9b304514404be7694fdd7af4830bdea407c3eedd3
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-190
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ff33fb81edcf0ffb65ffde524ecb362abdc5d89519066e9b828b1d9302bba4c1
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: *********2
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-191
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://af5898f9485ed761bba2ef85572be35ce80d586437e0b47d2e93bcb35c231b09
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-192
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1f7713ec4aadd0f0a6bc3fd64c3e7624b54efa4875db63c9c801ddce73e508a0
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-193
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://35a19fd0243941e9b111f0e49a4e614d1b104068794fb62de2d0e04697d9efd9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-194
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:01Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a41a1de45feb190565276961438b2ac2cb72b59515fb8fd4cb8030c74cd3770e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:08Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:01Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-195
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a29d9c2c3c0c1d60710eb2fa8b06076913e1d61df46bace687f7d03efd6e67fd
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********7
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-196
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ca6708dceccec33b91d8d609379c496adfd624bb30e6fcc0df2ca0aa629a61d2
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: *********1
      phase: MasterExitKilled
      podIP: ************3
      podIPs:
      - ip: ************3
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-197
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://29b17916701ad3f52e0b13636cfd88b76fc9056a5eabdcd9b1cb2c60fbe434d3
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-198
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://572385852ad54919192ba94695f57b092d66ec3e147712f02f3476d2afeaf683
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-199
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f758de22a5784079c9f724e01b19843c52c600d2c5f6844ac48e3a6aac624bfd
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-200
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e253d5d0bcfbc248007ef59f7c82a2b916850911b6e5be714eb223448606f75d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:10Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-201
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b5dcbc332509345e46bb4e6e056859ffa1ce392d33a2c60e6ee2917bfa4b7e35
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: *********8
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-202
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://fa2356b480f85eb391925b0bee7c80a87c61e0b39138b5c0ca87ac92cc8574ad
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: *********7
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-203
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://df1480c42599ede9a415150decab5d52248d616f4116e5f02689cbc7c0284256
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********1
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-204
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://83df474e2ed455841a6af57794a68e37a492382f0320d9e33dcf708f35286fdd
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********30
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-205
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://45ccabedb3046c6845a646f36e06aebb8a4b97aad04f07ba3ff8935c5ef400f8
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-206
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://2a2db8812d5321b55c486b0bc718d20eb1692a18e898df940a76f51952da1fb0
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-207
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e302f70702220b709e72413881d2346f516fca01e6166f197cc4718bab160e2f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-208
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://89bc34ce0b416709d1731a6d7f4a5c9f5ef8f2f8cf4ec1b468db507b2d669a51
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-209
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://10444433b921dad7b8e2fc67376c05cecf9d81ef612f7d56c4f55fb336eb6709
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:09Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-210
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://31f24f6d0c399034cac3a970d49f628b202d0cd0fb0961c049f0ede538ea17b9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-211
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://9ad84215c7d99c3e604428fdb7b311649877fbbff62273a195296ced1511054b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-212
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c06fa198bb9bf596af3fe0a8b0ff56af30fceb9100ffc0eecdd8d5c0e86e70ef
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-213
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:01Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://936f9ca82e60c61d16d879bab9955b7efbd2279dd0be0d119bb28e4922c2399a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:08Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:01Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-214
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://132505d372c2609737807f8aeae52220d53a3827938fd5359010e188a11acc9a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-215
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://005d6149f58cc12a8168f131766691584ee8d9313e7c297af088957bf6acf8fa
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:09Z"
      hostIP: *********35
      phase: MasterExitKilled
      podIP: ************0
      podIPs:
      - ip: ************0
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-216
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://8af90545675fffb13b14f1438118f6c3654cca7e286992dc094d2e8366545e60
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-217
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://2bd1ebfc7e6981f02649c9a18e39bd8682d509120d466a9e31fee819a3362e02
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-218
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a94e55ff3480c8915ed87d561251c09d73187bb0eeacf3e12c5d02b65705cb68
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-219
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f3c3d6897018ce135597d92e39f713119b8f4c88c5e2c988e6dbba5e7459255d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********8
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-220
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b7097e674700942713ba70334539b8bd6441d24b0bcf63ff753c184bf33fe830
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-221
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://8f21458469b87fecb5335bebdcd6633a95526edd1db6ec1ccf320c0756a3bce5
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********3
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-222
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://3ce2cd09dd6c78b5730927ab009a0ceab7ccd12be54ab3ab82249ee5389c3d9b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-223
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://582eab04cde87472800b02b66c83ff2b9180cc18437d1f19022de22cdfa33643
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-224
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://49152099560476e0da621d89360e4c6349c095ef083f035a7a080711245568ae
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-225
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://30e00e4792cf71975a016aceb2c6d347ab04fddc284937e0de6f558af02710d9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-226
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://cbca30441b2509177f9c86423abb05b48dd08eaa3e1ecc7d8f11d303b0aadee9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-227
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://0aaaf9ea18457ca97e7a06ef36363f9dce270428f86b2491275d37d151e714fd
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-228
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://2c082f39204f7fd60c99265e3271aa7f332a492ac1edda1255b2daff6010aaf8
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-229
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e940d07ce9d4cbf994c76c2609d80682547ed4df503e7a179079aee7eddf4bba
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********4
      phase: MasterExitKilled
      podIP: ***********4
      podIPs:
      - ip: ***********4
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-230
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://aa8037db4aa06e1846a28b37436e13d49b784caf9715bdce9845641a072fc31a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-231
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c94ae24b84f72ce1d8e1dab593d768eef4322ace73d3944d1d358ed033707b79
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-232
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://3593cc287b97cb0315c0499d4fc7abf174df20d1c0f513d382aa74edbfe5a11b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********12
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-233
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a96925e4c5fc18b2d2864e51f02d72982245862b35e8c4fca9af28166b3ba867
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:09Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-234
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://2621d0319584bae8bb91c55a8124a27e0082229d93ca8a6ebf2f36aa4b3796d0
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********16
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-235
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://446f7dc6deb29747af35bf5b680eb84e55971d15ab21e0d736feb4c6fc786141
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-236
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://0f7bad6394e93635b5765b965ee27e1e912a84ad6911db094e34ef828570c139
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-237
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://59169b0e30c988335104cf97cf9c4ffedd8b30eafa33d6acd1eb63d626e9febc
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-238
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7c1bfdef1aa557e88a45120b3578792609df70ab4bb983e14b47fc9c66671701
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********0
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-239
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://89155c2edf10cbbd1ccf4c207901546c0a8afc2a4a82c6a223980cfc19f1b30d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-240
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://beb1fb5cb5d070df10e37d3b0dcb23aa79c5bc20affd5e750a29e80797f1d26f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-241
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f8127dd2b8dbabb95b14c00eaf22d00d39fcb5a3252b853ed8a30d5fbc68638a
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-242
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://4f49be0f1539bffc9bd5e513b2d13217ff8af7fe675b2ee92f7ab6989404db41
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-243
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://2f035637688b6fc21712b4789037b4b54ec38027319be13af4dd60f8768bfe7d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********02
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-244
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c4791f9b5aa0aeee3cc7e3fd680449dc1f059e1ed95764512cd653dee2abfa97
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-245
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:02Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ba366a79230fda5db7c2716f80580c55cfb062ebbfa5b519dcf07a939ad7c671
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:10Z"
      hostIP: *********8
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:02Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-246
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://515c6cf5c70facd2b4d38ca17bcb248bf2463e99e658dcd80ac8cdb77162a977
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: *********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-247
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c47f545952a470b8822321636efd32c20952e7899ac031be854d1d6fb8d39954
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:08Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-248
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c1d6e160b0799c9e1922c6e1040ed017e988f28739879abb7b485a21de569ae5
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-249
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://561a53feba15061686a5cb391ea6a480b42158da44fad78bdf82977c27193212
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-250
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://8568daae73bf6ee071964242520ff1f8b9cd3e3b464030e139f16aed95b0e832
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-251
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://44457513f24b71dde66521fbad89e82461e9267afbf56c76bb7dc5f06bc12712
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-252
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://047557b0cba053051043fbccc5fa767b169d9590654a648c89b1a309fdb1e128
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********9
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-253
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1fbe38a31e0dc57c60158f84aba7b527366e913554c943cc213f6ca101d33e71
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********8
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-254
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d841266c903aead2f62d0c755820ee8339777305b75162d280988473f460a4e5
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-255
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c40eaf5a6efc30d8e957e69af4eafeba1763ea92480a375f6f28664ac75b20a0
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-256
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://9d24b238efc9d080db50c50d8612588de94ce7d69466caffb85638295dc5916f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-257
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d92767a19ff94f081fb932765229af7e372a957f5227000658079558ea6a80cd
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-258
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://82c00addc2dadea7276319c7ed686c483fb2d37f74dde5f03100b4aa1f692c20
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-259
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e96f244aa22419af82f31fc78c8b64a0074b623b775cfe3eda2cbe601190ffd6
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-260
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:01Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://3c2adea1df2ea61eb2a0dbe5507818d2ec0f5496f3871059ed44956821fabd01
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:09Z"
      hostIP: *********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:01Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-261
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://02fea6c78de6fb37359af6a85a39f5cd09838a3e55c1567ada0c873403d84bc3
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-262
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://970fe40b3f1025fb50e2f7fadd9f4e7ddf778448d9f257e018234c1af7492c8e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-263
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://0eaa0f352cec869a781eb0459a88e589d6b35780127854f89685b6f90e6ec64d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-264
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://8de758e57bc52562ae40360cbe6c16a4ca886a2ace388c3fed70042ba45c78e0
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-265
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://62338746641f8d3f0cdbe44d298efb98d6abf4223c2425a4128044b7ee64eff0
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-266
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c7abf1799bb2e4e339b27f6e61ae46721abc2da29f99684043452d89785b5d45
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-267
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://830c4f13ed7c354433514eced778280ed9a8d6b22cc05080ae6fa26033af67c9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-268
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a8cd0ca8e791b16426e53088e315d3233b0c88d3130f7d63827ca3cb1bf24199
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-269
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://dcb34b6b7f2fc7b7333eae41f590bf1807b16bf002115471329cbf3e174f3f29
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-270
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5167b241a7ad33dbb9ceae9463a6537c731b09977a0982aaa541b2a03df74f22
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********03
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-271
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ef462f09252cbb75128ef1de2bcb690f4422ae15bc3709b1397c3a268d641086
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-272
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e47a86a7b81eb34ae1ec5a75e9fc4b099d2b6c8bba0c0d1e21faec6cb429a23b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-273
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1ff1b79974ada1be2b411df17762a3d13d9a47acf4a51474d345e3b4673e0e58
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-274
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://3b6dba93506d2597dad324937943d9228f6fd106543c8166c40668d8eb9a7052
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********0
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-275
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d2a1708a64356b973795f8fb5ea5da1906688452ebdc5193c70620ec135d2543
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: *********01
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-276
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f20a77128c117f0a1ce08cb1ee24e34674b70ba1eee422d657b8833618dd5769
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-277
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1498bcf4605b521841c3b83389395ddfc59942c1449589eba914689ab12f5276
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-278
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://810f02654d846a42e79587616543ec09835336af6a103cf0eefd49721451110e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-279
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://369f183ff8fcd754172052e95da44a088f051cba77880dab3d7ae89954dc39a5
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-280
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://789a94dba633109000e5e2da942eef2aa1a492ff0d478345fe54dcc384d5df21
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-281
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://21dc04b14388e5f09f8b2386746182b3e9bc26d925c3b8ba9b1602bf0fb2732b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-282
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://45d2a969825566a9adae0b5a0a0780f97f0f904a563cc97608a998a66d37bf25
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-283
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://0d43bc090daa95ab3f330dbe622098eefc9f9a259c6dee1bcc78cc7482b90a18
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-284
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7fcfff380204a1a94bc4158573ed01e60aea3e908207b9d4b841964ac407c4c1
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-285
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6c3776325c4e1b12c9b8742d811dd4b453ec0e88cfb0e5efaf505addce8a9ccf
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-286
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://a1689b423a8b4fb7b8add201d14e102b6cb10ae875b7cac151a02f8902491788
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-287
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://14a8870cffea0ca1b50adb9ff2c82245256fce1613d9dd1dbf9d2e4bd668c6b3
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-288
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://2b9e01fd8445246125b373e7d7ba75bf05644f2e85c158367325c7632e0d0021
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-289
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:01Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6cabc2da9b0c1cb17003b67ad283b26f5342d3551d34d58d6a39d89e2ba5de1b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:09Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:01Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-290
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://5adb701ddf284b754455cb9886b99a5ecb17a881471efd7de20351c052ed0786
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-291
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f804a64201f107b0a47327e51f91fa1ba48dc81b03ecd7ba9e8ced5271f805fb
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-292
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://8d76b3415cc580aabd143359c739d6931b9e49cbf65260ab4cdc0bff684c232d
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-293
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:01Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ed7823a271bade4fa1ecdffef725725c01aa982b582c2e88d7a65db7dbef2086
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:09Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:01Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-294
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://2356d020b24daad2f94ebf0922d2adfd3a89adeebe483a90bef6626178d1b092
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********14
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-295
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6840e3f771278f2e748f99a219c8b10a9fe74312a6bef8e327e4920544bad67b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-296
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b7c1f335f3f24e50d8b9de201ee768ec6563dd30fb14bbcee2eb1984560a8520
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-297
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://b83012028ad6bed604453d3bd77ff7ad2dbb681169696d8c9fe70fc9e97fa3f6
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-298
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://cc832f1d44bec8aee14d6332a2ca16f0e02a825587c15d096d7892f1d81bd8b5
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********11
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-299
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://915e21d55433b11d68428e5283f3b0b9651f8bdc0fb15b37e7d25be45c76e426
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:08Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-300
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://c41ccd0acf9fffddfe10518710f2dc788ba688cd12b7a30fa16452712ef753b0
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-301
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://4e7f6fd2d539a1190e84c8c8effa8757662319054ae286d184e79585b1190cf3
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********2
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-302
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://80d9a9a5a42610ca4f3c58551c2b230248553ed436ed85530d90de189147f967
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-303
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d406d7093ad50c4cffb02302ef4999e432c010f29b5fac05bd541bb702e47d8c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-304
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7527ad08d72604e2dfbb18dd8de811105005a6961a47c93d94c6f25977efc56c
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ***********3
      podIPs:
      - ip: ***********3
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-305
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://656fa8f7db4d11ae634f08398cfad0a4a449b4763f2e6da0e1d0cebee774dbd9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-306
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://9723b78192ed87d358f1a74b15dd4c46650861cd7712dd631b0bfc91b74f78b6
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-307
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://bb093f69abcdca09e20eae6a2be3974641327bc8ffcab47fb3635b34692eba7b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************5
      podIPs:
      - ip: ************5
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-308
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7e5ccb08b291160f5b0bcdadf3f877c072dee1ff98b3a529ebd7fc3698eb0db9
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-309
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e7315666ffeb42e71d55925bc87d39590d566f377c155e126a874a3f99a5218b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:08Z"
      hostIP: *********10
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-310
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://bd74cb24fd26643fae96a13f39ea7d19ef5a21966c15dc46af674c31b6cd9a15
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-311
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6d2b673695a09b13c44bfeb8eafc053a11c5e066fe7a9aba741171801cb00974
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-312
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://e15cbad21315b1f6f0a5334b36a4ee9cec243db4518d9da6cc0cdfc9d762ac44
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-313
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://35647a6aad45aa0157d0768738eff862911bb88d9b88f7afc183b7b2f8b177dc
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-314
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:08Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://3397be96ac9d37993062f6e0200dfbb6227b352f0c24e6692d54d39eaa2c8db2
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:07Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-315
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d6451c5b2ea45e74ed67642e4160ea8e6c1f80c3089055bf97c21f270ee7c461
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-316
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://75a45382f3db4a004aeb469549ef355604624b4ddedd97fcf0c2e8579bc3789b
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:09Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-317
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://77d35f98ef51a68bc9961ec10d992629851d6619a780edb8a8eac5a44279cc9e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-318
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://bc5d22842779ea47522ed515e1b74183562b5b3c37bb6f61d5466783c3a10a49
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-319
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:00Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1649da590291b923f537240ea05b18ad797f73076a8f6a5590b52ad036986713
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:08Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:00Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-320
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://cdc3febca6be137c8310a96cbce95d52dbb1c3036dd95ca3457588add2ea9ef7
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-321
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://1efcf53c9ccb53edc86baba30ca8337df7bab115c4c9e9fc718e027cfd62bce7
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-322
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7a3c75a03446f79eae14e94d98fbfb892dac518ca9995932de0485e575881d42
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-323
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:10Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://3d2ada381eee9ccc875c5044d47163f8bce0d5dc797fc57f966d504858723bed
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:09Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-324
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://58cd438e21da29f6f98193e545a78bb86ea56f9fa7d4035fa505abd2c091cf82
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-325
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://cebe5d12e078c320b7d218f457db054b54ffeb53992dc75475f2c51da6aab9d3
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-326
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://6a766b19cf9830c9a069ae4810152f200d8a33eff543c61a63ac468380a05c16
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-327
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://4f596ebc57926d4779fc1060143f9bced412b9e39d7ed576309a31b6fbeee7d5
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: **********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-328
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://aa61a3840509da124505371fd86a6bb3147e6ee8b79b9494ccb06a8d149712c2
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********7
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-329
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7d5fdd73bb9c937683874e62cf65dad6bcd2e316d09f0c79a19f8a54378914fd
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********06
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-330
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://ecda087033d80b93fd2c238de0f8b5ac7b553e6f5ee52665de267eae6a7159f3
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ************9
      podIPs:
      - ip: ************9
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-331
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:59Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://205bfb8b716775ad24adf6177b1797c1f53fd382b5cec9c441099c25b7e2074e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********1
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:59Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-332
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:06Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://f626b99e73e0b4dd0fe1469293739269ee31ccc06f2507d8023e6e73bb3530af
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********2
      phase: MasterExitKilled
      podIP: ************
      podIPs:
      - ip: ************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-333
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://d33f1d584204f72a50fe7eedce04a458a5e6acd8f461503d3b668dc4c5d0c81e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********2
      phase: MasterExitKilled
      podIP: ***********2
      podIPs:
      - ip: ***********2
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-334
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:03Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:11Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://3068f30035099f14d69cfb61b3006c4e4155d035d922fed5e40ea67e0da7224f
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:10Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: ***********
      podIPs:
      - ip: ***********
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:03Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-335
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:09:58Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:07Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://7adca4a1a48fd998ac8ab7a9c967853050181ff8243dc6e85505dc66104b4ddc
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:06Z"
      hostIP: *********8
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:09:58Z"
  - name: job-nfc9y8fjev-baidu-0711-worker-336
    status:
      conditions:
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: PodReadyToStartContainers
      - lastTransitionTime: "2025-07-15T05:10:01Z"
        status: "True"
        type: Initialized
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: Ready
      - lastTransitionTime: "2025-07-15T05:10:09Z"
        status: "True"
        type: ContainersReady
      - lastTransitionTime: "2025-07-15T05:09:55Z"
        status: "True"
        type: PodScheduled
      containerStatuses:
      - containerID: containerd://4c6fd8e72474f1f01f2cbf351180b613e64df90f1476eb8d028c3ad9d15d996e
        image: registry.baidubce.com/ai-native-dev/aixl:latest
        imageID: registry.baidubce.com/ai-native-dev/aixl@sha256:4efb82f96d8502b1368878b6b63e63cb224a5ab2c0827d940e485b06e9d9faec
        lastState: {}
        name: deepspeed
        ready: true
        restartCount: 0
        started: true
        state:
          running:
            startedAt: "2025-07-15T05:10:08Z"
      hostIP: ***********
      phase: MasterExitKilled
      podIP: *************
      podIPs:
      - ip: *************
      qosClass: BestEffort
      startTime: "2025-07-15T05:10:01Z"
  replicaStatuses:
    launcher:
      failed: 1
    worker:
      active: 337
  startTime: "2025-07-15T05:09:53Z"
